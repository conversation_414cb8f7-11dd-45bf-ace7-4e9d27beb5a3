// ============================================
// 基础颜色变量 (保持兼容性)
// ============================================
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$text-color: #333333;
$text-color-secondary: #666666;
$background-color: #f5f5f5;
$border-color: #e8e8e8;

// ============================================
// 深色主题颜色变量
// ============================================

// 功能颜色 - 深色主题
$primary-color-dark: #0091FF;
$success-color-dark: #00ff00;
$error-color-dark: #ff0000;
$warning-color-dark: #ffaa00;

// 基础颜色
$white: #ffffff;
$black: #000000;

// 背景颜色 - 深色主题
$background-primary-dark: #272531;
$background-secondary-dark: #3d3a46;
$background-tertiary-dark: #1A191F;

// 文字颜色 - 深色主题
$text-primary-dark: #ffffff;
$text-secondary-dark: #cccccc;
$text-inverse-dark: #000000;

// 边框颜色 - 深色主题
$border-primary-dark: #ffffff;
$border-secondary-dark: #cccccc;
$border-light-dark: rgba(255, 255, 255, 0.3);

// 透明度颜色 - 深色主题
$opacity-white-10: rgba(255, 255, 255, 0.1);
$opacity-white-20: rgba(255, 255, 255, 0.2);
$opacity-white-30: rgba(255, 255, 255, 0.3);
$opacity-white-50: rgba(255, 255, 255, 0.5);
$opacity-white-70: rgba(255, 255, 255, 0.7);

// ============================================
// 骨骼检测专用颜色变量
// ============================================

// 骨骼点颜色
$skeleton-point-default: #00ff00;
$skeleton-point-selected: #ff0000;
$skeleton-point-hover: #ffffff;
$skeleton-point-red: #ff0000;
$skeleton-point-green: #00ff00;

// 骨骼线条颜色
$skeleton-line-default: #ff00ff;
$skeleton-line-selected: #ffff00;

// 边界框颜色
$skeleton-bbox-default: #00ffff;
$skeleton-bbox-selected: #ff0000;

// 字体变量
$font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-size-base: 14px;
$font-size-sm: 12px;
$font-size-lg: 16px;
$font-size-xl: 20px;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 边框变量
$border-radius-base: 4px;
$border-radius-sm: 2px;
$border-radius-lg: 8px;

// 断点变量
$screen-xs: 480px;
$screen-sm: 576px;
$screen-md: 768px;
$screen-lg: 992px;
$screen-xl: 1200px;
$screen-xxl: 1600px;

// 动画变量
$animation-duration-base: 0.2s;
$animation-duration-slow: 0.3s;
$animation-duration-fast: 0.1s;

// 阴影变量
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);

// ============================================
// 页面公共样式变量
// ============================================

// 项目专用字体
$font-family-primary: SourceHanSerifSC, SourceHanSerifSC;

// 项目专用颜色
$gradient-primary: linear-gradient(to bottom, #0091FF 0%, #B620E0 100%);

// 分页样式变量
$pagination-bg: rgba(255, 255, 255, 0.1);
$pagination-border: rgba(255, 255, 255, 0.2);
$pagination-hover-bg: rgba(255, 255, 255, 0.2);
$pagination-hover-border: rgba(255, 255, 255, 0.4);
$pagination-disabled-bg: rgba(255, 255, 255, 0.05);
$pagination-disabled-border: rgba(255, 255, 255, 0.1);
$pagination-disabled-text: rgba(255, 255, 255, 0.3);
