/**
 * 路由辅助函数
 */
export function useRouterHelpers() {
	const router = useRouter();
	const route = useRoute();

	/**
	 * 设置文档标题
	 */
	function setDocumentTitle(title: string) {
		if (import.meta.client) {
			document.title = `${title || route.meta.title || '灵宇科技'}`;
		}
	}

	/**
	 * 检查用户是否已认证
	 */
	function isAuthenticated() {
		const authStatus = useCookie('auth_status');
		return authStatus.value === 'authenticated';
	}

	/**
	 * 重定向到登录页
	 */
	function redirectToLogin(redirectPath?: string) {
		return navigateTo({
			path: '/login',
			query: redirectPath ? { redirect: redirectPath } : undefined,
		});
	}

	/**
	 * 重定向到首页
	 */
	function redirectToHome() {
		return navigateTo('/home');
	}

	/**
	 * 返回上一页
	 */
	function goBack() {
		router.back();
	}

	return {
		setDocumentTitle,
		isAuthenticated,
		redirectToLogin,
		redirectToHome,
		goBack,
	};
}
