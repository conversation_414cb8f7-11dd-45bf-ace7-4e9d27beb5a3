import { useAuthStore } from '~/stores/auth';

/**
 * 全局中间件 - 会应用于所有路由
 */
export default defineNuxtRouteMiddleware((to, from) => {
	//获取认证状态
	const authStore = useAuthStore();
	const isAuthenticated = authStore.isAuthenticated;

	// 只在客户端设置标题
	if (import.meta.client) {
		document.title = `${to.meta.title || '灵宇科技'}`;
	}

	// 需要登录但未登录，重定向到登录页
	if (to.meta.requiresAuth && !isAuthenticated) {
		return navigateTo({
			path: '/auth',
			query: { redirect: to.fullPath },
		});
	}
	// 仅限游客但已登录，重定向到首页
	else if (to.meta.guestOnly && isAuthenticated) {
		return navigateTo('/home');
	}
});
