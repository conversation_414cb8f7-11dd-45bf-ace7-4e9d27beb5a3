import io
import os
import shutil
import zipfile

from models.user_model import UserVideos
from service.dbRedis.redisClient import get_client
from service.mysql_client import DBSessionContext
from settings import VIDEO_UPLOAD_FOLDER, BP_UPLOAD_FOLDER
from utils.exceptions import VideoError
from utils.publicMethod import allowed_file, getuu_id
from sqlalchemy import desc, cast, DateTime


def upload_video(user_id, file):
    if not allowed_file(file.filename):
        raise VideoError(VideoError.INVALID_FILE_NAME)

    filename = generate_video_filename(file.filename)
    save_path = os.path.join(VIDEO_UPLOAD_FOLDER, filename)
    file.save(save_path)

    with DBSessionContext() as db_session:
        new_user_video = UserVideos(user_id=user_id, filename=filename)
        db_session.add(new_user_video)
        return {
            "filename": filename
        }


def get_video_list(user_id, start_page, page_size):
    with DBSessionContext() as db_session:
        # 计算总数
        total = db_session.query(UserVideos).filter(
            UserVideos.user_id == user_id
        ).count()
        
        # 获取分页数据
        videos = db_session.query(UserVideos).filter(
            UserVideos.user_id == user_id
        ).order_by(
            desc(cast(UserVideos.created_at, DateTime))
        ).offset(
            (start_page - 1) * page_size
        ).limit(
            page_size
        ).all()
        
        return {
            "total": total,
            "videoList": [{
                "videoId": video.id,
                "filename": video.filename,
                "bpFilename": video.bp_filename,
                "createdAt": video.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updatedAt": video.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                "status": video.status,
            } for video in videos]
        }


def get_video_path(user_id, video_id):
    with DBSessionContext() as db_session:
        video = db_session.query(UserVideos).filter(
            UserVideos.id == video_id,
            UserVideos.user_id == user_id
        ).first()
        
        if not video:
            raise VideoError(VideoError.VIDEO_NOT_FOUND)
            
        file_path = os.path.join(VIDEO_UPLOAD_FOLDER, video.filename)
        
        if not os.path.exists(file_path):
            raise VideoError(VideoError.VIDEO_FILE_MISSING)
            
        return file_path


def get_bp_path(user_id, video_id):
    with DBSessionContext() as db_session:
        video = db_session.query(UserVideos).filter(
            UserVideos.id == video_id,
            UserVideos.user_id == user_id
        ).first()
        
        if not video:
            raise VideoError(VideoError.VIDEO_NOT_FOUND)
            
        bp_filename = f"{os.path.splitext(video.filename)[0]}_bp.json"
        bp_path = os.path.join(BP_UPLOAD_FOLDER, bp_filename)
        
        if not os.path.exists(bp_path):
            raise VideoError(VideoError.BP_FILE_MISSING)
            
        return bp_path


def get_video_all(user_id, video_id):
    # 获取视频和骨骼点文件路径
    video_path = get_video_path(user_id, video_id)
    bp_path = get_bp_path(user_id, video_id)

    # 创建内存中的zip文件
    memory_file = io.BytesIO()
    with zipfile.ZipFile(memory_file, "w", zipfile.ZIP_DEFLATED) as zf:
        # 添加视频文件
        zf.write(str(video_path), os.path.basename(video_path))
        # 添加骨骼点文件
        zf.write(str(bp_path), os.path.basename(bp_path))

    # 重置文件指针
    memory_file.seek(0)
    return memory_file


def generate_video_filename(filename):
    ext = filename.rsplit(".", 1)[1].lower()
    return f"{getuu_id()}.{ext}"


def save_chunk(user_id, file_id, chunk, chunk_number, total_chunks):
    # 创建临时目录
    temp_dir = os.path.join(VIDEO_UPLOAD_FOLDER, "temp", file_id)
    os.makedirs(temp_dir, exist_ok=True)
    
    # 保存分块
    chunk_path = os.path.join(str(temp_dir), f"chunk_{chunk_number}")
    chunk.save(chunk_path)
    
    # 更新Redis中的上传进度
    redis_key = f"upload:{user_id}:{file_id}"
    redis_client = get_client()
    redis_client.hset(redis_key, str(chunk_number), "1")
    
    # 计算已完成分块数
    uploaded_chunks = len(redis_client.hkeys(redis_key))
    progress = int((uploaded_chunks / total_chunks) * 100)
    
    return {
        "progress": progress,
        "uploadedChunks": uploaded_chunks,
        "totalChunks": total_chunks
    }


def merge_chunks(user_id, file_id, filename):
    if not allowed_file(filename):
        raise VideoError(VideoError.INVALID_FILE_NAME)
    
    temp_dir = os.path.join(VIDEO_UPLOAD_FOLDER, "temp", file_id)
    final_path = os.path.join(VIDEO_UPLOAD_FOLDER, filename)
    
    # 合并所有分块
    with open(final_path, "wb") as outfile:
        for chunk_num in sorted(os.listdir(str(temp_dir))):
            chunk_path = os.path.join(str(temp_dir), chunk_num)
            with open(chunk_path, "rb") as infile:
                outfile.write(infile.read())
    
    # 清理临时文件
    shutil.rmtree(temp_dir)
    
    # 保存到数据库
    with DBSessionContext() as db_session:
        new_user_video = UserVideos(user_id=user_id, filename=filename)
        db_session.add(new_user_video)
    
    return {"filename": filename}


def get_upload_status(user_id, file_id):
    redis_key = f"upload:{user_id}:{file_id}"
    redis_client = get_client()
    
    # 获取已上传的分片信息
    uploaded_chunks = redis_client.hkeys(redis_key)
    
    # 检查是否已完成上传
    temp_dir = os.path.join(VIDEO_UPLOAD_FOLDER, "temp", file_id)
    if os.path.exists(temp_dir):
        total_chunks = len(os.listdir(str(temp_dir)))
    else:
        total_chunks = 0
    
    # 计算进度
    progress = 0
    if total_chunks > 0:
        progress = int((len(uploaded_chunks) / total_chunks) * 100)
    
    return {
        "progress": progress,
        "uploadedChunks": len(uploaded_chunks),
        "totalChunks": total_chunks,
        "status": "uploading" if progress < 100 else "completed"
    }