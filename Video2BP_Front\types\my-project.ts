export interface ProjectListParams {
	pageNum: number;
	pageSize: number;
}

export interface ProjectItem {
	id: number;
	name: string;
	image: string;
	fbxUrl?: string;
	videoUrl?: string;
	createdAt?: string;
	updatedAt?: string;
	status?: string;
}

export interface ProjectListData {
	data: ProjectItem[];
	total: number;
	pageNum: number;
	pageSize: number;
}

export interface ProjectListResponse {
	code: number;
	message: string;
	data: ProjectListData;
}
