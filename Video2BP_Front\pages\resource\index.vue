<template>
  <div class="resource-page">
    <div class="resource-content">
      <div class="ec-head">
        <div class="ch-left">
          <div class="hl-title">资源广场</div>
          <div class="hl-zm">Resource Square</div>
        </div>
        <div class="ec-right">
          <div class="cr-input">
            <input v-model="searchKeyword" placeholder="搜索" type="text" @keyup.enter="handleSearch" />
          </div>
          <div class="cr-btn" @click="handleSearch">
            <SearchOutlined />
          </div>
        </div>
      </div>
      <div class="ec-body">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <Loading overlay size="large" text="加载中..." />
        </div>

        <!-- 错误状态 -->
        <ErrorState v-else-if="error" :message="error" @retry="retryFetch" />

        <!-- 资源列表 -->
        <div v-else class="resource-content">
          <div class="cb-items">
            <div class="item" v-for="item in resourceList" :key="item.id">
              <div class="item-image">
                <img :src="item.image" :alt="item.name" />
              </div>
              <div class="item-name">
                <span>{{ item.name }}</span>
                <div class="download-icon" @click="handleDownload(item)">
                  <DownloadOutlined />
                </div>
              </div>
            </div>
          </div>

          <!-- 分页组件 -->
          <PaginationWrapper v-if="!loading && !error" :total="total" :current="currentPage" :page-size="pageSize"
            @change="handlePageChange" />
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
import Loading from '~/components/common/Loading.vue'
import ErrorState from '~/components/common/ErrorState.vue'
import PaginationWrapper from '~/components/common/PaginationWrapper.vue'
import type { ResourceItem } from '~/types/resource'
import { useApi } from '~/composables/useApi'

defineOptions({
  name: 'ResourceView',
})

definePageMeta({
  title: '资源广场',
  description: '',
  layout: 'default',
})

// 获取API服务实例
const { resource } = useApi()

// API 分页数据管理
const resourceList = ref<ResourceItem[]>([]) // 当前页面的资源列表
const currentPage = ref(1) // 当前页码
const pageSize = ref(12) // 每页显示12个资源
const total = ref(0) // 总记录数，从 API 返回
const loading = ref(true) // 加载状态
const error = ref<string | null>(null) // 错误信息

// 搜索相关状态
const searchKeyword = ref('')

// API 调用函数
const fetchResourceList = async (page: number = 1, size: number = 12, keyword: string = '') => {
  try {
    loading.value = true
    error.value = null

    const result = await resource?.getResourceList({
      pageNum: page,
      pageSize: size,
      keyword: keyword.trim() || undefined,
    })

    if (!result) {
      throw new Error('请求失败')
    }

    const { data, total: totalCount, pageNum, pageSize: responsePageSize } = result.data
    resourceList.value = data
    total.value = totalCount
    currentPage.value = pageNum
    pageSize.value = responsePageSize

    console.log('获取资源列表成功:', result)

  } catch (err) {
    console.error('获取资源列表失败:', err)
    error.value = err as string
  } finally {
    loading.value = false
  }
}

// 分页变化处理函数
const handlePageChange = async (page: number) => {
  await fetchResourceList(page, pageSize.value, searchKeyword.value)
}

// 重试函数
const retryFetch = async () => {
  await fetchResourceList(currentPage.value, pageSize.value, searchKeyword.value)
}

// 搜索处理函数
const handleSearch = async () => {
  // 搜索时重置到第一页
  await fetchResourceList(1, pageSize.value, searchKeyword.value)
}

// 下载处理函数
const handleDownload = (item: ResourceItem) => {
  console.log('下载资源:', item.name)

  if (item.downloadUrl) {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = item.downloadUrl
    link.download = item.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 页面初始化
onMounted(() => {
  fetchResourceList(1, pageSize.value)
})

</script>

<style lang="scss" scoped>
.resource-page {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-image: url('~/assets/images/upgrade-bg.webp');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;

  .resource-content {
    min-height: calc(100vh - rem(403));
    padding: rem(171) rem(120) rem(120);

    .ec-head {
      display: flex;
      justify-content: space-between;
      margin-bottom: rem(46);

      .ch-left {
        display: flex;
        flex-direction: column;

        .hl-title {
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: bold;
          font-size: rem(48);
          color: #FFFFFF;
          line-height: rem(69);
          text-align: left;
          font-style: normal;
          margin-bottom: rem(12);
        }

        .hl-zm {
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: bold;
          font-size: rem(14);
          line-height: rem(20);
          letter-spacing: rem(11);
          text-align: left;
          font-style: normal;
          text-transform: uppercase;
          background: linear-gradient(to bottom, #0091FF 0%, #B620E0 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }
      }

      .ec-right {
        display: flex;
        justify-content: space-between;
        width: rem(400);
        height: rem(72);
        padding: rem(19) rem(24);
        border: rem(1) solid rgba(255, 255, 255, 0.2);
        border-radius: rem(4);

        .cr-input {
          flex: 1;
          height: 100%;
          background: transparent;

          input {
            width: 100%;
            height: 100%;
            background: transparent;
            border: none;
            outline: none;
            color: #ffffff;
            font-family: SourceHanSerifSC, SourceHanSerifSC;
            font-weight: 400;
            font-size: rem(24);
            color: #fff;
            line-height: rem(32);
            text-align: left;
            font-style: normal;

            &::placeholder {
              color: #fff;
              font-size: rem(24);
              font-family: SourceHanSerifSC, SourceHanSerifSC;
            }
          }
        }

        .cr-btn {
          width: rem(24);
          height: rem(24);
          font-size: rem(24);
        }
      }
    }

    .ec-body {
      flex: 1;
      display: flex;
      flex-direction: column;

      .loading-container {
        @include loading-container;
      }



      .resource-content {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .cb-items {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: rem(49) rem(24);
        background: transparent;

        .item {
          position: relative;
          border-radius: rem(8);
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;


          &:hover {
            transform: translateY(-5px);
            box-shadow: 0 rem(10) rem(30) rgba(0, 0, 0, 0.3);

            .item-overlay {
              opacity: 1;
            }
          }

          .item-image {
            position: relative;
            width: 100%;
            height: rem(226);
            overflow: hidden;
            margin-bottom: rem(23);

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }
          }

          .item-name {
            display: flex;
            justify-content: space-between;
            align-items: center;

            span {
              font-family: SourceHanSerifSC, SourceHanSerifSC;
              font-weight: bold;
              font-size: rem(24);
              color: #FFFFFF;
              font-style: normal;
            }

            .download-icon {
              font-size: rem(24);
              color: #FFFFFF;
              cursor: pointer;
            }
          }
        }
      }


    }
  }
}
</style>
