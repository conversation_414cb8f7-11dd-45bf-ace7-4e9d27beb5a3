import traceback
from .R import R
from .exceptions import BusinessError, ServerError
from loguru import logger

def init_error_handlers(app):
    # @app.errorhandler(MysqlError)
    # def handle_mongo_error(e):
    #     tb = traceback.extract_tb(e.__traceback__)
    #     function_name = tb[-1].name if tb else "unknown"
    #     logger.error(f"{function_name}:操作mysql异常,{e.__dict__},traceback={traceback.format_exc()}")
    #     return R.error(e)

    @app.errorhandler(BusinessError)
    def handle_business_error(e):
        tb = traceback.extract_tb(e.__traceback__)
        function_name = tb[-1].name if tb else "unknown"
        if e.data:
            logger.error(f"{function_name}:业务异常:{e.msg},数据是:{e.data},traceback={traceback.format_exc()}")
        else:
            logger.error(f"{function_name}:业务异常:{e.msg},traceback={traceback.format_exc()}")
        return R.error(e)

    @app.errorhandler(Exception)
    def handle_general_error(e):
        tb = traceback.extract_tb(e.__traceback__)
        function_name = tb[-1].name if tb else "unknown"
        logger.error(f"{function_name}:未知异常:{str(e)},traceback={traceback.format_exc()}")
        return R.error(ServerError.OPERATION_ERROR, data={"errDetails": str(e)})