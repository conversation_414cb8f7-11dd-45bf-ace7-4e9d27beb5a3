import { ref, computed } from 'vue';
import type { Ref } from 'vue';
import type { SkeletonFrame, Keypoint, BoundingBox } from '~/types/repairBone';

export function useSkeletonAnimation(
	skeletonData: Ref<SkeletonFrame[]>,
	canvasWidth: Ref<number>,
	canvasHeight: Ref<number>,
	frameRate: Ref<number> = ref(30) // 添加帧率参数，默认为30fps
) {
	const currentFrame = ref(0);
	const isPlaying = ref(false);
	const playbackSpeed = ref(1);
	const totalFrames = computed(() => skeletonData.value.length);
	let animationInterval: number | null = null;

	const currentSkeletonData = computed(() => {
		if (!skeletonData.value.length) return null;
		return skeletonData.value[currentFrame.value];
	});

	// 计算图像比例尺 - 原始图像尺寸与画布尺寸的比例
	const scaleFactors = computed(() => {
		if (!currentSkeletonData.value)
			return { x: 1, y: 1, xOffset: 0, yOffset: 0 };

		const [imageHeight, imageWidth] = currentSkeletonData.value.image_shape;

		// 如果采用object-fit: contain的方式显示图像，需要计算实际绘制区域的尺寸
		// 计算等比例缩放后的实际绘制尺寸
		const imageRatio = imageWidth / imageHeight;
		const canvasRatio = canvasWidth.value / canvasHeight.value;

		let renderWidth = canvasWidth.value;
		let renderHeight = canvasHeight.value;

		// 根据图像比例调整实际绘制区域
		if (imageRatio > canvasRatio) {
			// 图像比画布更宽，宽度填满画布
			renderWidth = canvasWidth.value;
			renderHeight = renderWidth / imageRatio;
		} else {
			// 图像比画布更高，高度填满画布
			renderHeight = canvasHeight.value;
			renderWidth = renderHeight * imageRatio;
		}

		// 计算缩放因子
		const xScale = renderWidth / imageWidth;
		const yScale = renderHeight / imageHeight;

		// 计算居中偏移
		const xOffset = (canvasWidth.value - renderWidth) / 2;
		const yOffset = (canvasHeight.value - renderHeight) / 2;

		return {
			x: xScale,
			y: yScale,
			xOffset,
			yOffset,
		};
	});

	const formattedKeypoints = computed(() => {
		if (!currentSkeletonData.value) return [];

		const { x: xScale, y: yScale, xOffset, yOffset } = scaleFactors.value;

		return currentSkeletonData.value.instance_info.map((instance) => {
			return instance.keypoints.map(([x, y]) => ({
				// 应用比例尺和偏移，将原始坐标转换为画布坐标
				x: x * xScale + xOffset,
				y: y * yScale + yOffset,
			}));
		});
	});

	// 计算边界框数据
	const formattedBoundingBoxes = computed(() => {
		if (!currentSkeletonData.value) return [];

		const { x: xScale, y: yScale, xOffset, yOffset } = scaleFactors.value;

		return currentSkeletonData.value.instance_info
			.map((instance) => {
				// 检查实例是否有边界框数据
				if (!instance.bbox || instance.bbox.length === 0) {
					return null;
				}

				// 获取第一个边界框
				const [x1, y1, x2, y2] = instance.bbox[0];

				// 计算边界框尺寸
				const width = x2 - x1;
				const height = y2 - y1;

				// 应用缩放和偏移
				return {
					x: x1 * xScale + xOffset,
					y: y1 * yScale + yOffset,
					width: width * xScale,
					height: height * yScale,
					score: instance.bbox_score || 1.0,
				};
			})
			.filter((box) => box !== null) as BoundingBox[];
	});

	const play = () => {
		if (isPlaying.value) return;
		isPlaying.value = true;

		// 使用传入的帧率参数
		animationInterval = window.setInterval(
			() => {
				nextFrame();
			},
			1000 / (frameRate.value * playbackSpeed.value)
		);
	};

	const pause = () => {
		isPlaying.value = false;
		if (animationInterval) {
			clearInterval(animationInterval);
			animationInterval = null;
		}
	};

	const nextFrame = () => {
		if (currentFrame.value >= totalFrames.value - 1) {
			currentFrame.value = 0;
		} else {
			currentFrame.value++;
		}
	};

	const previousFrame = () => {
		if (currentFrame.value <= 0) {
			currentFrame.value = totalFrames.value - 1;
		} else {
			currentFrame.value--;
		}
	};

	const goToFrame = (frame: number) => {
		if (frame >= 0 && frame < totalFrames.value) {
			currentFrame.value = frame;
		}
	};

	const setPlaybackSpeed = (speed: number) => {
		playbackSpeed.value = speed;
		if (isPlaying.value && animationInterval) {
			clearInterval(animationInterval);
			animationInterval = window.setInterval(
				() => {
					nextFrame();
				},
				1000 / (frameRate.value * playbackSpeed.value)
			);
		}
	};

	const cleanup = () => {
		if (animationInterval) {
			clearInterval(animationInterval);
		}
	};

	return {
		currentFrame,
		isPlaying,
		playbackSpeed,
		totalFrames,
		currentSkeletonData,
		formattedKeypoints,
		formattedBoundingBoxes,
		scaleFactors,
		play,
		pause,
		nextFrame,
		previousFrame,
		goToFrame,
		setPlaybackSpeed,
		cleanup,
	};
}

export type { Keypoint, BoundingBox, SkeletonFrame };
