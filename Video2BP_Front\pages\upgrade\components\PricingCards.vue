<template>
  <div class="pricing-cards">
    <div class="card" v-for="(plan, index) in activePlans" :key="index">
      <div class="card-title">{{ plan.title }}</div>
      <div class="card-price">{{ plan.price }}</div>
      <div class="card-button" :class="{ current: plan.isCurrent }">
        {{ plan.buttonText }}
      </div>
      <div class="features">
        <div class="feature" v-for="(feature, fIndex) in plan.features" :key="fIndex">
          <span class="check"><img src="~/assets/images/upgrade-check.webp" alt="" /></span>
          <span class="feature-name">{{ feature.name }}</span>
          <span class="feature-value" v-html="feature.value"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PlanType, PlanConfig, PriceConfig } from '../types'

defineOptions({
  name: 'PricingCards',
})

const props = defineProps({
  planType: {
    type: String as () => PlanType,
    default: 'year',
  },
})

const planConfigs: PlanConfig[] = [
  {
    id: 'free',
    title: '免费版',
    isCurrent: true,
    buttonText: '当前版本',
    features: [
      { name: '赠送V币', valueMap: { year: '50V币', month: '50V币' } },
      { name: '视频排队制作顺序', valueMap: { year: '低优先级', month: '低优先级' } },
      { name: '视频长度上限', valueMap: { year: '30秒', month: '30秒' } },
      { name: '资源广场下载限制', valueMap: { year: '30秒', month: '30秒' } },
      { name: '视频排队数量限制', valueMap: { year: '2个', month: '2个' } },
      { name: '单个文件大小限制', valueMap: { year: '100M', month: '100M' } },
      { name: '支持格式', valueMap: { year: 'FBX', month: 'FBX' } },
      { name: '视频剪辑', valueMap: { year: '', month: '' } },
      { name: '物理优化', valueMap: { year: '', month: '' } },
    ],
  },
  {
    id: 'economy',
    title: '经济型',
    isCurrent: false,
    buttonText: '购买',
    features: [
      { name: '赠送V币', valueMap: { year: '4500V币', month: '450V币' } },
      { name: '视频排队制作顺序', valueMap: { year: '高优先级', month: '高优先级' } },
      { name: '视频长度上限', valueMap: { year: '120秒', month: '120秒' } },
      { name: '资源广场下载限制', valueMap: { year: '120秒', month: '120秒' } },
      { name: '视频排队数量', valueMap: { year: '10个', month: '10个' } },
      { name: '单个文件大小限制', valueMap: { year: '400M', month: '400M' } },
      {
        name: '支持格式',
        valueMap: {
          year: 'FBX、Unreal、Mixamo<br>BIP、VMD、Only face<br>Unity Anim、C4D、CC&iClone',
          month: 'FBX、Unreal、Mixamo<br>BIP、VMD、Only face<br>Unity Anim、C4D、CC&iClone',
        },
      },
      { name: '视频剪辑', valueMap: { year: '', month: '' } },
      { name: '物理优化', valueMap: { year: '', month: '' } },
      { name: '2D修复功能', valueMap: { year: '', month: '' } },
      { name: '收藏夹功能', valueMap: { year: '', month: '' } },
    ],
  },
  {
    id: 'premium',
    title: '高级版',
    isCurrent: false,
    buttonText: '购买',
    features: [
      { name: '赠送V币', valueMap: { year: '16000V币', month: '1600V币' } },
      { name: '视频排队制作顺序', valueMap: { year: '高优先级', month: '高优先级' } },
      { name: '视频长度上限', valueMap: { year: '120秒', month: '120秒' } },
      { name: '资源广场下载限制', valueMap: { year: '120秒', month: '120秒' } },
      { name: '视频排队数量', valueMap: { year: '10个', month: '10个' } },
      { name: '文件上传大小限制', valueMap: { year: '400M', month: '400M' } },
      {
        name: '支持格式',
        valueMap: {
          year: 'FBX、Unreal、Mixamo<br>BIP、VMD、Only face<br>Unity Anim、C4D、CC&iClone',
          month: 'FBX、Unreal、Mixamo<br>BIP、VMD、Only face<br>Unity Anim、C4D、CC&iClone',
        },
      },
      { name: '视频剪辑', valueMap: { year: '', month: '' } },
      { name: '物理优化', valueMap: { year: '', month: '' } },
      { name: '2D修复功能', valueMap: { year: '', month: '' } },
      { name: '收藏夹功能', valueMap: { year: '', month: '' } },
    ],
  },
]

// Price configuration for different plan types
const priceConfig: PriceConfig = {
  free: { year: '¥0.00', month: '¥0.00' },
  economy: { year: '¥699.00', month: '¥69.00' },
  premium: { year: '¥1299.00', month: '¥129.00' },
}

// Generate the active plans based on the current plan type
const activePlans = computed(() => {
  return planConfigs.map((plan) => {
    // Get the price for this plan and plan type
    const price = priceConfig[plan.id][props.planType as PlanType]

    // Transform the features to have the correct values for the current plan type
    const features = plan.features.map((feature) => ({
      name: feature.name,
      value: feature.valueMap[props.planType as PlanType],
    }))

    return {
      ...plan,
      price,
      features,
    }
  })
})
</script>

<style lang="scss" scoped>
.pricing-cards {
  display: flex;
  justify-content: center;
  gap: rem(24);
  width: 100%;

  .card {
    flex: 1;
    background-color: rgba(29, 25, 52, 0.8);
    // border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: rem(8);
    padding: rem(45) rem(48) rem(82);
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    border: 2px solid transparent;
    border-image: linear-gradient(to bottom,
        rgba(255, 255, 255, 0.3) 0%,
        /* 顶部可见 */
        rgba(255, 255, 255, 0.2) 30%,
        /* 逐渐变淡 */
        rgba(255, 255, 255, 0.1) 60%,
        /* 更淡 */
        transparent 100%
        /* 底部完全透明 */
      ) 1;

    .card-title {
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: bold;
      font-size: rem(36);
      color: #ffffff;
      line-height: rem(38);
      font-style: normal;
      text-align: center;
      margin-bottom: rem(22);
    }

    .card-price {
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: bold;
      font-size: rem(56);
      color: #ffffff;
      line-height: rem(80);
      font-style: normal;
      text-align: center;
      margin-bottom: rem(35);
    }

    .card-button {
      width: 100%;
      height: rem(64);

      background-color: rgba(255, 255, 255, 0.1);
      border-radius: rem(4);
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: bold;
      font-size: rem(24);
      color: #ffffff;
      font-style: normal;
      margin-bottom: rem(30);
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
      }

      &.current {
        cursor: default;
        background-color: #0099ff;

        &:hover {
          background-color: #00aaff;
        }
      }
    }

    .features {
      width: 100%;

      .feature {
        display: flex;
        align-items: flex-start;
        margin-bottom: rem(16);

        &:last-child {
          margin-bottom: 0;
        }

        .check {
          width: rem(20);
          height: rem(20);
          padding-top: rem(2);
          margin-right: rem(2);

          img {
            width: 100%;
            height: 100%;
          }
        }

        .feature-name {
          color: rgba(255, 255, 255, 0.8);
          flex: 1;
        }

        .feature-value {
          color: #ffffff;
          text-align: right;
          flex-shrink: 0;
          min-width: rem(80);
        }
      }
    }
  }
}

@media (max-width: rem(1200)) {
  .pricing-cards {
    flex-direction: column;
    align-items: center;

    .card {
      width: 100%;
      max-width: rem(500);
      margin-bottom: rem(20);
    }
  }
}
</style>
