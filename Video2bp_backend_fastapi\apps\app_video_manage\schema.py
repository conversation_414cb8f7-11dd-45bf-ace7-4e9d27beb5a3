from typing import Optional, List
from pydantic import BaseModel, Field
from schemas.base import BaseSchema
from schemas.response import StandardResponse


class VideoUploadResponse(BaseSchema):
    """视频上传响应"""
    filename: str = Field(..., description="文件名")


class VideoUploadResponseModel(StandardResponse):
    """视频上传响应模型"""
    data: Optional[VideoUploadResponse] = None


class VideoListRequest(BaseSchema):
    """视频列表请求"""
    startPage: int = Field(1, description="起始页", alias="start_page")
    pageSize: int = Field(10, description="页面大小", alias="page_size")


class VideoItem(BaseSchema):
    """视频项"""
    videoId: int = Field(..., description="视频ID", alias="video_id")
    filename: str = Field(..., description="文件名")
    bpFilename: Optional[str] = Field(None, description="骨骼点文件名", alias="bp_filename")
    createdAt: str = Field(..., description="创建时间", alias="created_at")
    updatedAt: str = Field(..., description="更新时间", alias="updated_at")
    status: int = Field(..., description="状态")


class VideoListResponse(BaseSchema):
    """视频列表响应"""
    total: int = Field(..., description="总数")
    videoList: List[VideoItem] = Field(..., description="视频列表", alias="video_list")


class VideoListResponseModel(StandardResponse):
    """视频列表响应模型"""
    data: Optional[VideoListResponse] = None


class ChunkUploadRequest(BaseSchema):
    """分片上传请求"""
    chunkNumber: int = Field(..., description="分片编号", alias="chunk_number")
    totalChunks: int = Field(..., description="总分片数", alias="total_chunks")
    fileId: str = Field(..., description="文件ID", alias="file_id")


class ChunkUploadResponse(BaseSchema):
    """分片上传响应"""
    progress: int = Field(..., description="进度")
    uploadedChunks: int = Field(..., description="已上传分片数", alias="uploaded_chunks")
    totalChunks: int = Field(..., description="总分片数", alias="total_chunks")


class ChunkUploadResponseModel(StandardResponse):
    """分片上传响应模型"""
    data: Optional[ChunkUploadResponse] = None


class MergeChunksRequest(BaseSchema):
    """合并分片请求"""
    fileId: str = Field(..., description="文件ID", alias="file_id")
    filename: str = Field(..., description="文件名")


class MergeChunksResponse(BaseSchema):
    """合并分片响应"""
    filename: str = Field(..., description="文件名")


class MergeChunksResponseModel(StandardResponse):
    """合并分片响应模型"""
    data: Optional[MergeChunksResponse] = None


class UploadStatusResponse(BaseSchema):
    """上传状态响应"""
    progress: int = Field(..., description="进度")
    uploadedChunks: int = Field(..., description="已上传分片数", alias="uploaded_chunks")
    totalChunks: int = Field(..., description="总分片数", alias="total_chunks")
    status: str = Field(..., description="状态")


class UploadStatusResponseModel(StandardResponse):
    """上传状态响应模型"""
    data: Optional[UploadStatusResponse] = None


# 新增的schema定义，根据前端API文档要求

class VideoProcessRequest(BaseSchema):
    """视频处理请求"""
    video_id: str = Field(..., description="视频ID")


class VideoProcessResponse(BaseSchema):
    """视频处理响应"""
    pass  # 空数据段


class VideoProcessResponseModel(StandardResponse):
    """视频处理响应模型"""
    data: Optional[VideoProcessResponse] = None


class VideoUploadStatusRequest(BaseSchema):
    """视频上传状态查询请求"""
    fileId: str = Field(..., description="文件ID")


class VideoUploadStatusResponse(BaseSchema):
    """视频上传状态查询响应"""
    video_id: str = Field(..., description="视频ID")
    status: str = Field(..., description="上传状态")
    video_web_path: str = Field(..., description="视频网站路径")
    video_file_path: str = Field(..., description="视频服务器路径")
    json_web_path: str = Field(..., description="骨骼点网站路径")
    json_file_path: str = Field(..., description="骨骼点服务器路径")


class VideoUploadStatusResponseModel(StandardResponse):
    """视频上传状态查询响应模型"""
    data: Optional[VideoUploadStatusResponse] = None


class SimpleVideoUploadRequest(BaseSchema):
    """简单视频上传请求"""
    filename: str = Field(..., description="文件名称")


class SimpleVideoUploadResponse(BaseSchema):
    """简单视频上传响应"""
    id: str = Field(..., description="视频唯一标识ID")
    url: str = Field(..., description="视频访问URL")


class SimpleVideoUploadResponseModel(StandardResponse):
    """简单视频上传响应模型"""
    data: Optional[SimpleVideoUploadResponse] = None


class MultiVideoUploadRequest(BaseSchema):
    """多视频上传请求"""
    filenames: List[str] = Field(..., description="文件名称列表", max_items=5)


class VideoUploadProgress(BaseSchema):
    """单个视频上传进度"""
    filename: str = Field(..., description="文件名")
    progress: int = Field(..., description="上传进度百分比 0-100")
    status: str = Field(..., description="上传状态: uploading, completed, failed")
    id: Optional[str] = Field(None, description="视频ID，上传完成后返回")
    url: Optional[str] = Field(None, description="视频URL，上传完成后返回")
    error: Optional[str] = Field(None, description="错误信息，失败时返回")


class MultiVideoUploadResponse(BaseSchema):
    """多视频上传响应"""
    uploadId: str = Field(..., description="批量上传任务ID")
    totalFiles: int = Field(..., description="总文件数")
    progress: List[VideoUploadProgress] = Field(..., description="各文件上传进度")


class MultiVideoUploadResponseModel(StandardResponse):
    """多视频上传响应模型"""
    data: Optional[MultiVideoUploadResponse] = None


class UploadProgressRequest(BaseSchema):
    """上传进度查询请求"""
    uploadId: str = Field(..., description="批量上传任务ID")


class UploadProgressResponse(BaseSchema):
    """上传进度查询响应"""
    uploadId: str = Field(..., description="批量上传任务ID")
    totalFiles: int = Field(..., description="总文件数")
    completedFiles: int = Field(..., description="已完成文件数")
    overallProgress: int = Field(..., description="总体进度百分比 0-100")
    progress: List[VideoUploadProgress] = Field(..., description="各文件上传进度")


class UploadProgressResponseModel(StandardResponse):
    """上传进度查询响应模型"""
    data: Optional[UploadProgressResponse] = None


class CreateFileRequest(BaseSchema):
    """创建文件记录请求"""
    id: str = Field(..., description="视频ID")
    videoUrl: str = Field(..., description="视频URL")
    startTime: float = Field(..., description="开始时间")
    endTime: float = Field(..., description="结束时间")
    duration: float = Field(..., description="时长")
    wasTrimmed: bool = Field(..., description="是否被裁剪")
    originalSelection: Optional[dict] = Field(None, description="原始选择")
    formData: Optional[dict] = Field(None, description="表单数据")
    outputFormat: int = Field(..., description="输出格式")


class CreateFileResponse(BaseSchema):
    """创建文件记录响应"""
    id: str = Field(..., description="文件记录ID")
    fileId: str = Field(..., description="项目ID")
    status: str = Field(..., description="状态")


class CreateFileResponseModel(StandardResponse):
    """创建文件记录响应模型"""
    data: Optional[CreateFileResponse] = None


# 错误响应模型
class VideoErrorResponseModel(BaseSchema):
    """视频模块错误响应模型"""
    code: int = Field(..., description="错误状态码")
    message: str = Field(..., description="错误信息")
    data: Optional[dict] = Field(None, description="错误时通常为null")
