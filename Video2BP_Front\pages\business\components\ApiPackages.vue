<template>
  <div class="api-packages">
    <div class="packages-container">
      <div class="labels-column">
        <div class="empty-header"></div>
        <div class="label-row">套餐价格</div>
        <div class="label-row">上传数量限制</div>
        <div class="label-row">算力支持优先级</div>
        <div class="label-row">视频上传/下载时长</div>
        <div class="label-row">单个文件大小限制</div>
        <div class="label-row">物理优化</div>
        <div class="label-row">客服支持</div>
        <div class="label-row">有效期</div>
      </div>

      <div class="package" v-for="(pkg, index) in packages" :key="index">
        <div class="package-title">{{ pkg.title }}</div>
        <div class="value-row price" data-label="套餐价格">{{ pkg.price }}</div>
        <div class="value-row" data-label="上传数量限制">{{ pkg.uploadLimit }}</div>
        <div class="value-row" data-label="算力支持优先级">{{ pkg.priority }}</div>
        <div class="value-row" data-label="视频上传/下载时长">{{ pkg.videoLength }}</div>
        <div class="value-row" data-label="单个文件大小限制">{{ pkg.fileSize }}</div>
        <div class="value-row" data-label="物理优化">{{ pkg.physicalOptimization }}</div>
        <div class="value-row" data-label="客服支持">{{ pkg.support }}</div>
        <div class="value-row" data-label="有效期">{{ pkg.validity }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const packages = [
  {
    title: '套餐A',
    price: '2000.00元',
    coins: '4000',
    conversion: '1元=2CV币=2秒',
    uploadLimit: '5条',
    priority: '普通',
    videoLength: '30秒',
    fileSize: '50M',
    physicalOptimization: '支持',
    support: '公用客服',
    validity: '30天',
  },
  {
    title: '套餐B',
    price: '1万元',
    coins: '5万',
    conversion: '1元=5CV币=5秒',
    uploadLimit: '10条',
    priority: '优先',
    videoLength: '60秒',
    fileSize: '100M',
    physicalOptimization: '支持',
    support: '公用客服优先支持',
    validity: '1年',
  },
  {
    title: '套餐C',
    price: '10万元',
    coins: '100万',
    conversion: '1元=10CV币=10秒',
    uploadLimit: '50条',
    priority: '优先',
    videoLength: '60秒',
    fileSize: '200M',
    physicalOptimization: '支持',
    support: '公用客服优先支持',
    validity: '2年',
  },
]
</script>

<style lang="scss" scoped>
.api-packages {
  width: 100%;
  padding: 0 rem(40) rem(200);

  .packages-container {
    width: 87.5%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    gap: rem(10);

    .labels-column {
      width: rem(180);
      display: flex;
      flex-direction: column;
      background-color: rgba(20, 18, 37, 0.5);
      border-radius: rem(8);

      .empty-header {
        height: rem(82); // Match package-title height
      }

      .empty-button {
        user-select: none;
        color: transparent;
        padding: rem(10) 0;
        margin: rem(15) auto;
        width: 80%;
        font-size: rem(16);
      }

      .label-row {
        height: rem(40);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-right: rem(15);
        color: rgba(255, 255, 255, 0.6);
        font-size: rem(14);
        margin-bottom: rem(12);
      }
    }

    .package {
      flex: 1;
      background-color: rgba(29, 25, 52, 0.8);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: rem(8);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      transition: transform 0.3s;
      position: relative;

      &:hover {
        transform: translateY(rem(-5));
        box-shadow: 0 rem(10) rem(20) rgba(0, 0, 0, 0.3);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: rem(100);
        background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
        pointer-events: none;
        z-index: 1;
      }

      .package-title {
        font-size: rem(28);
        font-weight: bold;
        color: #ffffff;
        text-align: center;
        padding: rem(20) 0;
        background-color: rgba(0, 0, 0, 0.2);
      }

      .buy-button {
        background-color: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        text-align: center;
        padding: rem(10) 0;
        margin: rem(15) auto;
        width: 80%;
        border-radius: rem(4);
        cursor: pointer;
        transition: all 0.3s;
        font-size: rem(16);

        &:hover {
          background-color: rgba(255, 235, 179, 0.8);
          color: #1d1934;
        }
      }

      .value-row {
        height: rem(40);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: rem(16);
        color: #ffffff;
        text-align: center;
        margin-bottom: rem(12);

        &.price {
          font-size: rem(24);
          font-weight: bold;
          color: #ffebb3;
        }
      }
    }
  }
}

@media (max-width: rem(1200)) {
  .api-packages {
    .packages-container {
      flex-direction: column;
      max-width: rem(600);

      .labels-column {
        display: none;
      }

      .package {
        margin-bottom: rem(20);

        .value-row {
          position: relative;
          padding-top: rem(20);

          &::before {
            content: attr(data-label);
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            font-size: rem(14);
            color: rgba(255, 255, 255, 0.6);
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
