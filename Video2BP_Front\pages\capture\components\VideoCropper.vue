<template>
    <div class="video-cropper-wrapper" v-if="videoUrl && duration > 0">
        <!-- 隐藏的视频播放器，用于提取帧 -->
        <video ref="hiddenVideoPlayerRef" :src="videoUrl" crossorigin="anonymous"
            style="display: none; width: 320px; height: 180px" @loadedmetadata="onHiddenVideoLoadedMetadata"
            @error="onHiddenVideoError" preload="metadata"></video>
        <!-- 隐藏的Canvas，用于绘制缩略图 -->
        <canvas ref="thumbnailCanvasRef" style="display: none;"></canvas>

        <div class="video-cropper-container">
            <!-- 时间刻度 -->
            <div class="time-scale-container">
                <canvas ref="timeScaleCanvasRef" class="time-scale-canvas"></canvas>
            </div>

            <!-- 视频切片区域 -->
            <div class="video-slices-container" ref="slicesContainerRef" @mousedown="onSlicesContainerMouseDown">
                <div v-for="segment in videoSegments" :key="segment.id" class="video-segment">
                    <div class="segment-thumbnail">
                        <img v-if="segment.thumbnailSrc.value" :src="segment.thumbnailSrc.value" alt="视频帧"
                            class="thumbnail-image" />
                        <!-- 保留灰色占位背景，直到图片加载完成或失败 -->
                        <div v-else class="thumbnail-placeholder"></div>
                    </div>
                </div>

                <!-- 播放头 -->
                <div class="playhead" :style="{ left: playheadPositionPx + 'px' }"
                    v-if="slicesContainerRef && duration > 0"></div>

                <!-- 选择范围高亮 -->
                <div class="selection-overlay"
                    :style="{ left: selectionStartPx + 'px', width: selectionWidthPx + 'px' }"
                    v-if="slicesContainerRef && duration > 0 && selectionWidthPx >= 0"
                    @mousedown.stop="onSelectionOverlayMouseDown">
                    <div class="selection-handle selection-handle-left"
                        @mousedown.stop="onHandleMouseDown($event, 'left')"></div>
                    <div class="selection-handle selection-handle-right"
                        @mousedown.stop="onHandleMouseDown($event, 'right')"></div>
                </div>
            </div>
        </div>
        <div v-if="isGeneratingThumbnails" class="thumbnail-loading-indicator">
            <div class="spinner"></div>
            <span>正在生成缩略图...</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed, onUnmounted } from 'vue';
import { formatTimeAuto } from '~/utils';

interface VideoSegment {
    id: string;
    startTime: number;
    endTime: number;
    thumbnailSrc: ReturnType<typeof ref<string>>; // ref('')
}

const props = defineProps({
    videoUrl: { type: String, required: true },
    duration: { type: Number, required: true },
    currentTime: { type: Number, default: 0 },
    selectionStart: { type: Number, default: 0 },
    selectionEnd: { type: Number, default: 0 },
});

const emit = defineEmits(['update:selectionStart', 'update:selectionEnd', 'update:currentTime']);

const timeScaleCanvasRef = ref<HTMLCanvasElement | null>(null);
const slicesContainerRef = ref<HTMLDivElement | null>(null);
const hiddenVideoPlayerRef = ref<HTMLVideoElement | null>(null);
const thumbnailCanvasRef = ref<HTMLCanvasElement | null>(null);

const isHiddenVideoReady = ref(false);
const isGeneratingThumbnails = ref(false);
let thumbnailGenerationController: AbortController | null = null;


// --- 时间刻度绘制逻辑 ---

const drawTimeScale = () => {
    if (!timeScaleCanvasRef.value || props.duration <= 0) return;
    const canvas = timeScaleCanvasRef.value;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);
    const { width, height } = rect;
    ctx.clearRect(0, 0, width, height);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'bottom';
    const totalDurationSeconds = props.duration;
    const pixelsPerSecond = width / totalDurationSeconds;
    let majorTickInterval = 10, minorTickInterval = 1;
    if (totalDurationSeconds <= 10) { majorTickInterval = 1; minorTickInterval = 0.2; }
    else if (totalDurationSeconds <= 60) { majorTickInterval = 5; minorTickInterval = 1; }
    else if (totalDurationSeconds <= 300) { majorTickInterval = 30; minorTickInterval = 5; }
    else if (totalDurationSeconds <= 600) { majorTickInterval = 60; minorTickInterval = 10; }
    else { majorTickInterval = Math.max(60, Math.round(totalDurationSeconds / 10 / 60) * 60); minorTickInterval = Math.max(10, majorTickInterval / 6); }
    for (let s = 0; s <= totalDurationSeconds; s += minorTickInterval) {
        const x = s * pixelsPerSecond; if (x > width) break;
        ctx.beginPath(); ctx.moveTo(x, height * 0.5); ctx.lineTo(x, height); ctx.stroke();
    }
    for (let s = 0; s <= totalDurationSeconds; s += majorTickInterval) {
        const x = s * pixelsPerSecond;
        if (x > width + pixelsPerSecond / 2 && s > totalDurationSeconds) break;
        ctx.beginPath(); ctx.moveTo(x, height * 0.2); ctx.lineTo(x, height); ctx.stroke();
        if (x <= width) { ctx.fillText(formatTimeAuto(s), x, height * 0.4); }
        else if (s === totalDurationSeconds) { ctx.fillText(formatTimeAuto(s), width, height * 0.4); }
    }
};


// --- 视频切片逻辑 ---
const numberOfSegments = ref(15);
const videoSegments = computed<VideoSegment[]>(() => {
    if (props.duration <= 0) return [];
    const segmentDuration = props.duration / numberOfSegments.value;
    return Array.from({ length: numberOfSegments.value }, (_, i) => ({
        id: `segment-${i}`,
        startTime: i * segmentDuration,
        endTime: (i + 1) * segmentDuration,
        thumbnailSrc: ref(''),
    }));
});

// --- 时间与像素转换 ---
const timeToPx = (time: number): number => {
    if (!slicesContainerRef.value || props.duration <= 0) return 0;
    const containerWidth = slicesContainerRef.value.clientWidth;
    return (time / props.duration) * containerWidth;
};
const pxToTime = (px: number): number => {
    if (!slicesContainerRef.value || props.duration <= 0) return 0;
    const containerWidth = slicesContainerRef.value.clientWidth;
    let time = (px / containerWidth) * props.duration;
    time = Math.max(0, Math.min(time, props.duration));
    return time;
};

const playheadPositionPx = computed(() => timeToPx(props.currentTime));
const selectionStartPx = computed(() => timeToPx(props.selectionStart));
const selectionEndPx = computed(() => timeToPx(props.selectionEnd));
const selectionWidthPx = computed(() => Math.max(0, selectionEndPx.value - selectionStartPx.value));

// --- 拖动状态 ---
const minClipDuration = 0.1;
const currentlyDraggingHandle = ref<'left' | 'right' | null>(null);
const isDraggingSelection = ref(false);
const dragStartX = ref(0);
const initialSelectionStart = ref(0);
const initialSelectionEnd = ref(0);

// --- 鼠标事件处理器 ---
const onHandleMouseDown = (event: MouseEvent, handleType: 'left' | 'right') => {
    if (!slicesContainerRef.value) return;
    currentlyDraggingHandle.value = handleType;
    document.addEventListener('mousemove', onDocumentMouseMove);
    document.addEventListener('mouseup', onDocumentMouseUp);
    document.body.style.cursor = 'ew-resize';
};
const onSelectionOverlayMouseDown = (event: MouseEvent) => {
    if (!slicesContainerRef.value || currentlyDraggingHandle.value) return;
    isDraggingSelection.value = true;
    dragStartX.value = event.clientX;
    initialSelectionStart.value = props.selectionStart;
    initialSelectionEnd.value = props.selectionEnd;
    document.addEventListener('mousemove', onDocumentMouseMove);
    document.addEventListener('mouseup', onDocumentMouseUp);
    document.body.style.cursor = 'grabbing';
};
const onSlicesContainerMouseDown = (event: MouseEvent) => {
    if (!slicesContainerRef.value || currentlyDraggingHandle.value || isDraggingSelection.value) return;
    const target = event.target as HTMLElement;
    // 如果点击的是手柄或选区本身，它们的处理器会 .stop 冒泡，所以这里不会被触发

    if (target.closest('.selection-handle') || target.closest('.selection-overlay')) {
        return;
    }

    const containerRect = slicesContainerRef.value.getBoundingClientRect();
    const clickXPx = event.clientX - containerRect.left;
    const clickedTime = pxToTime(clickXPx);
    emit('update:currentTime', clickedTime);

    const currentSelectionDuration = props.selectionEnd - props.selectionStart;
    if (currentSelectionDuration <= 0 && props.duration > 0) {
        const defaultNewDuration = Math.min(props.duration / 10, 5);
        let newStart = clickedTime;
        let newEnd = clickedTime + defaultNewDuration;
        if (newEnd > props.duration) {
            newEnd = props.duration;
            newStart = Math.max(0, newEnd - defaultNewDuration);
        }
        emit('update:selectionStart', newStart);
        emit('update:selectionEnd', newEnd);
    }
};
const onDocumentMouseMove = (event: MouseEvent) => {
    if (!slicesContainerRef.value) return;
    const containerRect = slicesContainerRef.value.getBoundingClientRect();
    let mouseXPx = event.clientX - containerRect.left;

    if (currentlyDraggingHandle.value) {
        mouseXPx = Math.max(0, Math.min(mouseXPx, containerRect.width));
        const newTime = pxToTime(mouseXPx);
        if (currentlyDraggingHandle.value === 'left') {
            const newStartTime = Math.max(0, Math.min(newTime, props.selectionEnd - minClipDuration));
            if (newStartTime !== props.selectionStart) emit('update:selectionStart', newStartTime);
        } else if (currentlyDraggingHandle.value === 'right') {
            const newEndTime = Math.min(props.duration, Math.max(newTime, props.selectionStart + minClipDuration));
            if (newEndTime !== props.selectionEnd) emit('update:selectionEnd', newEndTime);
        }
    } else if (isDraggingSelection.value) {
        const dxPx = event.clientX - dragStartX.value;
        const dxTime = (dxPx / containerRect.width) * props.duration;
        let newStart = initialSelectionStart.value + dxTime;
        let newEnd = initialSelectionEnd.value + dxTime;
        const selectionDuration = initialSelectionEnd.value - initialSelectionStart.value;
        if (newStart < 0) { newStart = 0; newEnd = newStart + selectionDuration; }
        if (newEnd > props.duration) { newEnd = props.duration; newStart = newEnd - selectionDuration; }
        if (newStart < 0) newStart = 0;
        if (newStart !== props.selectionStart || newEnd !== props.selectionEnd) {
            emit('update:selectionStart', newStart);
            emit('update:selectionEnd', newEnd);
        }
    }
};
const onDocumentMouseUp = () => {
    if (currentlyDraggingHandle.value) {
        currentlyDraggingHandle.value = null;
        document.body.style.cursor = 'default';
    }
    if (isDraggingSelection.value) {
        isDraggingSelection.value = false;
        document.body.style.cursor = 'default';
    }
    document.removeEventListener('mousemove', onDocumentMouseMove);
    document.removeEventListener('mouseup', onDocumentMouseUp);
};

// --- 缩略图生成逻辑 ---
const onHiddenVideoLoadedMetadata = () => {
    console.log('隐藏视频元数据已加载，准备生成缩略图');
    isHiddenVideoReady.value = true;
    if (props.videoUrl && props.duration > 0) {
        triggerThumbnailGeneration();
    }
};

const onHiddenVideoError = (e: Event) => {
    console.error('隐藏视频加载错误:', e);
    isHiddenVideoReady.value = false;
    if (thumbnailGenerationController) {
        thumbnailGenerationController.abort();
    }
    isGeneratingThumbnails.value = false;
    videoSegments.value.forEach(segment => segment.thumbnailSrc.value = '');
};

const THUMBNAIL_WIDTH = 120; // 缩略图的目标宽度
const THUMBNAIL_QUALITY = 0.5; // JPEG 质量

const triggerThumbnailGeneration = () => {
    if (thumbnailGenerationController) {
        thumbnailGenerationController.abort(); // 取消上一次的生成（如果还在进行中）
    }
    thumbnailGenerationController = new AbortController();
    generateSegmentThumbnails(thumbnailGenerationController.signal);
}

const generateSegmentThumbnails = async (abortSignal: AbortSignal) => {
    if (!hiddenVideoPlayerRef.value || !thumbnailCanvasRef.value || !props.duration || !isHiddenVideoReady.value) {
        console.log('无法生成缩略图，前置条件未满足');
        isGeneratingThumbnails.value = false; // 确保即使不开始也重置状态
        return;
    }
    // 如果上一个生成任务正在运行，但 signal 不同（意味着是新的触发），则旧的会被 abortSignal 中断
    if (isGeneratingThumbnails.value && !abortSignal.aborted) {
        console.log("缩略图生成已在进行中，新请求将等待或被控制器中止");
        //  AbortController 会处理这个，但可以加 return 避免重复进入
        // return;
    }

    isGeneratingThumbnails.value = true;
    const video = hiddenVideoPlayerRef.value;
    const canvas = thumbnailCanvasRef.value;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        console.error('无法从缩略图画布获取2D上下文');
        isGeneratingThumbnails.value = false;
        return;
    }

    videoSegments.value.forEach(segment => segment.thumbnailSrc.value = ''); // 清空旧的

    const aspectRatio = video.videoWidth / video.videoHeight;
    canvas.width = THUMBNAIL_WIDTH;
    canvas.height = THUMBNAIL_WIDTH / aspectRatio;
    if (isNaN(canvas.height) || !isFinite(canvas.height) || canvas.height <= 0) {
        canvas.height = THUMBNAIL_WIDTH * (9 / 16); // 备用方案
        console.warn("视频宽高比或尺寸无效，使用16:9备用方案设置缩略图画布高度");
    }
    if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.error("视频宽度或高度为零，无法生成缩略图");
        isGeneratingThumbnails.value = false;
        return;
    }


    console.log(`正在生成 ${videoSegments.value.length} 个缩略图...`);

    for (const segment of videoSegments.value) {
        if (abortSignal.aborted) {
            console.log("缩略图生成已中止");
            break;
        }
        try {
            const timeToSeek = segment.startTime + 0.1; // 偏离一点，避免某些视频在0秒是黑帧

            // 使用 Promise 包装 seek 操作
            await new Promise<void>((resolve, reject) => {
                if (abortSignal.aborted) { reject(new DOMException('已中止', 'AbortError')); return; }

                const onSeeked = () => {
                    video.removeEventListener('seeked', onSeeked);
                    video.removeEventListener('error', onErrorSeeking); // 使用不同的错误处理器名
                    if (abortSignal.aborted) { reject(new DOMException('已中止', 'AbortError')); return; }

                    // 确保 video 尺寸有效
                    if (video.videoWidth === 0 || video.videoHeight === 0) {
                        console.warn(`视频片段 ${segment.id} 在时间 ${video.currentTime} 时尺寸为零，跳过绘制`);
                        reject(new Error("视频尺寸为零"));
                        return;
                    }

                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                    segment.thumbnailSrc.value = canvas.toDataURL('image/jpeg', THUMBNAIL_QUALITY);
                    resolve();
                };

                const onErrorSeeking = (err: Event | string) => {
                    video.removeEventListener('seeked', onSeeked);
                    video.removeEventListener('error', onErrorSeeking);
                    console.error(`视频片段 ${segment.id} 在定位/绘制时出错:`, err);
                    reject(err);
                };

                video.addEventListener('seeked', onSeeked, { once: true });
                video.addEventListener('error', onErrorSeeking, { once: true });

                if (video.readyState >= video.HAVE_METADATA) { // readyState >= 1
                    video.currentTime = Math.min(timeToSeek, props.duration - 0.01); // 确保不超出总时长
                } else {
                    console.warn("视频未准备好进行定位");
                    reject(new Error("视频未准备好"));
                }
            });
        } catch (error: any) {
            if (error.name === 'AbortError') {
                console.log(`视频片段 ${segment.id} 的缩略图生成已中止`);
            } else {
                console.error(`视频片段 ${segment.id} 缩略图生成失败:`, error);
            }
            segment.thumbnailSrc.value = ''; // 标记失败
            if (abortSignal.aborted) break; // 如果是中止信号导致的，跳出循环
        }
    }

    if (!abortSignal.aborted) {
        console.log('缩略图生成完成');
    }
    isGeneratingThumbnails.value = false;
};

onMounted(() => {
    nextTick(() => {
        drawTimeScale();
        if (hiddenVideoPlayerRef.value && props.videoUrl) {
            // 如果 videoUrl 在 onMounted 时已经存在，尝试加载
        }
    });
});

watch(() => props.videoUrl, (newUrl) => {
    isHiddenVideoReady.value = false;
    if (thumbnailGenerationController) {
        thumbnailGenerationController.abort();
    }
    isGeneratingThumbnails.value = false;
    videoSegments.value.forEach(segment => segment.thumbnailSrc.value = '');

    if (newUrl && hiddenVideoPlayerRef.value) {
        console.log('新视频URL已设置，等待元数据加载...');
        // 视频元素会自动尝试加载新源
        // 如果成功，onHiddenVideoLoadedMetadata会被调用
    } else if (!newUrl && hiddenVideoPlayerRef.value) {
        hiddenVideoPlayerRef.value.src = ''; // 清除src以停止加载
        hiddenVideoPlayerRef.value.load(); // 某些浏览器可能需要这个来完全停止
    }
}, { immediate: false });

watch(
    () => [props.duration, isHiddenVideoReady.value, numberOfSegments.value],
    (newValues, oldValues) => { // oldValues 会在 immediate: true 的首次调用时为 undefined
        const [newDuration, videoReady, numSegs] = newValues;
        // 当 oldValues 为 undefined 时，安全地解构或者提供默认值
        const [oldDuration, oldVideoReady, oldNumSegs] = oldValues || [undefined, undefined, undefined];

        nextTick(() => {
            drawTimeScale();
        });

        if (+newDuration > 0 && videoReady && props.videoUrl) {
            // 触发缩略图生成的条件:
            // 1. 时长改变
            // 2. 切片数量改变
            // 3. 视频刚刚变为就绪状态 
            // 在首次 immediate 执行时，oldDuration, oldVideoReady, oldNumSegs 均为 undefined，
            if (newDuration !== oldDuration || numSegs !== oldNumSegs || (videoReady && !oldVideoReady)) {
                // console.log('Duration, segment count, or video readiness changed, triggering thumbnail generation. New:', newValues, 'Old:', oldValues);
                triggerThumbnailGeneration();
            }
        }
    },
    { immediate: true, deep: true }
);


onUnmounted(() => {
    if (thumbnailGenerationController) {
        thumbnailGenerationController.abort();
    }
    document.removeEventListener('mousemove', onDocumentMouseMove);
    document.removeEventListener('mouseup', onDocumentMouseUp);
    if (hiddenVideoPlayerRef.value) {

        hiddenVideoPlayerRef.value.removeEventListener('loadedmetadata', onHiddenVideoLoadedMetadata);
        hiddenVideoPlayerRef.value.removeEventListener('error', onHiddenVideoError);

        hiddenVideoPlayerRef.value.pause();
        hiddenVideoPlayerRef.value.removeAttribute('src');
        hiddenVideoPlayerRef.value.load();
        console.log("隐藏视频播放器资源已释放");
    }
});

</script>

<style scoped lang="scss">
.video-cropper-wrapper {
    width: 100%;
    margin-top: rem(20);
    position: relative;
}

.video-cropper-container {
    background-color: rgba(30, 30, 50, 0.5);
    border-radius: rem(8);
    padding: rem(15);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.time-scale-container {
    width: 100%;
    height: rem(35);
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: rem(4);
    margin-bottom: rem(10);
    position: relative;
    overflow: hidden;
}

.time-scale-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

.video-slices-container {
    width: 100%;
    height: rem(70);
    /* 保持原有高度 */
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: rem(4);
    display: flex;
    align-items: center;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.15);
    cursor: pointer;
}

.video-segment {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    position: relative;
    pointer-events: none;
    overflow: hidden;

    /* 确保图片不溢出 segment 边界 */
    &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 10%;
        bottom: 10%;
        width: rem(1);
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.segment-thumbnail {
    width: 95%;
    height: 85%;
    background-color: rgba(255, 255, 255, 0.08);
    /* 作为图片未加载时的背景 */
    border-radius: rem(3);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    /* 确保图片在容器内 */
    position: relative;
    /* For placeholder absolute positioning if needed */
}

.thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* 保持宽高比，裁剪多余部分 */
    display: block;
}

.thumbnail-placeholder {
    /* 这个 div 现在主要用于在 segment.thumbnailSrc 为空时，
       由 .segment-thumbnail 的背景色来充当占位符。
       如果需要特定的占位符样式（比如一个图标），可以在这里添加。
    */
    width: 100%;
    height: 100%;
    /* background-color: rgba(255, 255, 255, 0.08); // 已移至父级 */
}

.playhead {
    position: absolute;
    top: 0;
    bottom: 0;
    width: rem(2);
    background-color: #e74c3c;
    pointer-events: none;
    z-index: 10;
    box-shadow: 0 0 rem(5) rgba(231, 76, 60, 0.7);
}

.selection-overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    background-color: rgba(52, 152, 219, 0.4);
    pointer-events: auto;
    cursor: grab;
    z-index: 5;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
}

.selection-handle {
    width: rem(8);
    height: 100%;
    background-color: #3498db;
    cursor: ew-resize;
    pointer-events: auto;
    z-index: 6;
    position: absolute;
    top: 0;

    &::before {
        content: "";
        position: absolute;
        left: rem(-4);
        right: rem(-4);
        top: 0;
        bottom: 0;
    }
}

.selection-handle-left {
    left: rem(-4);
    border-top-left-radius: rem(3);
    border-bottom-left-radius: rem(3);
}

.selection-handle-right {
    right: rem(-4);
    border-top-right-radius: rem(3);
    border-bottom-right-radius: rem(3);
}

.thumbnail-loading-indicator {
    position: absolute;
    bottom: rem(-30); // 显示在裁剪器下方
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: rem(5) rem(10);
    border-radius: rem(4);
    font-size: rem(12);
    z-index: 100;
    display: flex;
    align-items: center;

    .spinner {
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top: 2px solid #fff;
        width: rem(12);
        height: rem(12);
        animation: spin 1s linear infinite;
        margin-right: rem(8);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>
