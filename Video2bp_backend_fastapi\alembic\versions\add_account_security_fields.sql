-- 添加账户安全相关字段到users表
-- 用于支持登录失败锁定功能

ALTER TABLE `users` 
ADD COLUMN `failed_login_attempts` INT NOT NULL DEFAULT 0 COMMENT '登录失败次数',
ADD COLUMN `last_failed_login` DATETIME NULL COMMENT '最后一次登录失败时间',
ADD COLUMN `account_locked_until` DATETIME NULL COMMENT '账户锁定到期时间';

-- 添加索引以提高查询性能
CREATE INDEX `idx_users_failed_login_attempts` ON `users` (`failed_login_attempts`);
CREATE INDEX `idx_users_account_locked_until` ON `users` (`account_locked_until`);
