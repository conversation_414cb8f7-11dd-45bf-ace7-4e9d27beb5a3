# DES加解密工具类
import base64
from Cryptodome.Cipher import DES
from Cryptodome.Util.Padding import pad, unpad

class DESUtil:
    @staticmethod
    def encrypt(plaintext: str, key: str) -> str:
        """DES加密并Base64编码
        :param plaintext: 明文
        :param key: 8字节密钥
        :return: Base64编码的密文
        """
        cipher = DES.new(key.encode("utf-8"), DES.MODE_ECB)
        padded_data = pad(plaintext.encode("utf-8"), DES.block_size)
        encrypted = cipher.encrypt(padded_data)
        return base64.b64encode(encrypted).decode("utf-8")

    @staticmethod
    def decrypt(ciphertext: str, key: str) -> str:
        """Base64解码并DES解密
        :param ciphertext: Base64编码的密文
        :param key: 8字节密钥
        :return: 解密后的明文
        """
        cipher = DES.new(key.encode("utf-8"), DES.MODE_ECB)
        encrypted_data = base64.b64decode(ciphertext)
        decrypted = cipher.decrypt(encrypted_data)
        return unpad(decrypted, DES.block_size).decode("utf-8")