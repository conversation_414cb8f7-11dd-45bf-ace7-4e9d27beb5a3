<template>
  <div class="user-page">
    <div class="user-content">
      <!-- 标签页导航 -->
      <div class="tab-navigation">
        <div v-for="tab in tabs" :key="tab.id" class="tab-item" :class="{ active: activeTab === tab.id }"
          @click="activeTab = tab.id">
          {{ tab.name }}
        </div>
      </div>

      <!-- 用户信息卡片 -->
      <div class="user-info-card">
        <div v-for="info in filteredUserInfoList" :key="info.label" class="info-row">
          <div class="info-label">{{ info.label }}</div>
          <div class="info-value">
            {{ info.value }}
            <span v-if="info.action" class="action-link" @click="handleAction(info.action)">
              {{ info.actionText }}
            </span>
          </div>
        </div>
      </div>


      <div class="payment-content" v-if="activeTab === 'payment'">
        <!-- 订单记录表格 -->
        <div class="records-table">
          <a-config-provider :locale="zhCN">
            <a-table :columns="tableColumns" :data-source="orderRecords" :pagination="paginationConfig"
              :locale="{ emptyText: '暂无数据' }" :loading="loading" row-key="id">
              <template #emptyText>
                <div class="empty-state">
                  <div class="empty-icon">
                    <div class="empty-image">
                      <div class="empty-text">暂无数据</div>
                    </div>
                  </div>
                </div>
              </template>
            </a-table>
          </a-config-provider>
        </div>
      </div>

      <!-- 修改密码弹窗 -->
      <ChangePasswordModal v-model:open="changePasswordModalVisible" @success="handlePasswordChangeSuccess" />
    </div>
  </div>
</template>

<script setup lang="ts">
import ChangePasswordModal from './components/ChangePasswordModal.vue'
import { useUserStore } from '~/stores/user'
import { storeToRefs } from 'pinia'
import type { RecordItem } from '~/types/user'
import { useApi } from '~/composables/useApi'

// 定义中文 locale 配置
const zhCN = {
  locale: 'zh_CN',
  Pagination: {
    items_per_page: '条/页',
    jump_to: '跳至',
    jump_to_confirm: '确定',
    page: '页',
    prev_page: '上一页',
    next_page: '下一页',
    prev_5: '向前 5 页',
    next_5: '向后 5 页',
    prev_3: '向前 3 页',
    next_3: '向后 3 页',
  },
  Table: {
    filterTitle: '筛选',
    filterConfirm: '确定',
    filterReset: '重置',
    selectAll: '全选当页',
    selectInvert: '反选当页',
    emptyText: '暂无数据',
  },
};

// 分页配置
const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: pageSize.value,
  total: total.value,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
  pageSizeOptions: ['10', '20', '50'],
  onChange: handlePageChange,
  onShowSizeChange: handlePageChange
}))

defineOptions({
  name: 'UserView',
})

const layoutConfig = useState('layoutConfig', () => ({}))

layoutConfig.value = {
  footerVisible: false
}

// 获取 API 服务实例
const { user } = useApi()

// 获取用户信息（保持响应式）
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const tabs = ref([
  { id: 'settings', name: '用户设置' },
  { id: 'payment', name: '支付计划' },
])

// 当前激活的标签页
const activeTab = ref('payment')

// API 分页数据管理
const orderRecords = ref<RecordItem[]>([]) // 当前页面的订单记录
const currentPage = ref(1) // 当前页码
const pageSize = ref(10) // 每页显示数量
const total = ref(0) // 总记录数，从 API 返回
const loading = ref(false) // 加载状态

// 修改密码弹窗状态
const changePasswordModalVisible = ref(false)

// 用户信息数据（使用响应式 userInfo）
const userInfoList = computed(() => [
  {
    label: '邮箱',
    value: userInfo.value?.email || '未设置',
    visibleInTabs: ['settings', 'payment']
  },
  {
    label: '密码',
    value: '******',
    action: 'changePassword',
    actionText: '修改密码',
    visibleInTabs: ['settings']
  },
  {
    label: 'Vip等级',
    value: userInfo.value?.vipLevel?.toString() || '0',
    action: 'levelUp',
    visibleInTabs: ['payment']
  },
  {
    label: '余额',
    value: userInfo.value?.balance?.toString() || '0',
    action: 'recharge',
    actionText: '增量包',
    visibleInTabs: ['payment']
  },
  {
    label: '付费版本',
    value: userInfo.value?.vipLevel && userInfo.value.vipLevel > 0 ? `VIP${userInfo.value.vipLevel}` : '免费版本',
    action: 'upgrade',
    actionText: '支付升级计划',
    visibleInTabs: ['payment']
  },
  {
    label: 'VIP到期时间',
    value: userInfo.value?.vipDeadline ? new Date(userInfo.value.vipDeadline).toLocaleDateString() : '无',
    visibleInTabs: ['payment']
  }
])

// 根据当前tab过滤显示的用户信息
const filteredUserInfoList = computed(() => {
  return userInfoList.value.filter(item =>
    item.visibleInTabs.includes(activeTab.value)
  )
})

// 处理操作点击
const handleAction = (action: string) => {
  switch (action) {
    case 'changePassword':
      changePasswordModalVisible.value = true
      break
    case 'levelUp':
    case 'recharge':
    case 'upgrade':
      navigateTo('/upgrade')
      break
    default:
      break
  }
}

// 表格列配置
const tableColumns = ref([
  {
    title: '套餐类型',
    dataIndex: 'type',
    key: 'type',
    align: 'center' as const
  },
  {
    title: '扣费时间',
    dataIndex: 'time',
    key: 'time',
    align: 'center' as const
  },
  {
    title: '金额/元',
    dataIndex: 'amount',
    key: 'amount',
    align: 'center' as const
  },
  {
    title: '支付方式',
    dataIndex: 'method',
    key: 'method',
    align: 'center' as const
  },
  {
    title: '余额/元',
    dataIndex: 'toAccount',
    key: 'toAccount',
    align: 'center' as const
  }
])

// API 调用函数
const fetchOrderRecords = async (page: number = 1, size: number = 10) => {
  try {
    loading.value = true

    // 
    const result = await user?.getOrderRecords({
      pageNum: page,
      pageSize: size,
    })

    if (!result) {
      throw new Error('请求失败')
    }

    const { data, total: totalCount, pageNum, pageSize: responsePageSize } = result.data
    orderRecords.value = data
    total.value = totalCount
    currentPage.value = pageNum
    pageSize.value = responsePageSize

    console.log('获取订单记录成功:', result)

  } catch (err) {
    console.error('获取订单记录失败:', err)
    // 请求错误时数据为空
    orderRecords.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 分页变化处理函数
const handlePageChange = async (page: number, size?: number) => {
  const newSize = size || pageSize.value
  await fetchOrderRecords(page, newSize)
}
// 处理密码修改成功
const handlePasswordChangeSuccess = () => {
  console.log('密码修改成功')
  // 刷新用户信息
  userStore.fetchUserInfo()
}

// 页面加载时获取用户信息和订单记录
onMounted(() => {
  if (!userInfo.value) {
    userStore.fetchUserInfo()
  }
  // 获取订单记录
  fetchOrderRecords(1, pageSize.value)
})

</script>
<style lang="scss">
// 分页样式
.records-table {
  .ant-pagination {
    margin-top: rem(24);
    text-align: center;

    .ant-pagination-item {
      background: rgba(0, 0, 0, 0.6);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: rem(4);

      a {
        color: #ffffff !important;
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(14);
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);

        a {
          color: #ffffff !important;
        }
      }

      &.ant-pagination-item-active {
        background: #0091ff !important;
        border-color: #0091ff !important;

        a {
          color: #ffffff !important;
          font-weight: bold;
        }
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      .ant-pagination-item-link {
        background: rgba(0, 0, 0, 0.6) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        color: #ffffff !important;

        &:hover {
          background: rgba(255, 255, 255, 0.1) !important;
          border-color: rgba(255, 255, 255, 0.5) !important;
          color: #ffffff !important;
        }
      }
    }

    .ant-pagination-options {
      .ant-select {
        .ant-select-selector {
          background: rgba(0, 0, 0, 0.6) !important;
          border: 1px solid rgba(255, 255, 255, 0.3) !important;
          color: #ffffff !important;
        }

        .ant-select-selection-item {
          color: #ffffff !important;
        }

        &:hover .ant-select-selector {
          background: rgba(255, 255, 255, 0.1) !important;
          border-color: rgba(255, 255, 255, 0.5) !important;
        }
      }

      .ant-pagination-simple-pager {
        color: #ffffff !important;

        input {
          background: rgba(0, 0, 0, 0.6) !important;
          border: 1px solid rgba(255, 255, 255, 0.3) !important;
          color: #ffffff !important;

          &:focus {
            border-color: #0091ff !important;
            box-shadow: 0 0 0 2px rgba(0, 145, 255, 0.2) !important;
          }
        }
      }
    }

    .ant-pagination-total-text {
      color: #ffffff !important;
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-size: rem(14);
      font-weight: 400;
    }

    // 跳转输入框样式
    .ant-pagination-options-quick-jumper {
      color: #ffffff !important;

      input {
        background: rgba(0, 0, 0, 0.6) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        color: #ffffff !important;

        &:focus {
          border-color: #0091ff !important;
          box-shadow: 0 0 0 2px rgba(0, 145, 255, 0.2) !important;
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.user-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('~/assets/images/upgrade-bg.webp');
  background-size: 100% 100%;
  overflow: hidden;
}

.user-content {
  flex: 1;
  padding: rem(186) rem(120) rem(120);
}

.tab-navigation {
  display: flex;
  gap: 0;
  margin-bottom: rem(54);
}

.tab-item {
  margin: 0 rem(16);
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: bold;
  font-size: rem(32);
  color: #FFFFFF;
  line-height: rem(32);
  text-align: left;
  font-style: normal;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    color: rgba(255, 255, 255, 0.8);
  }

  &.active {
    &::after {
      content: '';
      position: absolute;
      bottom: rem(-18);
      left: 0;
      width: 100%;
      height: rem(3);
      background: #0091ff;
    }
  }
}

// 用户信息卡片样式
.user-info-card {
  background: #000000;
  border-radius: rem(10);
  border: 1px solid #FFFFFF;
  padding: rem(39) rem(40) rem(41);
  margin-bottom: rem(61);
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: rem(24);

  &:last-child {
    margin-bottom: 0;
  }

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  width: rem(150);
  font-size: rem(16);
  font-weight: 500;
  flex-shrink: 0;
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: 400;
  font-size: rem(24);
  color: rgba(255, 255, 255, 0.6);
  line-height: rem(32);
  text-align: left;
  font-style: normal;
  margin-right: rem(40);
}

.info-value {
  flex: 1;
  color: #ffffff;
  font-size: rem(16);
  display: flex;
  align-items: center;
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: 400;
  font-size: rem(24);
  color: #FFFFFF;
  line-height: rem(32);
  text-align: left;
  font-style: normal;
}

.action-link {
  cursor: pointer;
  margin-left: rem(16);
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: 400;
  font-size: rem(24);
  color: #0091FF;
  line-height: rem(32);
  text-align: left;
  font-style: normal;
}

.payment-content {
  width: 100%;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: rem(120) rem(20);
}

.empty-icon {
  margin-bottom: rem(14);
}

.empty-image {
  width: rem(180);
  height: rem(180);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('~/assets/images/no-result.webp');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.empty-text {
  color: rgba(255, 255, 255, 0.6);
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: 400;
  font-size: rem(20);
  line-height: rem(28);
}

// 记录表格样式
.records-table {
  background: transparent;
  overflow: hidden;
  padding: 0 rem(24);
  border-radius: rem(4);
  border: 1px solid;
  border-image: linear-gradient(180deg,
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 80%,
      rgba(255, 255, 255, 0.1) 100%) 1;


  // 覆盖 Ant Design 表格样式
  :deep(.ant-table) {
    background: transparent;
    color: #ffffff;

    table {
      width: 100%;
      border-collapse: separate !important;
    }


    .ant-table-thead>tr>th {
      background: transparent;
      color: rgba(255, 255, 255, 0.8);
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: bold;
      font-size: rem(18);
      line-height: rem(26);
      text-align: right;
      font-style: normal;
      padding: rem(24) rem(20);
      border-radius: rem(4);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);



      &:last-child {
        border-right: none;
      }

      &::before {
        display: none;
      }
    }

    .ant-table-tbody>tr>td {
      background: transparent;
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: bold;
      font-size: rem(18);
      color: #FFFFFF;
      line-height: rem(26);
      text-align: right;
      font-style: normal;
      padding: rem(24) rem(20);

      &:last-child {
        border-right: none;
      }
    }

    .ant-table-tbody>tr:hover>td {
      background: transparent !important;
    }

    .ant-table-tbody>tr:last-child>td {
      border-bottom: none;
    }

    .ant-table-placeholder {
      background: transparent;
      border: none;

      .ant-table-cell {
        border: none;
        background: transparent;
      }
    }
  }
}
</style>
