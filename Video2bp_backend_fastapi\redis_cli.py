#!/usr/bin/env python3
"""
Redis管理命令行工具
提供Redis数据查看、清理等功能
"""

import asyncio
import sys
import os
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.redis import get_redis, get_async_redis
from core.redis.redis_utils import RedisUtils, AsyncRedisUtils


def show_info():
    """显示Redis信息"""
    try:
        client = get_redis()
        info = client.info()
        
        print("=== Redis服务器信息 ===")
        print(f"Redis版本: {info.get('redis_version', 'Unknown')}")
        print(f"运行模式: {info.get('redis_mode', 'Unknown')}")
        print(f"已用内存: {info.get('used_memory_human', 'Unknown')}")
        print(f"连接数: {info.get('connected_clients', 'Unknown')}")
        print(f"运行时间: {info.get('uptime_in_seconds', 0)} 秒")
        print(f"键总数: {client.dbsize()}")
        
    except Exception as e:
        print(f"获取Redis信息失败: {e}")


def list_keys(pattern="*", limit=50):
    """列出Redis键"""
    try:
        client = get_redis()
        keys = client.keys(pattern)
        
        print(f"=== Redis键列表 (模式: {pattern}) ===")
        print(f"找到 {len(keys)} 个键")
        
        if len(keys) > limit:
            print(f"显示前 {limit} 个键:")
            keys = keys[:limit]
        
        for i, key in enumerate(keys, 1):
            ttl = client.ttl(key)
            key_type = client.type(key)
            
            ttl_str = f"{ttl}s" if ttl > 0 else "永久" if ttl == -1 else "已过期"
            print(f"{i:3d}. {key} ({key_type}) TTL: {ttl_str}")
            
    except Exception as e:
        print(f"列出键失败: {e}")


def get_key_value(key):
    """获取键值"""
    try:
        client = get_redis()
        
        if not client.exists(key):
            print(f"键 '{key}' 不存在")
            return
        
        key_type = client.type(key)
        ttl = client.ttl(key)
        
        print(f"=== 键信息: {key} ===")
        print(f"类型: {key_type}")
        print(f"TTL: {ttl}s" if ttl > 0 else "TTL: 永久" if ttl == -1 else "TTL: 已过期")
        
        if key_type == 'string':
            value = client.get(key)
            print(f"值: {value}")
        elif key_type == 'list':
            length = client.llen(key)
            print(f"长度: {length}")
            if length > 0:
                values = client.lrange(key, 0, min(10, length-1))
                print(f"前10个元素: {values}")
        elif key_type == 'set':
            size = client.scard(key)
            print(f"大小: {size}")
            if size > 0:
                members = list(client.sscan_iter(key, count=10))
                print(f"部分成员: {members[:10]}")
        elif key_type == 'hash':
            size = client.hlen(key)
            print(f"字段数: {size}")
            if size > 0:
                fields = client.hgetall(key)
                print(f"字段: {dict(list(fields.items())[:10])}")
        elif key_type == 'zset':
            size = client.zcard(key)
            print(f"大小: {size}")
            if size > 0:
                members = client.zrange(key, 0, 9, withscores=True)
                print(f"前10个成员: {members}")
                
    except Exception as e:
        print(f"获取键值失败: {e}")


def delete_keys(pattern):
    """删除匹配的键"""
    try:
        client = get_redis()
        keys = client.keys(pattern)
        
        if not keys:
            print(f"没有找到匹配模式 '{pattern}' 的键")
            return
        
        print(f"找到 {len(keys)} 个匹配的键:")
        for key in keys[:10]:  # 只显示前10个
            print(f"  - {key}")
        
        if len(keys) > 10:
            print(f"  ... 还有 {len(keys) - 10} 个键")
        
        confirm = input(f"\n确定要删除这 {len(keys)} 个键吗? (y/N): ")
        if confirm.lower() == 'y':
            deleted = client.delete(*keys)
            print(f"成功删除 {deleted} 个键")
        else:
            print("操作已取消")
            
    except Exception as e:
        print(f"删除键失败: {e}")


def clear_auth_data():
    """清理认证相关数据"""
    patterns = [
        "login_attempts:*",
        "account_locked:*", 
        "email_verification:*",
        "user_token:*"
    ]
    
    try:
        client = get_redis()
        total_deleted = 0
        
        print("=== 清理认证数据 ===")
        for pattern in patterns:
            keys = client.keys(pattern)
            if keys:
                deleted = client.delete(*keys)
                total_deleted += deleted
                print(f"删除 {pattern}: {deleted} 个键")
            else:
                print(f"删除 {pattern}: 0 个键")
        
        print(f"\n总共删除 {total_deleted} 个键")
        
    except Exception as e:
        print(f"清理认证数据失败: {e}")


def monitor_redis():
    """监控Redis命令"""
    try:
        client = get_redis()
        print("=== Redis命令监控 (按Ctrl+C停止) ===")
        print("时间戳 | 数据库 | 客户端 | 命令")
        print("-" * 60)
        
        with client.monitor() as m:
            for command in m.listen():
                timestamp = datetime.now().strftime("%H:%M:%S")
                db = command.get('db', 0)
                client_info = command.get('client_info', 'unknown')
                cmd = command.get('command', '')
                
                print(f"{timestamp} | DB{db} | {client_info} | {cmd}")
                
    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        print(f"监控失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Redis管理命令行工具")
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # info命令
    subparsers.add_parser('info', help='显示Redis服务器信息')
    
    # keys命令
    keys_parser = subparsers.add_parser('keys', help='列出Redis键')
    keys_parser.add_argument('pattern', nargs='?', default='*', help='键模式 (默认: *)')
    keys_parser.add_argument('--limit', type=int, default=50, help='显示数量限制 (默认: 50)')
    
    # get命令
    get_parser = subparsers.add_parser('get', help='获取键值')
    get_parser.add_argument('key', help='键名')
    
    # delete命令
    delete_parser = subparsers.add_parser('delete', help='删除匹配的键')
    delete_parser.add_argument('pattern', help='键模式')
    
    # clear-auth命令
    subparsers.add_parser('clear-auth', help='清理认证相关数据')
    
    # monitor命令
    subparsers.add_parser('monitor', help='监控Redis命令')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'info':
            show_info()
        elif args.command == 'keys':
            list_keys(args.pattern, args.limit)
        elif args.command == 'get':
            get_key_value(args.key)
        elif args.command == 'delete':
            delete_keys(args.pattern)
        elif args.command == 'clear-auth':
            clear_auth_data()
        elif args.command == 'monitor':
            monitor_redis()
            
    except Exception as e:
        print(f"执行命令失败: {e}")


if __name__ == "__main__":
    main()
