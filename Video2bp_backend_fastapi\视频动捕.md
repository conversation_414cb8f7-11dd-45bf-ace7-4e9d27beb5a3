---
title: 视频动捕
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 视频动捕

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer<br/>用户认证Token

* API Key (cookieAuth)
    - Parameter Name: **sessionId**, in: cookie. HttpOnly Cookie 身份验证

# 视频模块

## POST 视频上传 /video/upload

POST /api/video/upload

上传视频并处理：首先显示上传进度条，上传完毕后提示上传完成；接着显示处理进度条，处理完毕后提示处理完成。
注意该接口与视频处理接口串联使用

> Body 请求参数

```yaml
fileData: file://C:\Users\<USER>\Downloads\trailer.mp4

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» fileData|body|string(binary)| 否 |视频文件|

> 返回示例

> 200 Response

```
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 上传查询 /video/upload/status

GET /api/video/upload/status

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|fileId|query|string| 否 |视频id|

> 返回示例

> 200 Response

```json
{
  "status": 0,
  "msg": "string",
  "data": {
    "video_id": "string",
    "status": "string",
    "video_web_path": "string",
    "video_file_path": "string",
    "json_web_path": "string",
    "json_file_path": "string"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» status|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|
|»» video_id|string|true|none|视频ID|视频ID|
|»» status|string|true|none|上传状态|上传状态|
|»» video_web_path|string|true|none|视频网站路径|视频网站路径|
|»» video_file_path|string|true|none|视频服务器路径|视频服务器路径|
|»» json_web_path|string|true|none|骨骼点网站路径|骨骼点网站路径|
|»» json_file_path|string|true|none|骨骼点服务器路径|骨骼点服务器路径|

## POST 视频处理 /video/process

POST /video/process

> Body 请求参数

```yaml
video_id: 337fbf3c-2cd0-4fc9-8686-c981ceb5e578

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» video_id|body|string| 否 |视频id|

> 返回示例

> 200 Response

```json
{
  "status": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» status|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

## GET 处理查询 /video/process_status

GET /video/process_status

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 视频列表查询 /video/list

POST /api/video/list

> Body 请求参数

```json
{
  "pageSize": 10,
  "startPage": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1,
    "videoList": [
      {
        "videoId": 1,
        "filename": "9e0292ff469311f0a92fe41fd5a46982.mp4",
        "bpFilename": "",
        "createdAt": "2025-06-11 15:13:47",
        "updatedAt": "2025-06-11 15:13:47",
        "status": 1
      }
    ]
  },
  "timestamp": 1749626818335
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 视频下载 /video/download_video

GET /api/video/download

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|videoId|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 骨骼点下载 /video/download_bp

GET /api/bp/download

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|videoId|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 打包下载 /video/download_all

GET /api/video/download/all

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|videoId|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 视频分片上传 /video/upload_chunk

POST /api/video/upload_chunk

上传视频并处理：首先显示上传进度条，上传完毕后提示上传完成；接着显示处理进度条，处理完毕后提示处理完成。
注意该接口与视频处理接口串联使用

> Body 请求参数

```yaml
chunk: file://C:\Users\<USER>\Downloads\trailer.mp4
chunkNumber: "1"
totalChunks: ""
fileId: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» chunk|body|string(binary)| 否 |视频文件|
|» chunkNumber|body|string| 否 |none|
|» totalChunks|body|string| 否 |none|
|» fileId|body|string| 否 |none|

> 返回示例

> 200 Response

```
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 合并分片 /video/merge_chunks

POST /api/video/merge_chunks

上传视频并处理：首先显示上传进度条，上传完毕后提示上传完成；接着显示处理进度条，处理完毕后提示处理完成。
注意该接口与视频处理接口串联使用

> Body 请求参数

```yaml
fileId: file://C:\Users\<USER>\Downloads\trailer.mp4
filename: "1"

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» fileId|body|string(binary)| 否 |视频文件|
|» filename|body|string| 否 |none|

> 返回示例

> 200 Response

```
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 视频上传

POST /api/video/simple-upload

上传视频文件并获取视频ID和访问URL，支持上传进度监听

> Body 请求参数

```yaml
video: ""
filename: my_video.mp4

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» video|body|string(binary)| 是 |视频文件，支持格式：|
|» filename|body|string| 是 |文件名称|

#### 详细说明

**» video**: 视频文件，支持格式：
- mp4 (推荐)
- avi
- mov
- wmv
- flv
- mkv

限制：
- 文件大小：建议不超过500MB
- 时长：建议不超过30分钟

> 返回示例

> 200 Response

```json
{
  "id": "video_123456789",
  "url": "https://example.com/videos/video_123456789.mp4"
}
```

> 请求参数错误

```json
{
  "code": 400,
  "message": "文件格式不支持，仅支持mp4, avi, mov, wmv, flv, mkv格式",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "文件大小超过限制，建议不超过500MB",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "视频时长超过限制，建议不超过30分钟",
  "data": null
}
```

> 401 Response

```json
{
  "code": 401,
  "message": "未授权，需要登录",
  "data": null
}
```

> 413 Response

```json
{
  "code": 413,
  "message": "文件过大",
  "data": null
}
```

> 415 Response

```json
{
  "code": 415,
  "message": "不支持的媒体类型",
  "data": null
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|视频上传成功|[VideoUploadResponse](#schemavideouploadresponse)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权，需要登录|[ErrorResponse](#schemaerrorresponse)|
|413|[Payload Too Large](https://tools.ietf.org/html/rfc7231#section-6.5.11)|文件过大|[ErrorResponse](#schemaerrorresponse)|
|415|[Unsupported Media Type](https://tools.ietf.org/html/rfc7231#section-6.5.13)|不支持的媒体类型|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

## POST 创建文件记录

POST /api/video/create-file

基于已上传的视频创建文件记录，支持视频裁剪和格式转换配置

> Body 请求参数

```json
{
  "id": "video_123456789",
  "videoUrl": "https://example.com/videos/video_123456789.mp4",
  "startTime": 0,
  "endTime": 30.5,
  "duration": 30.5,
  "wasTrimmed": true,
  "originalSelection": {
    "start": 10.2,
    "end": 40.7,
    "duration": 30.5
  },
  "formData": {
    "title": "我的视频项目",
    "description": "项目描述"
  },
  "outputFormat": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[CreateFileRequest](#schemacreatefilerequest)| 否 |none|

> 返回示例

> 文件记录创建成功

```json
{
  "id": "file_987654321",
  "fileId": "project_abc123",
  "status": "created"
}
```

```json
{
  "id": "file_987654321",
  "fileId": "project_abc123",
  "status": "processing"
}
```

> 请求参数错误

```json
{
  "code": 400,
  "message": "请求参数错误，outputFormat必须为1-4之间的数字",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "时间范围错误，endTime必须大于startTime",
  "data": null
}
```

> 401 Response

```json
{
  "code": 401,
  "message": "未授权，需要登录",
  "data": null
}
```

> 404 Response

```json
{
  "code": 404,
  "message": "视频资源不存在，请先上传视频",
  "data": null
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|文件记录创建成功|[CreateFileResponse](#schemacreatefileresponse)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权，需要登录|[ErrorResponse](#schemaerrorresponse)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|视频资源不存在|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

# 订单模块

## POST 创建订单

POST /api/order/create

> 返回示例

> 200 Response

```json
{
  "status": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» status|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

# 首页模块

## POST 客户咨询 /consult

POST /consult

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 公司信息

GET /api/compony/info

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

## GET 套餐信息

GET /api/combo/info

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

## GET 资讯详情

GET /api/news/detail

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "newsList": [
      {
        "id": 1,
        "title": "V 1.3.2.0 新增防穿模功能、UE5和ROBLOX 格式支持等功能",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/9b9a3c13-a308-43c5-8c98-342f3d7749ba.webp",
        "author": "灵宇 AI 动捕",
        "excerpt": "新增防穿模功能、UE5和ROBLOX 格式支持等功能",
        "createdAt": "2025-06-05 00:00:00",
        "isPinned": true
      },
      {
        "id": 2,
        "title": "灵宇 AI 动捕如何在 3DMAX 中使用",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/29573e87-7922-45e7-a2a0-97581c8bcaa8.jpg",
        "author": "灵宇 AI 动捕",
        "excerpt": "此教程主要介绍灵宇 AI 动捕如何在 3DMAX 中使用，包含如何优化/修复动捕捕数据和如何重定向面捕数据。",
        "createdAt": "2025-05-13 00:00:00",
        "isPinned": false
      },
      {
        "id": 3,
        "title": "面捕数据如何在 Unity 中重定向",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/9400dfb7-27ef-40f9-99b6-1d2d52b8fc50.png",
        "author": "灵宇 AI 动捕",
        "excerpt": "此教程主要讲解灵宇 AI 动捕的面部捕捉数据如何在 Unity 中完成重定向。",
        "createdAt": "2025-04-14 00:00:00",
        "isPinned": false
      },
      {
        "id": 4,
        "title": "如何将灵宇动捕数据转为2D动画？",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/1de7c65e-b09b-4f76-b0c3-a43ed84068c8.png",
        "author": "灵宇 AI 动捕",
        "excerpt": "此教程主要演示如何将灵宇AI动捕数据转为 2D 动画。",
        "createdAt": "2025-04-02 00:00:00",
        "isPinned": false
      },
      {
        "id": 5,
        "title": "Blender 面捕数据重定向教程",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/740b987b-4ee1-4ed3-a83b-25e15630e075.jpg",
        "author": "灵宇 AI 动捕",
        "excerpt": "此教程主要介绍了两种面捕数据重定向方法，Onlyface 面捕数据也可以使用相同办法。",
        "createdAt": "2025-03-28 00:00:00",
        "isPinned": false
      },
      {
        "id": 6,
        "title": "图片如何变为带动作的 3D 模型？",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/de21bfc1-c4d1-4c1a-9b8a-41883684aff7.jpg",
        "author": "灵宇 AI 动捕",
        "excerpt": "此教程详细讲解如何将一张图片生成3D模型→绑定角色→灵宇 AI 动捕获得动作→动作重定向到我的角色",
        "createdAt": "2025-03-27 00:00:00",
        "isPinned": false
      },
      {
        "id": 7,
        "title": "灵宇 AI 动捕如何在 UE5 中使用",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/7af345f2-83c8-49c0-9f85-9c8bdc278388.jpg",
        "author": "灵宇 AI 动捕",
        "excerpt": "此教程主要介绍灵宇 Al 动捕如何在 UE5 中使用。",
        "createdAt": "2025-03-21 00:00:00",
        "isPinned": false
      },
      {
        "id": 8,
        "title": "灵宇 AI 动捕如何在 iClone 中使用",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/f48c1fac-988a-415d-bfb0-615cf275b8ef.jpg",
        "author": "灵宇 AI 动捕",
        "excerpt": "此教程主要介绍灵宇 AI 动捕如何在 iClone 中使用。",
        "createdAt": "2025-03-20 00:00:00",
        "isPinned": false
      },
      {
        "id": 9,
        "title": "灵宇 AI 动捕如何在 Cascadeur 中使用",
        "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/3f95fc51-902c-44d5-93f4-27a2e94b0a48.jpg",
        "author": "灵宇 AI 动捕",
        "excerpt": "此教程主要介绍灵宇 AI 动捕如何在 Cascadeur 中使用，包含如何优化/修复动捕数据。",
        "createdAt": "2025-03-20 00:00:00",
        "isPinned": false
      }
    ],
    "totalCount": 9
  },
  "timestamp": 1749695910457
}
```

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "V 1.3.2.0 新增防穿模功能、UE5和ROBLOX 格式支持等功能",
    "cover": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/9b9a3c13-a308-43c5-8c98-342f3d7749ba.webp",
    "author": "灵宇 AI 动捕",
    "content": "<p><span class=\"ql-size-large\">为了进一步提升您的使用体验，灵宇 AI 动捕进行了全新升级！以下是本次更新的详细内容：</span></p><p><br></p><h1><strong>一、新增功能</strong></h1><h2><strong>1. 核心功能升级</strong></h2><p><br></p><h2><strong>防穿模功能上线：初步实现模型穿模检测与优化，提升动画渲染稳定性。</strong></h2><h2 class=\"ql-indent-7\"><strong>防穿模功能展示：</strong></h2><p class=\"ql-indent-8\"><img src=\"https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/c5d7e5de-990b-4eb9-a671-7b7061dcd96c.gif\"></p><h2><strong>&nbsp;</strong></h2><h2><strong>UE5 格式支持：新增对 Unreal Engine 5 项目文件的兼容与导出能力,UE5格式目前处于测试版本，建议用于非生产环境</strong>。</h2><h2 class=\"ql-indent-7\"><strong>UE5 格式效果展示：</strong></h2><h2 class=\"ql-indent-5\"><strong>   &nbsp;</strong><img src=\"https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/78adbea5-4292-4a5c-9c6c-c6603ba4638a.gif\"></h2><h2><strong>&nbsp;</strong></h2><h2><strong>ROBLOX 格式支持：新增对 ROBLOX项目文件的兼容与导出能力，ROBLOX格式目前处于测试版本，建议用于非生产环境</strong>。</h2><p class=\"ql-indent-8\"><strong class=\"ql-size-large\">                  Roblox 格式效果展示：</strong></p><h2 class=\"ql-indent-5\">&nbsp;   <img src=\"https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/84140ab7-17aa-4a99-a581-a03ee7739f85.gif\"></h2><h2><strong>&nbsp;</strong></h2><h2><strong>2. </strong>.<strong>&nbsp;API&nbsp;功能升级</strong></h2><p><br></p><p><strong> </strong><span class=\"ql-size-large\">1.新增任务进度条</span></p><p><span class=\"ql-size-large\"> &nbsp;您会看到一个动态更新的进度条，该进度条会显示任务处理进度。</span></p><p><span class=\"ql-size-large\"> 2.新增任务成功/失败通知</span></p><p><span class=\"ql-size-large\"> &nbsp;视频任务完成后，您会接收到通知，告诉您任务是否制作成功</span></p><p><span class=\"ql-size-large\"> 3.消费记录查询</span></p><p><span class=\"ql-size-large\"> &nbsp;您可以通过账户页面查看消费记录，了解每次任务的费用和资源使用情况。</span></p><p><span class=\"ql-size-large\">4.人框检测（支持多人视频）</span></p><p><span class=\"ql-size-large\"> 上传一张图像后，系统会自动检测画面中的人体并以矩形框标记。您将收到系统自动检测后标示每个人的位置检测结果。</span></p><p><span class=\"ql-size-large\">5.API&nbsp;售价调整</span></p><p><br></p><h2><strong>3.支付系统调整</strong></h2><p><br></p><p><span class=\"ql-size-large\">根据诸多用户的反馈与需求，我们重新调整了一版支付计划并简化了开发票流程，需要详细了解新支付计划的用户可以通过官网进行查看</span></p><p><br></p><h2><strong>4.灵宇动捕移动端优化</strong></h2><p><br></p><p><span class=\"ql-size-large\">为了更加方便用户们的需求，灵宇&nbsp;AI&nbsp;动捕已适配移动端使用。</span></p><p><br></p><h1><strong>二、功能优化与问题修复</strong></h1><p><br></p><h2><strong>Web 端界面优化</strong></h2><p><br></p><p><span class=\"ql-size-large\">Web 首页添加 C4D、CC&amp;iClone 格式的图标</span></p><p><span class=\"ql-size-large\">支付计划相关美术优化</span></p><p><br></p><h2><strong>Web 端流程优化</strong></h2><p><br></p><p><span class=\"ql-size-large\">移除上传视频扫描角色按钮和扫描标记，目前版本扫描角色已自动化，上传视频即可自动扫描角色，并且可以通过点击格式、拖拽格式到目标人物、剪辑（剪辑后自动扫描）等系列操作完成自动扫描角色</span></p><p><span class=\"ql-size-large\">各格式默认勾选手捕功能</span></p><p><br></p><h2><strong>Web 端问题修复</strong></h2><p><br></p><p><span class=\"ql-size-large\">注册、登录页面的用户协议、隐私条款无法点击问题处理</span></p><p><span class=\"ql-size-large\">注销文字改为退出登录</span></p><p><span class=\"ql-size-large\">登录页去掉女性角色</span></p><p><span class=\"ql-size-large\">修复&nbsp;boy&nbsp;穿地BUG</span></p><p><span class=\"ql-size-large\">修复&nbsp;boy、girl&nbsp;手指旋转异常&nbsp;BUG</span></p><p><span class=\"ql-size-large\">修复&nbsp;only&nbsp;face&nbsp;大小眼BUG</span></p><p><span class=\"ql-size-large\">修复 Unity WebGL 内存泄漏问题，提升前端稳定性</span></p><p><span class=\"ql-size-large\">修复制作失败未返还 ∨ 币、疑似盗刷订单等问题</span></p><p><span class=\"ql-size-large\">修复 IP 与地区显示异常、VMD 面捕文件缺失，企业用户制作 0nlface 格式后台无法</span></p><p><span class=\"ql-size-large\">预览，普通账号月初未刷新 50V 币等 BUG。</span></p><p><span class=\"ql-size-large\">PDF导出 BUG 修复</span></p><h1><br></h1><h1><strong>三、其他说明</strong></h1><p><br></p><p><span class=\"ql-size-large\">兼容性提醒：部分 UI 调整可能影响旧版本缓存，建议更新后清除浏览器缓存或重启应用。</span></p><p><span class=\"ql-size-large\">&nbsp;</span></p><p><span class=\"ql-size-large\">&nbsp;</span></p><h1><strong>感谢您对产品的支持！</strong></h1><p><br></p><p><span class=\"ql-size-large\">本次更新中 \"UE5 格式\" 标注为测试版（beta），欢迎用户体验并反馈建议。</span></p><p><span class=\"ql-size-large\">若遇到任何问题或有功能需求，可联系我们团队（<EMAIL>）。</span></p><p><span class=\"ql-size-large\">我们将持续优化产品体验，后续版本将推出更多动捕功能与性能升级，敬请期待！</span></p><p class=\"ql-align-justify\"><span class=\"ql-size-large\">如需进一步调整风格（如更正式、更简洁等），请随时告知！</span></p>",
    "excerpt": "新增防穿模功能、UE5和ROBLOX 格式支持等功能",
    "createdAt": "2025-06-05 00:00:00",
    "isPinned": true,
    "profilePicture": "https://ai2022-1253391138.cos.ap-shanghai.myqcloud.com/news/1e21ec3a-f9de-4510-bbd4-70f123feb73a.webp",
    "tag": null,
    "type": 3
  },
  "timestamp": 1749697700160
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

# 用户模块

## POST 登录

POST /api/user/login

### 用户登录

| 登录成功 | 登录失败 |
| --- | --- |
| status 200 | status 401 |
| data=/user/info | data {} |

> Body 请求参数

```json
{
  "email": "<EMAIL>",
  "password": "1234567"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

## POST 注册

POST /api/user/register

### 用户注册

| 注册成功 | 注册失败 |
| --- | --- |
| status 200 | status 401 |
| data=/user/info | data {} |

> Body 请求参数

```json
{
  "email": "<EMAIL>",
  "password": "1234567",
  "code": "565611"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "success",
  "data": {},
  "timestamp": 1749620105597
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

## POST 发送验证码

POST /api/user/send_verification_code

### 发送验证码

| 发送验证码成功 | 发送验证码失败 |
| --- | --- |
| status 200 | status 401 |
| data=/user/info | data {} |

> Body 请求参数

```json
{
  "email": "<EMAIL>"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "success",
  "data": {},
  "timestamp": 1749614377780
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

## POST 获取用户信息

POST /api/user/info

### 获取用户信息

| 获取用户信息成功 | 获取用户信息失败 |
| --- | --- |
| status 200 | status 401 |
| data=/user/info | data {} |

> Body 请求参数

```json
{
  "userId": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Content-Type|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {},
  "timestamp": 1749620105597
}
```

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "username": "",
    "email": "<EMAIL>",
    "phone": "",
    "status": 1,
    "vipLevel": "1"
  },
  "timestamp": 1749622448094
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|状态|http status code|
|» msg|string|true|none|消息|用来前端展示的消息，需要前端支持 \n 换行|
|» data|object|true|none|数据|数据段|

## GET 获取用户信息

GET /api/user/info

获取当前登录用户的详细信息

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "12345",
    "username": "用户名",
    "email": "<EMAIL>",
    "role": "user",
    "avatar": "https://example.com/avatar.jpg",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "balance": 100.5,
    "vipLevel": 2,
    "vipDeadline": "2024-12-31T23:59:59Z"
  }
}
```

> 403 Response

```json
{
  "code": 403,
  "message": "用户未登录",
  "data": null
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取用户信息|[UserInfoResponse](#schemauserinforesponse)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|用户未登录|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

## POST 修改密码

POST /api/user/change-password

用户修改密码接口，需要验证当前密码

> Body 请求参数

```json
{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword456"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[ChangePasswordRequest](#schemachangepasswordrequest)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "密码修改成功",
  "data": {}
}
```

> 400 Response

```json
{
  "code": 400,
  "message": "新密码长度必须在6-20位之间",
  "data": null
}
```

> 401 Response

```json
{
  "code": 401,
  "message": "当前密码错误",
  "data": null
}
```

> 403 Response

```json
{
  "code": 403,
  "message": "用户未登录",
  "data": null
}
```

> 429 Response

```json
{
  "code": 429,
  "message": "修改密码过于频繁，请5分钟后再试",
  "data": null
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|密码修改成功|[SuccessResponse](#schemasuccessresponse)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|当前密码错误|[ErrorResponse](#schemaerrorresponse)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|用户未登录|[ErrorResponse](#schemaerrorresponse)|
|429|[Too Many Requests](https://tools.ietf.org/html/rfc6585#section-4)|请求频率限制|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

## PUT 更新用户信息

PUT /api/user/update

更新用户信息，部分字段需要管理员权限

> Body 请求参数

```json
{
  "username": "新用户名",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[UpdateUserRequest](#schemaupdateuserrequest)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "用户信息更新成功",
  "data": {}
}
```

> 400 Response

```json
{
  "code": 400,
  "message": "用户名已存在",
  "data": null
}
```

> 用户未登录或权限不足

```json
{
  "code": 403,
  "message": "用户未登录",
  "data": null
}
```

```json
{
  "code": 403,
  "message": "权限不足，无法修改VIP相关信息",
  "data": null
}
```

> 500 Response

```json
{
  "code": 0,
  "message": "string",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|用户信息更新成功|[SuccessResponse](#schemasuccessresponse)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误或用户名已存在|[ErrorResponse](#schemaerrorresponse)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|用户未登录或权限不足|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

## POST 获取用户订单记录

POST /api/user/order-records

分页获取当前登录用户的订单记录，按扣费时间倒序排列

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 10
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[OrderRecordsRequest](#schemaorderrecordsrequest)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "data": [
      {
        "id": "order_001",
        "type": "基础套餐",
        "time": "2024-01-15",
        "amount": "99.00",
        "method": "支付宝",
        "toAccount": "1500.50"
      },
      {
        "id": "order_002",
        "type": "高级套餐",
        "time": "2024-01-10",
        "amount": "199.00",
        "method": "微信支付",
        "toAccount": "1401.50"
      }
    ],
    "total": 25,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

> 请求参数错误

```json
{
  "code": 400,
  "message": "pageNum必须大于0",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "pageSize必须在5-50之间",
  "data": null
}
```

> 403 Response

```json
{
  "code": 403,
  "message": "用户未登录",
  "data": null
}
```

> 500 Response

```json
{
  "code": 500,
  "message": "服务器内部错误，请稍后重试",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取订单记录|[OrderRecordsResponse](#schemaorderrecordsresponse)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|用户未登录|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

# 业务模块

## POST 获取业务轮播图列表

POST /api/business/list

获取所有业务数据，无需参数

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "AI视频处理解决方案",
      "image": "https://example.com/business1.jpg",
      "details": [
        "解决方案技术支持",
        "应用支持服务",
        "产品功能演示讲解",
        "产品部署与接入调试指南"
      ]
    }
  ]
}
```

```json
{
  "code": 1,
  "message": "获取业务列表失败",
  "data": []
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功响应|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||响应码，0表示成功，1表示失败|
|» message|string|false|none||响应消息|
|» data|[[Business](#schemabusiness)]|false|none||none|
|»» id|integer|true|none||业务ID|
|»» title|string|true|none||业务标题|
|»» image|string(uri)|true|none||业务图片URL|
|»» details|[string]|true|none||业务详情列表|

## POST 获取产品列表

POST /api/business/products

获取所有产品数据，无需参数

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "单/多人捕捉",
      "description": "在动作捕捉的世界中，首帧姿势是一切的开始。自定义首帧姿势，赋予您无限的创造自由。",
      "videoUrl": "https://example.com/videos/demo1.mp4"
    }
  ]
}
```

```json
{
  "code": 1,
  "message": "获取产品列表失败",
  "data": []
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功响应|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||响应码，0表示成功，1表示失败|
|» message|string|false|none||响应消息|
|» data|[[Product](#schemaproduct)]|false|none||none|
|»» id|integer|true|none||产品ID|
|»» title|string|true|none||产品标题|
|»» description|string|true|none||产品描述|
|»» videoUrl|string(uri)|true|none||产品演示视频URL|

## POST 获取用户案例列表

POST /business/user-cases

获取所有用户案例数据，无需参数

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "Elva Cruz",
      "avatar": "https://example.com/avatars/user1.jpg",
      "time": "10:30w",
      "content": "很喜欢这首歌，但找了半天发现没人配布这首歌的动作，只能我自己出手了...",
      "preview": "https://example.com/videos/case1.mp4"
    }
  ]
}
```

```json
{
  "code": 1,
  "message": "获取用户案例列表失败",
  "data": []
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功响应|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||响应码，0表示成功，1表示失败|
|» message|string|false|none||响应消息|
|» data|[[UserCase](#schemausercase)]|false|none||none|
|»» id|integer|true|none||案例ID|
|»» name|string|true|none||用户姓名|
|»» avatar|string(uri)|true|none||用户头像URL|
|»» time|string|true|none||时间标识|
|»» content|string|true|none||案例内容描述|
|»» preview|string(uri)|true|none||案例预览视频URL|

## POST 提交联系表单

POST /business/contact

提交用户联系信息和咨询项目

> Body 请求参数

```json
{
  "phone": "***********",
  "companyName": "示例科技有限公司",
  "project": "AI视频处理解决方案"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[ContactRequest](#schemacontactrequest)| 否 |none|

> 返回示例

> 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

```json
{
  "code": 1,
  "message": "提交联系表单失败",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功响应|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||响应码，0表示成功，1表示失败|
|» message|string|false|none||响应消息|
|» data|object|false|none||响应数据|

# 公司动态模块

## POST 获取新闻列表

POST /api/news/list

根据分页参数和分类获取新闻列表数据

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 9,
  "category": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[NewsListRequest](#schemanewslistrequest)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "title": "企业最新动态",
        "image": "https://example.com/news1.jpg",
        "date": "2024-01-15",
        "author": "官方",
        "authorAvatar": "https://example.com/avatar1.jpg",
        "tag": "官方新闻"
      }
    ],
    "total": 25,
    "pageNum": 1,
    "pageSize": 9
  }
}
```

> 400 Response

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null
}
```

> 401 Response

```json
{
  "code": 401,
  "message": "未授权，需要登录",
  "data": null
}
```

> 500 Response

```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功响应|[NewsListResponse](#schemanewslistresponse)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权，需要登录|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

# 我的项目模块

## POST 获取项目列表

POST /api/projects/list

根据分页参数获取项目列表数据，支持4列布局展示

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 8
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[ProjectListRequest](#schemaprojectlistrequest)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "舞蹈项目",
        "image": "https://example.com/project1.jpg",
        "fbxUrl": "https://example.com/models/project1.fbx",
        "videoUrl": "https://example.com/videos/project1.mp4",
        "createdAt": "2024-01-15",
        "updatedAt": "2024-01-15",
        "status": "completed"
      },
      {
        "id": 2,
        "name": "音乐项目",
        "image": "https://example.com/project2.jpg",
        "fbxUrl": "https://example.com/models/project2.fbx",
        "videoUrl": "https://example.com/videos/project2.mp4",
        "createdAt": "2024-01-16",
        "updatedAt": "2024-01-16",
        "status": "in_progress"
      }
    ],
    "total": 25,
    "pageNum": 1,
    "pageSize": 8
  }
}
```

> 400 Response

```json
{
  "code": 400,
  "message": "参数错误：pageNum必须大于0"
}
```

> 401 Response

```json
{
  "code": 401,
  "message": "用户未登录或登录已过期"
}
```

> 500 Response

```json
{
  "code": 500,
  "message": "服务器内部错误，请稍后重试"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功响应|[ProjectListResponse](#schemaprojectlistresponse)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未授权访问|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

# 项目资源模块

## POST 获取资源列表

POST /api/resources/list

根据分页参数和搜索关键词获取资源列表数据，支持4列布局展示

> Body 请求参数

```json
{
  "pageNum": 1,
  "pageSize": 12
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[ResourceListRequest](#schemaresourcelistrequest)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "舞蹈资源包",
        "image": "https://example.com/resource1.jpg",
        "downloadUrl": "https://example.com/downloads/resource1.zip",
        "fileSize": "25.6MB",
        "fileType": "zip",
        "createdAt": "2024-01-15",
        "updatedAt": "2024-01-15",
        "downloadCount": 1250,
        "description": "包含多种舞蹈动作的资源包"
      },
      {
        "id": 2,
        "name": "音乐资源包",
        "image": "https://example.com/resource2.jpg",
        "downloadUrl": "https://example.com/downloads/resource2.rar",
        "fileSize": "18.3MB",
        "fileType": "rar",
        "createdAt": "2024-01-16",
        "updatedAt": "2024-01-16",
        "downloadCount": 890,
        "description": "高质量音乐素材合集"
      }
    ],
    "total": 156,
    "pageNum": 1,
    "pageSize": 12
  }
}
```

> 400 Response

```json
{
  "code": 400,
  "message": "参数错误：pageNum必须大于0"
}
```

> 500 Response

```json
{
  "code": 500,
  "message": "服务器内部错误，请稍后重试"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功响应|[ResourceListResponse](#schemaresourcelistresponse)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[ErrorResponse](#schemaerrorresponse)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[ErrorResponse](#schemaerrorresponse)|

# 数据模型

<h2 id="tocS_Business">Business</h2>

<a id="schemabusiness"></a>
<a id="schema_Business"></a>
<a id="tocSbusiness"></a>
<a id="tocsbusiness"></a>

```json
{
  "id": 1,
  "title": "AI视频处理解决方案",
  "image": "https://example.com/business1.jpg",
  "details": [
    "解决方案技术支持",
    "应用支持服务",
    "产品功能演示讲解",
    "产品部署与接入调试指南"
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||业务ID|
|title|string|true|none||业务标题|
|image|string(uri)|true|none||业务图片URL|
|details|[string]|true|none||业务详情列表|

<h2 id="tocS_NewsListRequest">NewsListRequest</h2>

<a id="schemanewslistrequest"></a>
<a id="schema_NewsListRequest"></a>
<a id="tocSnewslistrequest"></a>
<a id="tocsnewslistrequest"></a>

```json
{
  "pageNum": 1,
  "pageSize": 9,
  "category": 1
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|true|none||当前页码（从1开始）|
|pageSize|integer|true|none||每页数量（建议9条）|
|category|integer|true|none||新闻分类：<br />- 1: 所有<br />- 2: 官方新闻<br />- 3: 实用教程<br />- 4: 体验分享<br />- 5: 更新公告|

#### 枚举值

|属性|值|
|---|---|
|category|1|
|category|2|
|category|3|
|category|4|
|category|5|

<h2 id="tocS_ProjectListRequest">ProjectListRequest</h2>

<a id="schemaprojectlistrequest"></a>
<a id="schema_ProjectListRequest"></a>
<a id="tocSprojectlistrequest"></a>
<a id="tocsprojectlistrequest"></a>

```json
{
  "pageNum": 1,
  "pageSize": 8
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|true|none||当前页码（从1开始）|
|pageSize|integer|true|none||每页数量（建议8条，适配4列布局）|

<h2 id="tocS_ResourceListRequest">ResourceListRequest</h2>

<a id="schemaresourcelistrequest"></a>
<a id="schema_ResourceListRequest"></a>
<a id="tocSresourcelistrequest"></a>
<a id="tocsresourcelistrequest"></a>

```json
{
  "pageNum": 1,
  "pageSize": 12,
  "keyword": "舞蹈"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|true|none||当前页码（从1开始）|
|pageSize|integer|true|none||每页数量（建议12条，适配4列布局时建议8的倍数）|
|keyword|string|false|none||搜索关键词，支持模糊搜索资源名称和描述|

<h2 id="tocS_ChangePasswordRequest">ChangePasswordRequest</h2>

<a id="schemachangepasswordrequest"></a>
<a id="schema_ChangePasswordRequest"></a>
<a id="tocSchangepasswordrequest"></a>
<a id="tocschangepasswordrequest"></a>

```json
{
  "currentPassword": "oldPassword123",
  "newPassword": "newPassword456"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|currentPassword|string|true|none||当前密码|
|newPassword|string|true|none||新密码（6-20位，建议包含字母、数字和特殊字符）|

<h2 id="tocS_OrderRecordsRequest">OrderRecordsRequest</h2>

<a id="schemaorderrecordsrequest"></a>
<a id="schema_OrderRecordsRequest"></a>
<a id="tocSorderrecordsrequest"></a>
<a id="tocsorderrecordsrequest"></a>

```json
{
  "pageNum": 1,
  "pageSize": 10
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|pageNum|integer|true|none||当前页码（从1开始）|
|pageSize|integer|true|none||每页数量（建议10条，范围5-50）|

<h2 id="tocS_VideoUploadRequest">VideoUploadRequest</h2>

<a id="schemavideouploadrequest"></a>
<a id="schema_VideoUploadRequest"></a>
<a id="tocSvideouploadrequest"></a>
<a id="tocsvideouploadrequest"></a>

```json
{
  "video": "string",
  "filename": "my_video.mp4"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|video|string(binary)|true|none||视频文件，支持格式：<br />- mp4 (推荐)<br />- avi<br />- mov<br />- wmv<br />- flv<br />- mkv<br /><br />限制：<br />- 文件大小：建议不超过500MB<br />- 时长：建议不超过30分钟|
|filename|string|true|none||文件名称|

<h2 id="tocS_Product">Product</h2>

<a id="schemaproduct"></a>
<a id="schema_Product"></a>
<a id="tocSproduct"></a>
<a id="tocsproduct"></a>

```json
{
  "id": 1,
  "title": "单/多人捕捉",
  "description": "在动作捕捉的世界中，首帧姿势是一切的开始。自定义首帧姿势，赋予您无限的创造自由。",
  "videoUrl": "https://example.com/videos/demo1.mp4"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||产品ID|
|title|string|true|none||产品标题|
|description|string|true|none||产品描述|
|videoUrl|string(uri)|true|none||产品演示视频URL|

<h2 id="tocS_NewsListResponse">NewsListResponse</h2>

<a id="schemanewslistresponse"></a>
<a id="schema_NewsListResponse"></a>
<a id="tocSnewslistresponse"></a>
<a id="tocsnewslistresponse"></a>

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "title": "企业最新动态",
        "image": "https://example.com/news1.jpg",
        "date": "2024-01-15",
        "author": "官方",
        "authorAvatar": "https://example.com/avatar1.jpg",
        "tag": "官方新闻"
      }
    ],
    "total": 25,
    "pageNum": 1,
    "pageSize": 9
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||状态码（0表示成功）|
|message|string|false|none||响应消息|
|data|object|false|none||none|
|» data|[[NewsItem](#schemanewsitem)]|false|none||新闻列表数组|
|» total|integer|false|none||总记录数|
|» pageNum|integer|false|none||当前页码|
|» pageSize|integer|false|none||每页数量|

<h2 id="tocS_ProjectListResponse">ProjectListResponse</h2>

<a id="schemaprojectlistresponse"></a>
<a id="schema_ProjectListResponse"></a>
<a id="tocSprojectlistresponse"></a>
<a id="tocsprojectlistresponse"></a>

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "舞蹈项目",
        "image": "https://example.com/project1.jpg",
        "fbxUrl": "https://example.com/models/project1.fbx",
        "videoUrl": "https://example.com/videos/project1.mp4",
        "createdAt": "2024-01-15",
        "updatedAt": "2024-01-15",
        "status": "completed"
      }
    ],
    "total": 25,
    "pageNum": 1,
    "pageSize": 8
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||状态码（0表示成功）|
|message|string|false|none||响应消息|
|data|object|false|none||none|
|» data|[[ProjectItem](#schemaprojectitem)]|false|none||项目列表数组|
|» total|integer|false|none||总记录数|
|» pageNum|integer|false|none||当前页码|
|» pageSize|integer|false|none||每页数量|

<h2 id="tocS_ResourceListResponse">ResourceListResponse</h2>

<a id="schemaresourcelistresponse"></a>
<a id="schema_ResourceListResponse"></a>
<a id="tocSresourcelistresponse"></a>
<a id="tocsresourcelistresponse"></a>

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "舞蹈资源包",
        "image": "https://example.com/resource1.jpg",
        "downloadUrl": "https://example.com/downloads/resource1.zip",
        "fileSize": "25.6MB",
        "fileType": "zip",
        "createdAt": "2024-01-15",
        "updatedAt": "2024-01-15",
        "downloadCount": 1250,
        "description": "包含多种舞蹈动作的资源包"
      }
    ],
    "total": 156,
    "pageNum": 1,
    "pageSize": 12
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||状态码（0表示成功）|
|message|string|false|none||响应消息|
|data|object|false|none||none|
|» data|[[ResourceItem](#schemaresourceitem)]|false|none||资源列表数组|
|» total|integer|false|none||总记录数|
|» pageNum|integer|false|none||当前页码|
|» pageSize|integer|false|none||每页数量|

<h2 id="tocS_UpdateUserRequest">UpdateUserRequest</h2>

<a id="schemaupdateuserrequest"></a>
<a id="schema_UpdateUserRequest"></a>
<a id="tocSupdateuserrequest"></a>
<a id="tocsupdateuserrequest"></a>

```json
{
  "username": "新用户名",
  "avatar": "https://example.com/new-avatar.jpg",
  "balance": 200,
  "vipLevel": 3,
  "vipDeadline": "2025-12-31T23:59:59Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|username|string|false|none||用户名|
|avatar|string(uri)¦null|false|none||头像URL|
|balance|number(double)|false|none||账户余额（管理员权限，单位：元，支持小数点后两位）|
|vipLevel|integer|false|none||VIP等级（管理员权限）：<br />- 0: 普通用户<br />- 1-5: VIP等级（1最低，5最高）|
|vipDeadline|string(date-time)|false|none||VIP到期时间（管理员权限，ISO 8601格式，UTC时区）|

<h2 id="tocS_OrderRecordsResponse">OrderRecordsResponse</h2>

<a id="schemaorderrecordsresponse"></a>
<a id="schema_OrderRecordsResponse"></a>
<a id="tocSorderrecordsresponse"></a>
<a id="tocsorderrecordsresponse"></a>

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "data": [
      {
        "id": "order_001",
        "type": "基础套餐",
        "time": "2024-01-15",
        "amount": "99.00",
        "method": "支付宝",
        "toAccount": "1500.50"
      }
    ],
    "total": 25,
    "pageNum": 1,
    "pageSize": 10
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||状态码（0表示成功）|
|message|string|false|none||响应消息|
|data|object|false|none||none|
|» data|[[OrderRecord](#schemaorderrecord)]|false|none||当前页的订单记录列表|
|» total|integer|false|none||总记录数|
|» pageNum|integer|false|none||当前页码|
|» pageSize|integer|false|none||每页数量|

<h2 id="tocS_VideoUploadResponse">VideoUploadResponse</h2>

<a id="schemavideouploadresponse"></a>
<a id="schema_VideoUploadResponse"></a>
<a id="tocSvideouploadresponse"></a>
<a id="tocsvideouploadresponse"></a>

```json
{
  "id": "video_123456789",
  "url": "https://example.com/videos/video_123456789.mp4"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none||视频唯一标识ID|
|url|string(uri)|true|none||视频访问URL|

<h2 id="tocS_UserCase">UserCase</h2>

<a id="schemausercase"></a>
<a id="schema_UserCase"></a>
<a id="tocSusercase"></a>
<a id="tocsusercase"></a>

```json
{
  "id": 1,
  "name": "Elva Cruz",
  "avatar": "https://example.com/avatars/user1.jpg",
  "time": "10:30w",
  "content": "很喜欢这首歌，但找了半天发现没人配布这首歌的动作，只能我自己出手了...",
  "preview": "https://example.com/videos/case1.mp4"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||案例ID|
|name|string|true|none||用户姓名|
|avatar|string(uri)|true|none||用户头像URL|
|time|string|true|none||时间标识|
|content|string|true|none||案例内容描述|
|preview|string(uri)|true|none||案例预览视频URL|

<h2 id="tocS_NewsItem">NewsItem</h2>

<a id="schemanewsitem"></a>
<a id="schema_NewsItem"></a>
<a id="tocSnewsitem"></a>
<a id="tocsnewsitem"></a>

```json
{
  "id": 1,
  "title": "企业最新动态",
  "image": "https://example.com/news1.jpg",
  "date": "2024-01-15",
  "author": "官方",
  "authorAvatar": "https://example.com/avatar1.jpg",
  "tag": "官方新闻"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||新闻ID|
|title|string|true|none||新闻标题|
|image|string(uri)|true|none||封面图片URL|
|date|string(date)|true|none||发布日期（格式：YYYY-MM-DD）|
|author|string|true|none||作者名称|
|authorAvatar|string(uri)|false|none||作者头像URL（可选）|
|tag|string|true|none||新闻标签|

<h2 id="tocS_ProjectItem">ProjectItem</h2>

<a id="schemaprojectitem"></a>
<a id="schema_ProjectItem"></a>
<a id="tocSprojectitem"></a>
<a id="tocsprojectitem"></a>

```json
{
  "id": 1,
  "name": "舞蹈项目",
  "image": "https://example.com/project1.jpg",
  "fbxUrl": "https://example.com/models/project1.fbx",
  "videoUrl": "https://example.com/videos/project1.mp4",
  "createdAt": "2024-01-15",
  "updatedAt": "2024-01-15",
  "status": "completed"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|false|none||项目ID|
|name|string|false|none||项目名称|
|image|string(uri)|false|none||封面图片完整URL地址|
|fbxUrl|string(uri)|false|none||FBX模型文件完整URL地址|
|videoUrl|string(uri)|false|none||视频文件完整URL地址|
|createdAt|string(date)|false|none||创建时间（格式：YYYY-MM-DD）|
|updatedAt|string(date)|false|none||更新时间（格式：YYYY-MM-DD）|
|status|string|false|none||项目状态：<br />- in_progress: 制作中<br />- completed: 已完成|

#### 枚举值

|属性|值|
|---|---|
|status|in_progress|
|status|completed|

<h2 id="tocS_ResourceItem">ResourceItem</h2>

<a id="schemaresourceitem"></a>
<a id="schema_ResourceItem"></a>
<a id="tocSresourceitem"></a>
<a id="tocsresourceitem"></a>

```json
{
  "id": 1,
  "name": "舞蹈资源包",
  "image": "https://example.com/resource1.jpg",
  "downloadUrl": "https://example.com/downloads/resource1.zip",
  "fileSize": "25.6MB",
  "fileType": "zip",
  "createdAt": "2024-01-15",
  "updatedAt": "2024-01-15",
  "downloadCount": 1250,
  "description": "包含多种舞蹈动作的资源包"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer|true|none||资源ID|
|name|string|true|none||资源名称|
|image|string(uri)|true|none||封面图片完整URL地址|
|downloadUrl|string(uri)¦null|false|none||下载链接URL（为空时不显示下载按钮）|
|fileSize|string¦null|false|none||文件大小|
|fileType|string¦null|false|none||文件类型，支持的压缩格式：<br />- zip: ZIP压缩包<br />- rar: RAR压缩包<br />- 7z: 7Z压缩包|
|createdAt|string(date)¦null|false|none||创建时间（格式：YYYY-MM-DD）|
|updatedAt|string(date)¦null|false|none||更新时间（格式：YYYY-MM-DD）|
|downloadCount|integer¦null|false|none||下载次数|
|description|string¦null|false|none||资源描述|

#### 枚举值

|属性|值|
|---|---|
|fileType|zip|
|fileType|rar|
|fileType|7z|

<h2 id="tocS_UserInfoResponse">UserInfoResponse</h2>

<a id="schemauserinforesponse"></a>
<a id="schema_UserInfoResponse"></a>
<a id="tocSuserinforesponse"></a>
<a id="tocsuserinforesponse"></a>

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "12345",
    "username": "用户名",
    "email": "<EMAIL>",
    "role": "user",
    "avatar": "https://example.com/avatar.jpg",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z",
    "balance": 100.5,
    "vipLevel": 2,
    "vipDeadline": "2024-12-31T23:59:59Z"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||状态码（0表示成功）|
|message|string|false|none||响应消息|
|data|[UserInfo](#schemauserinfo)|false|none||none|

<h2 id="tocS_OrderRecord">OrderRecord</h2>

<a id="schemaorderrecord"></a>
<a id="schema_OrderRecord"></a>
<a id="tocSorderrecord"></a>
<a id="tocsorderrecord"></a>

```json
{
  "id": "order_001",
  "type": "基础套餐",
  "time": "2024-01-15",
  "amount": "99.00",
  "method": "支付宝",
  "toAccount": "1500.50"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none||订单唯一标识|
|type|string|true|none||套餐类型：<br />- 基础套餐: 入门级服务套餐<br />- 高级套餐: 进阶服务套餐<br />- 专业套餐: 专业级服务套餐<br />- VIP套餐: 顶级VIP服务套餐|
|time|string(date)|true|none||扣费时间（YYYY-MM-DD格式）|
|amount|string|true|none||订单金额（单位：元，保留两位小数的字符串格式）|
|method|string|true|none||支付方式：<br />- 支付宝: 支付宝在线支付<br />- 微信支付: 微信在线支付<br />- 银行卡: 银行卡支付<br />- 余额支付: 账户余额支付|
|toAccount|string|true|none||支付后的账户余额（单位：元，保留两位小数的字符串格式）|

#### 枚举值

|属性|值|
|---|---|
|type|基础套餐|
|type|高级套餐|
|type|专业套餐|
|type|VIP套餐|
|method|支付宝|
|method|微信支付|
|method|银行卡|
|method|余额支付|

<h2 id="tocS_CreateFileRequest">CreateFileRequest</h2>

<a id="schemacreatefilerequest"></a>
<a id="schema_CreateFileRequest"></a>
<a id="tocScreatefilerequest"></a>
<a id="tocscreatefilerequest"></a>

```json
{
  "id": "video_123456789",
  "videoUrl": "https://example.com/videos/video_123456789.mp4",
  "startTime": 0,
  "endTime": 30.5,
  "duration": 30.5,
  "wasTrimmed": true,
  "originalSelection": {
    "start": 10.2,
    "end": 40.7,
    "duration": 30.5
  },
  "formData": {
    "title": "我的视频项目",
    "description": "项目描述"
  },
  "outputFormat": 1
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|true|none||视频上传后返回的ID|
|videoUrl|string(uri)|true|none||视频URL地址|
|startTime|number(double)|false|none||开始时间（秒，支持小数）|
|endTime|number(double)|false|none||结束时间（秒，支持小数）|
|duration|number(double)|false|none||持续时间（秒，支持小数）|
|wasTrimmed|boolean|false|none||是否进行了裁剪|
|originalSelection|[OriginalSelection](#schemaoriginalselection)|false|none||原始选择信息|
|formData|object|true|none||表单数据（自定义结构）|
|outputFormat|integer|true|none||输出格式：<br />- 1: Gif FBX 格式1<br />- 2: Gif FBX 格式2<br />- 3: Gif FBX 格式3<br />- 4: Gif FBX 格式4|

#### 枚举值

|属性|值|
|---|---|
|outputFormat|1|
|outputFormat|2|
|outputFormat|3|
|outputFormat|4|

<h2 id="tocS_ContactRequest">ContactRequest</h2>

<a id="schemacontactrequest"></a>
<a id="schema_ContactRequest"></a>
<a id="tocScontactrequest"></a>
<a id="tocscontactrequest"></a>

```json
{
  "phone": "***********",
  "companyName": "示例科技有限公司",
  "project": "AI视频处理解决方案"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|phone|string|true|none||联系手机号码|
|companyName|string|true|none||公司名称|
|project|string|true|none||咨询项目描述|

<h2 id="tocS_ErrorResponse">ErrorResponse</h2>

<a id="schemaerrorresponse"></a>
<a id="schema_ErrorResponse"></a>
<a id="tocSerrorresponse"></a>
<a id="tocserrorresponse"></a>

```json
{
  "code": 0,
  "message": "string",
  "data": null
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误状态码|
|message|string|false|none||错误信息|
|data|null|false|none||错误时通常为null|

<h2 id="tocS_UserInfo">UserInfo</h2>

<a id="schemauserinfo"></a>
<a id="schema_UserInfo"></a>
<a id="tocSuserinfo"></a>
<a id="tocsuserinfo"></a>

```json
{
  "id": "12345",
  "username": "用户名",
  "email": "<EMAIL>",
  "role": "user",
  "avatar": "https://example.com/avatar.jpg",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z",
  "balance": 100.5,
  "vipLevel": 2,
  "vipDeadline": "2024-12-31T23:59:59Z"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||用户唯一标识|
|username|string|false|none||用户名|
|email|string(email)|false|none||邮箱地址|
|role|string|false|none||用户角色：<br />- user: 普通用户<br />- admin: 管理员|
|avatar|string(uri)¦null|false|none||头像URL|
|createdAt|string(date-time)|false|none||账户创建时间（ISO 8601格式，UTC时区）|
|updatedAt|string(date-time)|false|none||最后更新时间（ISO 8601格式，UTC时区）|
|balance|number(double)|false|none||账户余额（单位：元）|
|vipLevel|integer|false|none||VIP等级（0=普通用户，1-5=VIP等级）|
|vipDeadline|string(date-time)|false|none||VIP到期时间（ISO 8601格式，UTC时区）|

#### 枚举值

|属性|值|
|---|---|
|role|user|
|role|admin|

<h2 id="tocS_OriginalSelection">OriginalSelection</h2>

<a id="schemaoriginalselection"></a>
<a id="schema_OriginalSelection"></a>
<a id="tocSoriginalselection"></a>
<a id="tocsoriginalselection"></a>

```json
{
  "start": 10.2,
  "end": 40.7,
  "duration": 30.5
}

```

原始选择信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|start|number(double)|false|none||原始开始时间（秒）|
|end|number(double)|false|none||原始结束时间（秒）|
|duration|number(double)|false|none||原始持续时间（秒）|

<h2 id="tocS_SuccessResponse">SuccessResponse</h2>

<a id="schemasuccessresponse"></a>
<a id="schema_SuccessResponse"></a>
<a id="tocSsuccessresponse"></a>
<a id="tocssuccessresponse"></a>

```json
{
  "code": 0,
  "message": "string",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||状态码（0表示成功）|
|message|string|false|none||成功消息|
|data|object|false|none||响应数据|

<h2 id="tocS_CreateFileResponse">CreateFileResponse</h2>

<a id="schemacreatefileresponse"></a>
<a id="schema_CreateFileResponse"></a>
<a id="tocScreatefileresponse"></a>
<a id="tocscreatefileresponse"></a>

```json
{
  "id": "file_987654321",
  "fileId": "project_abc123",
  "status": "created"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|string|false|none||文件记录ID（可选）|
|fileId|string|false|none||项目文件ID（可选）|
|status|string|true|none||创建状态：<br />- created: 创建成功<br />- processing: 处理中<br />- failed: 创建失败|

#### 枚举值

|属性|值|
|---|---|
|status|created|
|status|processing|
|status|failed|

