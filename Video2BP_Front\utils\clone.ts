/**
 * 深拷贝
 */
export const deepClone = <T>(obj: T, visited = new WeakMap()): T => {
	// 基本类型直接返回
	if (obj === null || typeof obj !== 'object') {
		return obj;
	}

	// 检查循环引用
	if (visited.has(obj as object)) {
		return visited.get(obj as object);
	}

	// 获取对象的类型
	const type = Object.prototype.toString.call(obj);

	// 处理各种特殊对象类型
	switch (type) {
		case '[object Date]':
			return new Date((obj as unknown as Date).getTime()) as unknown as T;

		case '[object RegExp]': {
			const regExp = obj as unknown as RegExp;
			return new RegExp(regExp.source, regExp.flags) as unknown as T;
		}

		case '[object Map]': {
			const map = obj as unknown as Map<any, any>;
			const clonedMap = new Map();
			visited.set(obj as object, clonedMap);
			map.forEach((value, key) => {
				clonedMap.set(
					deepClone(key, visited),
					deepClone(value, visited)
				);
			});
			return clonedMap as unknown as T;
		}

		case '[object Set]': {
			const set = obj as unknown as Set<any>;
			const clonedSet = new Set();
			visited.set(obj as object, clonedSet);
			set.forEach((value) => {
				clonedSet.add(deepClone(value, visited));
			});
			return clonedSet as unknown as T;
		}

		case '[object ArrayBuffer]':
			return (obj as unknown as ArrayBuffer).slice(0) as unknown as T;

		case '[object Function]':
			return obj;

		default:
			// 处理类型化数组
			if (ArrayBuffer.isView(obj)) {
				const Constructor = (obj as any).constructor;
				return new Constructor(obj) as unknown as T;
			}
	}

	// 处理数组
	if (Array.isArray(obj)) {
		const clonedArray: any[] = [];
		visited.set(obj as object, clonedArray);

		obj.forEach((item, index) => {
			clonedArray[index] = deepClone(item, visited);
		});

		return clonedArray as unknown as T;
	}

	// 处理普通对象
	const cloned = Object.create(Object.getPrototypeOf(obj));
	visited.set(obj as object, cloned);

	// 获取所有属性键（包括 Symbol）
	Reflect.ownKeys(obj).forEach((key) => {
		const descriptor = Object.getOwnPropertyDescriptor(obj, key);
		if (descriptor) {
			if (descriptor.value !== undefined) {
				Object.defineProperty(cloned, key, {
					...descriptor,
					value: deepClone(descriptor.value, visited),
				});
			} else {
				Object.defineProperty(cloned, key, descriptor);
			}
		}
	});

	return cloned;
};
