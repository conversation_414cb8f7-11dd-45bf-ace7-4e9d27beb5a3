import datetime
import decimal
import json
import time
from json import JSONEncoder

from . import R

SUCCESS_CODE = 0
FAIL_CODE = 500
TIMEOUT_CODE = 408
NOT_LOGIN_CODE = 401


def custom(*args) -> R:
    if len(args) == 0:
        raise Exception("R.custom 没有指定错误码")
    elif len(args) == 1:
        return R(args[0], "")
    elif len(args) == 2:
        return R(args[0], args[1])
    elif len(args) == 3:
        return R(args[0], args[1], args[2])
    else:
        raise Exception("R.custom 参数数量不对")


from typing import Any, Union, Tuple
from .exceptions import BusinessError

class R:
    def __init__(self, code: int, msg: str, data: Any = None):
        self.code = code
        self.msg = msg
        self.data = data or {}
        self.timestamp = int(round(time.time() * 1000))

    @classmethod
    def success(cls, msg: str = "success", data: Any = None) -> "R":
        """成功响应"""
        return cls(SUCCESS_CODE, msg, data)

    @classmethod
    def fail(cls, msg: str = "failed", data: Any = None) -> "R":
        """成功响应"""
        return cls(FAIL_CODE, msg, data)

    @classmethod
    def error(cls, error: Union[BusinessError, Tuple[int, str]], data: Any = None) -> "R":
        """错误响应"""
        if isinstance(error, BusinessError):
            return cls(error.code, error.msg, error.data or data)
        code, msg = error
        return cls(code, msg, data)

    def __str__(self):
        return json.dumps({
            "code": self.code,
            "msg": self.msg,
            "data": self.data,
            "timestamp": self.timestamp
        }, indent=2, ensure_ascii=False, cls=AlchemyEncoder, check_circular=False)

    def with_errno(self, errno, data=None):
        """使用 errno_no 中的错误码和消息创建响应"""
        return R.custom(errno.code, errno.msg, data)


class AlchemyEncoder(JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Exception):
            return str(obj)
        if isinstance(obj, R):
            return str(obj)
        if isinstance(obj, datetime.datetime):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        if isinstance(obj, datetime.date):
            return obj.strftime("%Y-%m-%d")
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        if isinstance(obj, bytes):
            return str(obj, encoding="utf-8")
        return JSONEncoder.default(self, obj)
