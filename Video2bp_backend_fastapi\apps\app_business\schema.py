from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl, ConfigDict
from schemas.base import BaseSchema
from schemas.response import StandardResponse


class Business(BaseSchema):
    """业务模型"""
    id: int = Field(..., description="业务ID")
    title: str = Field(..., description="业务标题")
    image: HttpUrl = Field(..., description="业务图片URL")
    details: List[str] = Field(..., description="业务详情列表")


class BusinessListResponse(BaseSchema):
    """业务列表响应"""
    pass  # 直接返回业务列表数组


class BusinessListResponseModel(StandardResponse):
    """业务列表响应模型"""
    data: Optional[List[Business]] = None


class Product(BaseModel):
    """产品模型"""
    model_config = ConfigDict(
        populate_by_name=True,
        # 不使用alias_generator，保持原始字段名
    )

    id: int = Field(..., description="产品ID")
    title: str = Field(..., description="产品标题")
    description: str = Field(..., description="产品描述")
    videoUrl: HttpUrl = Field(..., description="产品演示视频URL")


class ProductListResponse(BaseSchema):
    """产品列表响应"""
    pass  # 直接返回产品列表数组


class ProductListResponseModel(StandardResponse):
    """产品列表响应模型"""
    data: Optional[List[Product]] = None


class UserCase(BaseSchema):
    """用户案例模型"""
    id: int = Field(..., description="案例ID")
    name: str = Field(..., description="用户姓名")
    avatar: HttpUrl = Field(..., description="用户头像URL")
    time: str = Field(..., description="时间标识")
    content: str = Field(..., description="案例内容描述")
    preview: HttpUrl = Field(..., description="案例预览视频URL")


class UserCaseListResponse(BaseSchema):
    """用户案例列表响应"""
    pass  # 直接返回用户案例列表数组


class UserCaseListResponseModel(StandardResponse):
    """用户案例列表响应模型"""
    data: Optional[List[UserCase]] = None


class ContactRequest(BaseSchema):
    """联系表单请求"""
    phone: str = Field(..., description="手机号")
    companyName: str = Field(..., description="公司名称")
    project: str = Field(..., description="咨询项目")


class ContactResponse(BaseSchema):
    """联系表单响应"""
    pass


class ContactResponseModel(StandardResponse):
    """联系表单响应模型"""
    data: Optional[ContactResponse] = None
