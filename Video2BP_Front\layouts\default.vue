<template>
  <div class="app-container">
    <!-- 应用内容 - 始终渲染 -->
    <div class="app-content" :class="{ 'app-loading': isAppLoading }">
      <AppHeader v-if="layoutSettings.headerVisible" :style="layoutSettings.headStyle" />
      <main>
        <slot />
      </main>
      <AppFooter v-if="layoutSettings.footerVisible" />
    </div>

    <!-- 应用初始化覆盖层 -->
    <AppInitializer v-if="isAppLoading" :progress="loadingProgress" :loading-text="loadingText" />
  </div>
</template>

<script setup lang="ts">
import AppHeader from '~/components/layout/AppHeader.vue';
import AppFooter from '~/components/layout/AppFooter.vue';
import AppInitializer from '~/components/common/AppInitializer.vue';
import type { LayoutConfig } from '~/types/layout';

// 应用加载状态
const isAppLoading = ref(true)
const loadingProgress = ref(0)
const loadingText = ref('正在初始化应用')

// 模拟加载进度
const simulateLoading = async () => {
  const steps = [
    { progress: 20, text: '正在加载资源' },
    { progress: 40, text: '正在初始化组件' },
    { progress: 60, text: '正在连接服务' },
    { progress: 80, text: '正在准备界面' },
    { progress: 100, text: '加载完成' }
  ]

  for (const step of steps) {
    loadingProgress.value = step.progress
    loadingText.value = step.text
    await new Promise(resolve => setTimeout(resolve, 300))
  }

  // 额外等待一下让用户看到100%
  await new Promise(resolve => setTimeout(resolve, 500))
}

// 在客户端挂载后开始加载流程
onMounted(async () => {
  await nextTick()

  // 模拟加载进度
  await simulateLoading()

  // 隐藏应用加载状态
  isAppLoading.value = false
})

// 获取路由元数据中的布局配置
const route = useRoute();
const layoutMeta: Ref<LayoutConfig> = computed(() => route.meta.layoutconfig || {});

// 合并默认配置和路由元数据中的配置
const layoutConfig = useState<LayoutConfig>('layoutConfig', () => ({}));

const layoutSettings = computed(() => {
  return {
    headerVisible: layoutConfig.value.headerVisible ?? layoutMeta.value.headerVisible ?? true,
    footerVisible: layoutConfig.value.footerVisible ?? layoutMeta.value.footerVisible ?? true,
    headStyle: layoutConfig.value.headStyle ?? layoutMeta.value.headStyle ?? {},
  }
});

</script>

<style lang="scss">
.app-container {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  .app-content {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;

    &.app-loading {
      opacity: 0;
    }

    &:not(.app-loading) {
      animation: fadeIn 0.5s ease-in-out forwards;
    }

    main {
      flex: 1;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
