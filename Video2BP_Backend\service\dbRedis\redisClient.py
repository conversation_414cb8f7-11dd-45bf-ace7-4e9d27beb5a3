import redis
import sys
import traceback
from rediscluster import RedisCluster
from loguru import logger
from settings import REDIS_CLUSTER_STR, REDIS_CLUSTER_PASSWORD



class RedisError(RuntimeError):
    def __init__(self, err_msg):
        self.errMsg = err_msg
        self.errDetails = None

    def data(self, err_details):
        self.errDetails = err_details
        return self


ErrRedisParamNone = RedisError("param has None")
ErrRedisConnPool = RedisError("conn_pool is null and refresh fail")
ErrRedisRedisNone = RedisError("redis.Redis is None")
ErrRedishNameNotExist = RedisError("name is not exist")


class RedisClient:
    __instance = None
    cluster_url = REDIS_CLUSTER_STR
    password = REDIS_CLUSTER_PASSWORD
    host = None
    port = None
    db = 0

    def __new__(cls, *args, **kwargs):
        if not cls.__instance:
            cls.check_redis()
            cls.__instance = object.__new__(cls)
        return cls.__instance

    def __init__(self):
        try:
            self.CLUSTER_POLL = None
            if self.cluster_url:  # 集群
                self.redis_str = self.cluster_url
                self.refresh_init()

            else:  # 单机
                self.CONN_POLL = redis.ConnectionPool(
                    host=self.host,
                    port=self.port,
                    password=self.password,
                    decode_responses=True,
                    db=self.db,
                    max_connections=100)

        except Exception as e:
            logger.error(f"数据库初次连接异常, 程序退出:{str(e)},traceback={traceback.format_exc()}")
            sys.exit(0)

    @classmethod
    def check_redis(cls):
        if cls.cluster_url is None:
            logger.error(f"未配置Redis参数")
            exit(1)

        cluster_list = cls.cluster_url.split(",")
        if len(cls.cluster_url) <= 0 or cluster_list[0] == "":
            logger.error(f"Redis参数配置有误")
            exit(1)
        elif len(cluster_list) == 1:  # 单机
            cls.host, cls.port = cluster_list[0].split(":")
            cls.cluster_url = None

    def refresh_init(self):
        try:
            redis_nodes = self.redis_list()
            if self.password:
                self.CLUSTER_POLL = RedisCluster(startup_nodes=redis_nodes,
                                                 max_connections=1000,
                                                 password=self.password,
                                                 decode_responses=True
                                                 )
            else:
                self.CLUSTER_POLL = RedisCluster(startup_nodes=redis_nodes,
                                                 max_connections=1000,
                                                 decode_responses=True
                                                 )

        except Exception as e:
            log = f"error:{str(e)},traceback={traceback.format_exc()}"
            logger.error(log)
            sys.exit(0)

    def redis_list(self):
        """集群处理"""
        redis_nodes = []
        if "," in self.redis_str:
            lines = self.redis_str.split(",")
            for i in lines:
                line = i.split(":")
                redis_nodes.append({"host": line[0], "port": line[1]})
        else:
            line = self.redis_str.split(":")
            redis_nodes.append({"host": line[0], "port": line[1]})
        return redis_nodes

    def get_redis_client(self):
        try:
            if self.CLUSTER_POLL:
                logger.info("redis集群连接成功")
                return self.CLUSTER_POLL

            conn = redis.StrictRedis(connection_pool=self.CONN_POLL)
            if conn.ping():
                logger.info(f"redis:{self.host}正常")
                return conn
            return None
        except Exception as e:
            logger.error(f"数据库连接异常, 程序退出:{str(e)},traceback={traceback.format_exc()}")
            sys.exit(0)


redis_obj = RedisClient()
ErrRedisClient = RedisError("操作redis异常")


def get_client():
    return redis_obj.get_redis_client()
