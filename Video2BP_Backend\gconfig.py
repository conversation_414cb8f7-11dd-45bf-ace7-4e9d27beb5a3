# -*- coding: utf-8 -*-
__author__ = ''

import os

bind = '0.0.0.0:5000'
daemon = False
debug = False
worker_class = 'sync'
workers = 4  # multiprocessing
pidfile = 'project.pid'
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
log_path = BASE_DIR + "/static/logs"

chdir = '../'
timeout = 600  # timeout. need to warmup cache
graceful_timeout = 600

# http://docs.gunicorn.org/en/stable/settings.html
GUNICORN_ERROR_LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{line}:{function} | {message}"
GUNICORN_ACCESS_LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{line}:{function} | {message}"
DMS_ACCESS_LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{line}:{function} | {message}"

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        "gunicorn.error": {
            "level": "ERROR",
            "handlers": ["error_file"],
            "propagate": 0,
            "qualname": "gunicorn.error"
        },
        "gunicorn.access": {
            "level": "INFO",
            "handlers": ["access_file"],
            "propagate": 0,
            "qualname": "gunicorn.access"
        }
    },
    'handlers': {
        "error_file": {
            "class": "cloghandler.ConcurrentRotatingFileHandler",
            "maxBytes": 1024 * 1024 * 1024,
            "backupCount": 5,
            "encoding": "utf-8",
            "rotation": "00:00",
            "backtrace": True,
            "diagnose": False,
            "enqueue": True,
            "retention": "1 week",
            "formatter": "generic",
            "filename": os.path.join(log_path, "gunicorn_error.log"),
        },
        "access_file": {
            "class": "cloghandler.ConcurrentRotatingFileHandler",
            "maxBytes": 1024 * 1024 * 1024,
            "backupCount": 5,
            "encoding": "utf-8",
            "rotation": "00:00",
            "backtrace": True,
            "diagnose": False,
            "enqueue": True,
            "retention": "1 week",
            "formatter": "generic",
            "filename": os.path.join(log_path, "gunicorn_access.log"),
        }
    },
    'formatters': {
        "generic": {
            "format": "%(asctime)s [%(process)d] %(levelname)s"
                      + " [%(filename)s:%(lineno)s] %(message)s",
            "datefmt": "[%Y-%m-%d %H:%M:%S]",
            "class": "logging.Formatter"
        }
    }
}