import os

__version__ = "v1.0.0"


PORT = 5000

LOG_LEVEL = "INFO"
WEB_TOKEN_EXP_TIME = 7*24*60*60
SECRET_KEY = "Lingyu"


# DB
mysql_host = "127.0.0.1"
mysql_port = "3306"
mysql_user = "root"
mysql_password = "webLingc0011%40"
mysql_db_name = "video2bp"
DB_URI = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db_name}?charset=utf8"
DEBUG = True
# redis
REDIS_CLUSTER_STR = "127.0.0.1" + ":" + "6379"
REDIS_CLUSTER_PASSWORD = ""

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_PATH = BASE_DIR + "\\static\\logs"
GUNICORN_ERROR_LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{line}:{function} | {message}"
GUNICORN_ACCESS_LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{line}:{function} | {message}"

VIDEO_EXTENSIONS = {"mp4", "avi", "mov"}

class Config:
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db_name}?charset=utf8")
    SQLALCHEMY_TRACK_MODIFICATIONS = False

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "loggers": {
        "gunicorn.error": {
            "level": "ERROR",
            "handlers": ["error_file"],
            "propagate": 0,
            "qualname": "gunicorn.error"
        },
        "gunicorn.access": {
            "level": "INFO",
            "handlers": ["access_file"],
            "propagate": 0,
            "qualname": "gunicorn.access"
        }
    },
    "handlers": {
        "error_file": {
            "class": "cloghandler.ConcurrentRotatingFileHandler",
            "maxBytes": 1024 * 1024 * 1024,
            "backupCount": 5,
            "encoding": "utf-8",
            "rotation": "00:00",
            "backtrace": True,
            "diagnose": False,
            "enqueue": True,
            "retention": "1 week",
            "formatter": "generic",
            "filename": os.path.join(LOG_PATH, "gunicorn_error.log"),
        },
        "access_file": {
            "class": "cloghandler.ConcurrentRotatingFileHandler",
            "maxBytes": 1024 * 1024 * 1024,
            "backupCount": 5,
            "encoding": "utf-8",
            "rotation": "00:00",
            "backtrace": True,
            "diagnose": False,
            "enqueue": True,
            "retention": "1 week",
            "formatter": "generic",
            "filename": os.path.join(LOG_PATH, "gunicorn_access.log"),
        }
    },
    "formatters": {
        "generic": {
            "format": "%(asctime)s [%(process)d] %(levelname)s"
                      + " [%(filename)s:%(lineno)s] %(message)s",
            "datefmt": "[%Y-%m-%d %H:%M:%S]",
            "class": "logging.Formatter"
        }
    }
}

MAIL_SERVER = 'smtp.qq.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'rrtlfjktrrinbdjf'

# 视频上传配置
VIDEO_UPLOAD_FOLDER = BASE_DIR + '\\static\\videos'
BP_UPLOAD_FOLDER = BASE_DIR + '\\static\\bp_files'