/**
 * 时间格式化工具函数
 */

/**
 * 统一的时间格式化函数
 * @param seconds 要格式化的秒数
 * @param format 格式模板字符串
 * @returns 格式化后的时间字符串
 *
 * @example
 * formatTime(125, "mm:ss") // "02:05"
 * formatTime(3725, "hh:mm:ss") // "01:02:05"
 * formatTime(125, "hh小时mm分钟ss秒") // "00小时02分钟05秒"
 * formatTime(125, "hh-mm-ss") // "00-02-05"
 * formatTime(125, "mm分ss秒") // "02分05秒"
 * formatTime(3725, "hh:mm") // "01:02"
 */
export const formatTime = (
	seconds: number,
	format: string = 'mm:ss'
): string => {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = Math.floor(seconds % 60);

	return format
		.replace(/hh/g, hours.toString().padStart(2, '0'))
		.replace(/mm/g, minutes.toString().padStart(2, '0'))
		.replace(/ss/g, secs.toString().padStart(2, '0'))
		.replace(/h/g, hours.toString())
		.replace(/m/g, minutes.toString())
		.replace(/s/g, secs.toString());
};

/**
 * 智能时间格式化函数 - 根据时长自动选择合适的格式
 * @param seconds 要格式化的秒数
 * @param separator 分隔符，默认为 ":"
 * @returns 格式化后的时间字符串
 *
 * @example
 * formatTimeAuto(125) // "02:05"
 * formatTimeAuto(3725) // "01:02:05"
 * formatTimeAuto(125, "-") // "02-05"
 */
export const formatTimeAuto = (
	seconds: number,
	separator: string = ':'
): string => {
	const hours = Math.floor(seconds / 3600);

	if (hours > 0) {
		return formatTime(seconds, `hh${separator}mm${separator}ss`);
	} else {
		return formatTime(seconds, `mm${separator}ss`);
	}
};
