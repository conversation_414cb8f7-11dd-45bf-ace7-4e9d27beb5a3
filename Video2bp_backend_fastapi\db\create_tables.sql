-- 视频动捕项目数据库建表脚本
-- 数据库：video2bp_db
-- 字符集：utf8mb4

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `video2bp_db` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `video2bp_db`;

-- 1. 用户表
CREATE TABLE `users` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    `username` VARCHAR(80) NULL COMMENT '用户名',
    `email` VARCHAR(120) NOT NULL UNIQUE COMMENT '邮箱',
    `phone` VARCHAR(120) NULL COMMENT '手机号',
    `password` VARCHAR(128) NOT NULL COMMENT '密码',
    `balance` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '账户余额',
    `vip_level` VARCHAR(80) NOT NULL DEFAULT '1' COMMENT 'VIP等级',
    `status` SMALLINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `current_plan_id` INT NULL COMMENT '当前套餐ID',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_email` (`email`),
    INDEX `idx_phone` (`phone`),
    INDEX `idx_balance` (`balance`),
    INDEX `idx_current_plan` (`current_plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 用户视频表
CREATE TABLE `user_videos` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '视频ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `filename` VARCHAR(128) NULL COMMENT '视频文件名',
    `bp_filename` VARCHAR(128) NULL COMMENT '骨骼点文件名',
    `status` SMALLINT NOT NULL DEFAULT 1 COMMENT '状态：1-上传完成，2-处理中，3-处理完成',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户视频表';

-- 3. 用户订单表
CREATE TABLE `user_orders` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `order_no` VARCHAR(64) NOT NULL UNIQUE COMMENT '订单号',
    `order_type` VARCHAR(50) NOT NULL COMMENT '订单类型',
    `amount` DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    `payment_method` VARCHAR(50) NULL COMMENT '支付方式',
    `payment_status` SMALLINT DEFAULT 0 COMMENT '支付状态：0-未支付，1-已支付，2-已退款',
    `combo_plan_id` INT NULL COMMENT '套餐ID',
    `description` TEXT NULL COMMENT '订单描述',
    `paid_at` DATETIME NULL COMMENT '支付时间',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_order_no` (`order_no`),
    INDEX `idx_payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户订单表';

-- 4. 用户VIP记录表
CREATE TABLE `user_vip_records` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'VIP记录ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `vip_level` VARCHAR(20) NOT NULL COMMENT 'VIP等级',
    `start_date` DATETIME NOT NULL COMMENT '开始时间',
    `end_date` DATETIME NOT NULL COMMENT '结束时间',
    `order_id` BIGINT NULL COMMENT '关联订单ID',
    `status` SMALLINT DEFAULT 1 COMMENT '状态：0-已过期，1-生效中',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户VIP记录表';

-- 5. 新闻表
CREATE TABLE `news` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '新闻ID',
    `name` VARCHAR(255) DEFAULT '' COMMENT '新闻名称',
    `type` INT NOT NULL COMMENT '新闻类型',
    `is_display` BOOLEAN DEFAULT TRUE COMMENT '是否显示',
    `cover` VARCHAR(500) NOT NULL COMMENT '封面图片',
    `title` VARCHAR(255) NOT NULL COMMENT '标题',
    `author` VARCHAR(100) NOT NULL DEFAULT '灵宇 AI 动捕' COMMENT '作者',
    -- `profile_picture` VARCHAR(500) NOT NULL COMMENT '作者头像',
    `excerpt` TEXT NOT NULL COMMENT '摘要',
    `tag` VARCHAR(100) NULL COMMENT '标签',
    `content` LONGTEXT NOT NULL COMMENT '内容',
    `is_pinned` BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_type` (`type`),
    INDEX `idx_is_display` (`is_display`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻表';

-- 6. 业务项目表
CREATE TABLE `business_items` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '业务项目ID',
    `title` VARCHAR(255) NOT NULL COMMENT '业务标题',
    `image_url` VARCHAR(500) NOT NULL COMMENT '业务图片URL',
    `details` JSON NOT NULL COMMENT '业务详情列表',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务项目表';

-- 7. 产品表
CREATE TABLE `products` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '产品ID',
    `title` VARCHAR(255) NOT NULL COMMENT '产品标题',
    `description` TEXT NOT NULL COMMENT '产品描述',
    `video_url` VARCHAR(500) NOT NULL COMMENT '产品演示视频URL',
    `thumbnail_url` VARCHAR(500) NULL COMMENT '缩略图URL',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';

-- 8. 用户案例表
CREATE TABLE `user_cases` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '案例ID',
    `user_name` VARCHAR(100) NOT NULL COMMENT '用户姓名',
    `avatar_url` VARCHAR(500) NOT NULL COMMENT '用户头像URL',
    `time_label` VARCHAR(20) NOT NULL COMMENT '时间标识',
    `content` TEXT NOT NULL COMMENT '案例内容描述',
    `preview_url` VARCHAR(500) NOT NULL COMMENT '案例预览视频URL',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户案例表';

-- 9. 项目展示表
CREATE TABLE `projects` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '项目ID',
    `title` VARCHAR(255) NOT NULL COMMENT '项目标题',
    `description` TEXT NOT NULL COMMENT '项目描述',
    `image_url` VARCHAR(500) NOT NULL COMMENT '项目图片URL',
    `category` VARCHAR(100) NOT NULL COMMENT '项目分类',
    `tags` JSON NOT NULL COMMENT '项目标签',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_category` (`category`),
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目展示表';

-- 10. 资源下载表
CREATE TABLE `resources` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '资源ID',
    `title` VARCHAR(255) NOT NULL COMMENT '资源标题',
    `description` TEXT NOT NULL COMMENT '资源描述',
    `resource_type` VARCHAR(50) NOT NULL COMMENT '资源类型',
    `file_url` VARCHAR(500) NOT NULL COMMENT '资源链接',
    `thumbnail_url` VARCHAR(500) NULL COMMENT '缩略图URL',
    `file_size` VARCHAR(20) NULL COMMENT '文件大小',
    `file_format` VARCHAR(20) NULL COMMENT '文件格式',
    `download_count` INT DEFAULT 0 COMMENT '下载次数',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_resource_type` (`resource_type`),
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源下载表';

-- 11. 公司信息表
CREATE TABLE `company_info` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '公司信息ID',
    `name` VARCHAR(255) NOT NULL COMMENT '公司名称',
    `description` TEXT NULL COMMENT '公司描述',
    `address` VARCHAR(500) NULL COMMENT '公司地址',
    `phone` VARCHAR(50) NULL COMMENT '联系电话',
    `email` VARCHAR(100) NULL COMMENT '联系邮箱',
    `website` VARCHAR(255) NULL COMMENT '官方网站',
    `logo_url` VARCHAR(500) NULL COMMENT '公司Logo URL',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司信息表';

-- 12. 套餐计划表
CREATE TABLE `combo_plans` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '套餐ID',
    `name` VARCHAR(100) NOT NULL COMMENT '套餐名称',
    `description` TEXT NULL COMMENT '套餐描述',
    `price` DECIMAL(10,2) NOT NULL COMMENT '套餐价格',
    `duration_days` INT NOT NULL COMMENT '有效期天数',
    `features` JSON NOT NULL COMMENT '套餐功能列表',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_sort_order` (`sort_order`),
    INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐计划表';

-- 13. 联系表单提交记录表
CREATE TABLE `contact_submissions` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '提交记录ID',
    `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
    `company_name` VARCHAR(255) NOT NULL COMMENT '公司名称',
    `project` VARCHAR(255) NOT NULL COMMENT '咨询项目',
    `status` SMALLINT DEFAULT 0 COMMENT '处理状态：0-未处理，1-已处理',
    `remark` TEXT NULL COMMENT '备注',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_phone` (`phone`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系表单提交记录表';

-- 14. 视频处理记录表
CREATE TABLE `video_process_records` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '处理记录ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `video_id` BIGINT NOT NULL COMMENT '视频ID',
    `file_record_id` VARCHAR(100) NULL COMMENT '文件记录ID',
    `project_id` VARCHAR(100) NULL COMMENT '项目ID',
    `start_time` DECIMAL(10,3) NULL COMMENT '开始时间',
    `end_time` DECIMAL(10,3) NULL COMMENT '结束时间',
    `duration` DECIMAL(10,3) NULL COMMENT '时长',
    `was_trimmed` BOOLEAN DEFAULT FALSE COMMENT '是否被裁剪',
    `output_format` SMALLINT NOT NULL COMMENT '输出格式：1-4',
    `original_selection` JSON NULL COMMENT '原始选择',
    `form_data` JSON NULL COMMENT '表单数据',
    `process_status` SMALLINT DEFAULT 0 COMMENT '处理状态：0-待处理，1-处理中，2-已完成，3-失败',
    `progress` SMALLINT DEFAULT 0 COMMENT '处理进度：0-100',
    `error_message` TEXT NULL COMMENT '错误信息',
    `result_file_path` VARCHAR(500) NULL COMMENT '结果文件路径',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_video_id` (`video_id`),
    INDEX `idx_process_status` (`process_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频处理记录表';

-- 插入初始数据

-- 插入公司信息
INSERT INTO `company_info` (`name`, `description`, `address`, `phone`, `email`, `website`, `logo_url`) VALUES
('灵宇AI动捕', '专业的AI动作捕捉解决方案提供商', '北京市海淀区中关村科技园', '************', '<EMAIL>', 'https://www.lingyuai.com', 'https://example.com/logo.png');

-- 插入套餐信息
INSERT INTO `combo_plans` (`name`, `description`, `price`, `duration_days`, `features`, `sort_order`) VALUES
('基础版', '适合个人用户的基础动捕功能', 99.00, 30, '["单人动捕", "基础格式导出", "标准技术支持"]', 1),
('专业版', '适合专业用户的高级动捕功能', 299.00, 30, '["多人动捕", "高级格式导出", "面部表情捕捉", "优先技术支持"]', 2),
('企业版', '适合企业用户的完整解决方案', 999.00, 30, '["无限制动捕", "所有格式导出", "API接入", "专属技术支持", "定制化服务"]', 3);

-- 插入业务项目
INSERT INTO `business_items` (`title`, `image_url`, `details`, `sort_order`) VALUES
('AI视频处理解决方案', 'https://example.com/business1.jpg', '["解决方案技术支持", "应用支持服务", "产品功能演示讲解", "产品部署与接入调试指南"]', 1),
('动作捕捉技术服务', 'https://example.com/business2.jpg', '["专业动作捕捉设备", "实时动作识别", "高精度骨骼追踪", "多人同时捕捉"]', 2);

-- 插入产品信息
INSERT INTO `products` (`title`, `description`, `video_url`, `thumbnail_url`, `sort_order`) VALUES
('单/多人捕捉', '在动作捕捉的世界中，首帧姿势是一切的开始。自定义首帧姿势，赋予您无限的创造自由。', 'https://example.com/videos/demo1.mp4', 'https://example.com/thumbnails/product1.jpg', 1),
('面部表情捕捉', '精准捕捉面部表情变化，为您的角色注入生动的情感表达。', 'https://example.com/videos/demo2.mp4', 'https://example.com/thumbnails/product2.jpg', 2),
('手部动作捕捉', '细致入微的手部动作捕捉，让每一个手势都栩栩如生。', 'https://example.com/videos/demo3.mp4', 'https://example.com/thumbnails/product3.jpg', 3);

-- 插入用户案例
INSERT INTO `user_cases` (`user_name`, `avatar_url`, `time_label`, `content`, `preview_url`, `sort_order`) VALUES
('Elva Cruz', 'https://example.com/avatars/user1.jpg', '10:30w', '很喜欢这首歌，但找了半天发现没人配布这首歌的动作，只能我自己出手了...', 'https://example.com/videos/case1.mp4', 1),
('张小明', 'https://example.com/avatars/user2.jpg', '8:45w', '使用灵宇AI动捕制作了一个舞蹈视频，效果超出预期！', 'https://example.com/videos/case2.mp4', 2),
('李小红', 'https://example.com/avatars/user3.jpg', '6:20w', '第一次尝试动作捕捉，操作简单，效果很棒！', 'https://example.com/videos/case3.mp4', 3);

-- 插入项目展示
INSERT INTO `projects` (`title`, `description`, `image_url`, `category`, `tags`, `sort_order`) VALUES
('AI动作捕捉系统', '基于深度学习的实时动作捕捉解决方案，支持多人同时捕捉', 'https://example.com/projects/project1.jpg', 'AI技术', '["动作捕捉", "深度学习", "实时处理"]', 1),
('虚拟现实交互系统', '沉浸式VR体验平台，结合动作捕捉技术实现自然交互', 'https://example.com/projects/project2.jpg', 'VR技术', '["虚拟现实", "交互设计", "沉浸体验"]', 2),
('智能健身指导系统', '基于动作识别的智能健身教练，提供实时动作纠正', 'https://example.com/projects/project3.jpg', '健康科技', '["健身", "动作识别", "智能指导"]', 3);

-- 插入资源下载
INSERT INTO `resources` (`title`, `description`, `resource_type`, `file_url`, `thumbnail_url`, `file_size`, `file_format`, `sort_order`) VALUES
('动作捕捉SDK开发文档', '完整的SDK开发指南，包含API文档和示例代码', '文档', 'https://example.com/docs/sdk-guide.pdf', 'https://example.com/thumbnails/doc1.jpg', '2.5MB', 'PDF', 1),
('动作数据集', '包含1000+个标准动作的训练数据集', '数据集', 'https://example.com/datasets/motion-dataset.zip', 'https://example.com/thumbnails/dataset1.jpg', '150MB', 'ZIP', 2),
('示例视频素材', '高质量的动作捕捉示例视频，适合测试和演示', '视频', 'https://example.com/videos/demo-collection.mp4', 'https://example.com/thumbnails/video1.jpg', '85MB', 'MP4', 3),
('3D模型库', '常用的人体3D模型和骨骼绑定文件', '模型', 'https://example.com/models/character-models.zip', 'https://example.com/thumbnails/model1.jpg', '45MB', 'FBX', 4);
