export interface NewsListParams {
	pageNum: number;
	pageSize: number;
	category: number;
}

export interface NewsItem {
	id: number;
	title: string;
	image: string;
	date: string;
	author: string;
	authorAvatar?: string;
	tag: string;
}

export interface NewsListData {
	data: NewsItem[];
	total: number;
	pageNum: number;
	pageSize: number;
}

export interface NewsListResponse {
	code: number;
	message: string;
	data: NewsListData;
}
