import os
import shutil
import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from loguru import logger

from core.config import settings
from lib.upload_manager import cleanup_task


async def startup_database():
    """初始化数据库连接"""
    try:
        logger.info("🚀 Initializing database connection pool...")
        # 这里可以添加数据库连接预热逻辑
        from db.database import async_engine
        # 测试数据库连接（可选）
        logger.success("✅ Database connection pool initialized")
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        raise


async def startup_redis():
    """初始化Redis连接"""
    try:
        logger.info("🔗 Connecting to Redis...")

        # 测试Redis连接
        from apps.app_user_auth.service import user_auth_service
        user_auth_service.redis_client.ping()

        logger.success("✅ Redis connection established")
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        # Redis连接失败不应该阻止应用启动，只记录警告
        logger.warning("⚠️ Application will continue without Redis")


async def startup_directories():
    """创建必要的存储目录"""
    try:
        logger.info("📁 Creating storage directories...")

        directories = [
            "static/videos",
            "static/bp",
            "static/videos/temp",
            "logs/app",
            "logs/celery"
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.debug(f"Created directory: {directory}")

        logger.success("✅ Storage directories created")
    except Exception as e:
        logger.error(f"❌ Directory creation failed: {e}")
        raise


async def startup_validation():
    """验证应用配置"""
    try:
        logger.info("🔍 Validating application configuration...")

        # 检查必要的配置
        required_configs = {
            "SECRET_KEY": settings.SECRET_KEY,
        }

        missing_configs = []
        for name, value in required_configs.items():
            if not value:
                missing_configs.append(name)

        if missing_configs:
            raise ValueError(f"Missing required configurations: {', '.join(missing_configs)}")

        logger.success("✅ Configuration validation passed")
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        raise


async def shutdown_database():
    """关闭数据库连接"""
    try:
        logger.info("🔄 Closing database connections...")
        from db.database import async_engine, engine

        # 关闭异步引擎
        if async_engine:
            await async_engine.dispose()

        # 关闭同步引擎
        if engine:
            engine.dispose()

        logger.success("✅ Database connections closed")
    except Exception as e:
        logger.error(f"❌ Database shutdown error: {e}")


async def shutdown_redis():
    """关闭Redis连接"""
    try:
        logger.info("🔄 Closing Redis connections...")

        from apps.app_user_auth.service import user_auth_service
        from apps.app_video_manage.service import video_manage_service

        user_auth_service.redis_client.close()
        video_manage_service.redis_client.close()

        logger.success("✅ Redis connections closed")
    except Exception as e:
        logger.error(f"❌ Redis shutdown error: {e}")


async def shutdown_cleanup():
    """清理临时文件"""
    try:
        logger.info("🧹 Cleaning up temporary files...")

        temp_dir = "static/videos/temp"
        if os.path.exists(temp_dir):
            for item in os.listdir(temp_dir):
                item_path = os.path.join(temp_dir, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                    logger.debug(f"Removed temp directory: {item_path}")

        logger.success("✅ Temporary files cleaned up")
    except Exception as e:
        logger.error(f"❌ Cleanup error: {e}")


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    应用生命周期管理

    Args:
        app: FastAPI应用实例

    Yields:
        None: 应用运行期间
    """
    # ========== 启动阶段 ==========
    logger.info("🚀 Starting Video2BP FastAPI application...")

    try:
        # 1. 验证配置
        await startup_validation()

        # 2. 创建目录
        await startup_directories()

        # 3. 初始化数据库
        await startup_database()

        # 4. 初始化Redis（可选）
        # await startup_redis()

        # 5. 启动上传管理器清理任务
        cleanup_task_handle = asyncio.create_task(cleanup_task())
        logger.info("🧹 Upload manager cleanup task started")

        logger.success("🎉 Application startup completed successfully!")

    except Exception as e:
        logger.error(f"💥 Application startup failed: {e}")
        raise

    # ========== 运行阶段 ==========

    yield

    # ========== 关闭阶段 ==========
    logger.info("🛑 Shutting down Video2BP FastAPI application...")

    try:
        # 1. 取消上传管理器清理任务
        cleanup_task_handle.cancel()
        try:
            await cleanup_task_handle
        except asyncio.CancelledError:
            logger.info("🧹 Upload manager cleanup task cancelled")

        # 2. 清理临时文件
        await shutdown_cleanup()

        # 3. 关闭Redis连接
        # await shutdown_redis()

        # 4. 关闭数据库连接
        await shutdown_database()

        logger.success("👋 Application shutdown completed successfully!")

    except Exception as e:
        logger.error(f"💥 Application shutdown error: {e}")
        # 关闭阶段的错误不应该抛出，只记录日志