from redisClient import get_client, ErrRedisClient


def insert(img_key, code, exp=60):
    """写入图片验证码"""
    try:
        redis_conn = get_client()
        if redis_conn:
            redis_conn.setex(img_key, exp, code)
            redis_conn.close()
    except Exception as e:
        raise ErrRedisClient.data(str(e))


def get_and_delete(img_key):
    """取出并删除验证码"""
    try:
        redis_conn = get_client()
        code = ""
        if redis_conn:
            code = redis_conn.get(img_key)
            redis_conn.delete(img_key)
            redis_conn.close()
        return code
    except Exception as e:
        raise ErrRedisClient.data(str(e))
