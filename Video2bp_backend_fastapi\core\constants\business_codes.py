"""
业务码常量定义
用于统一管理所有业务响应码，提高安全性和一致性
"""

from enum import IntEnum


class BusinessCode(IntEnum):
    """业务响应码枚举类"""
    
    # ============= 成功响应码 =============
    SUCCESS = 0  # 操作成功
    
    # ============= 认证相关业务码 (1000-1099) =============
    AUTH_LOGIN_FAILED = 1000  # 用户名或密码错误（统一提示，不区分具体原因）
    AUTH_ACCOUNT_LOCKED = 1001  # 账号已锁定，请稍后重试
    AUTH_TOKEN_INVALID = 1002  # 登录状态已失效，请重新登录
    AUTH_TOKEN_EXPIRED = 1003  # 登录已过期，请重新登录
    AUTH_PERMISSION_DENIED = 1004  # 权限不足
    AUTH_NOT_LOGGED_IN = 1005  # 用户未登录
    
    # ============= 用户相关业务码 (1100-1199) =============
    USER_NOT_FOUND = 1100  # 用户不存在
    USER_ALREADY_EXISTS = 1101  # 用户已存在
    USER_EMAIL_EXISTS = 1102  # 邮箱已被注册
    USER_PHONE_EXISTS = 1103  # 手机号已被注册
    USER_STATUS_DISABLED = 1104  # 用户账号已被禁用
    USER_INFO_INCOMPLETE = 1105  # 用户信息不完整
    
    # ============= 验证码相关业务码 (1200-1299) =============
    VERIFICATION_CODE_INVALID = 1200  # 验证码错误或已过期
    VERIFICATION_CODE_EXPIRED = 1201  # 验证码已过期
    VERIFICATION_CODE_SEND_FAILED = 1202  # 验证码发送失败
    VERIFICATION_CODE_TOO_FREQUENT = 1203  # 验证码发送过于频繁
    
    # ============= 参数相关业务码 (1300-1399) =============
    PARAM_INVALID = 1300  # 请求参数无效
    PARAM_MISSING = 1301  # 缺少必要参数
    PARAM_FORMAT_ERROR = 1302  # 参数格式错误
    PARAM_OUT_OF_RANGE = 1303  # 参数超出允许范围
    
    # ============= 业务逻辑相关业务码 (1400-1499) =============
    BUSINESS_LOGIC_ERROR = 1400  # 业务逻辑错误
    OPERATION_NOT_ALLOWED = 1401  # 操作不被允许
    RESOURCE_NOT_FOUND = 1402  # 资源不存在
    RESOURCE_ALREADY_EXISTS = 1403  # 资源已存在
    INSUFFICIENT_BALANCE = 1404  # 余额不足
    
    # ============= 频率限制相关业务码 (1500-1599) =============
    RATE_LIMIT_EXCEEDED = 1500  # 请求频率超限
    TOO_MANY_REQUESTS = 1501  # 请求过于频繁
    OPERATION_TOO_FREQUENT = 1502  # 操作过于频繁
    
    # ============= 系统级错误业务码 (5000-5099) =============
    SYSTEM_ERROR = 5000  # 系统繁忙，请稍后重试
    DATABASE_ERROR = 5001  # 数据库错误
    NETWORK_ERROR = 5002  # 网络错误
    SERVICE_UNAVAILABLE = 5003  # 服务暂不可用
    EXTERNAL_SERVICE_ERROR = 5004  # 外部服务错误


class BusinessMessage:
    """业务响应消息映射"""
    
    _messages = {
        # 成功响应
        BusinessCode.SUCCESS: "success",
        
        # 认证相关
        BusinessCode.AUTH_LOGIN_FAILED: "用户名或密码错误",
        BusinessCode.AUTH_ACCOUNT_LOCKED: "账号已锁定，请10分钟后重试",
        BusinessCode.AUTH_TOKEN_INVALID: "登录状态已失效，请重新登录",
        BusinessCode.AUTH_TOKEN_EXPIRED: "登录已过期，请重新登录",
        BusinessCode.AUTH_PERMISSION_DENIED: "权限不足",
        BusinessCode.AUTH_NOT_LOGGED_IN: "用户未登录",
        
        # 用户相关
        BusinessCode.USER_NOT_FOUND: "用户不存在",
        BusinessCode.USER_ALREADY_EXISTS: "用户已存在",
        BusinessCode.USER_EMAIL_EXISTS: "邮箱已被注册",
        BusinessCode.USER_PHONE_EXISTS: "手机号已被注册",
        BusinessCode.USER_STATUS_DISABLED: "用户账号已被禁用",
        BusinessCode.USER_INFO_INCOMPLETE: "用户信息不完整",
        
        # 验证码相关
        BusinessCode.VERIFICATION_CODE_INVALID: "验证码错误或已过期",
        BusinessCode.VERIFICATION_CODE_EXPIRED: "验证码已过期",
        BusinessCode.VERIFICATION_CODE_SEND_FAILED: "验证码发送失败",
        BusinessCode.VERIFICATION_CODE_TOO_FREQUENT: "验证码发送过于频繁，请稍后重试",
        
        # 参数相关
        BusinessCode.PARAM_INVALID: "请求参数无效",
        BusinessCode.PARAM_MISSING: "缺少必要参数",
        BusinessCode.PARAM_FORMAT_ERROR: "参数格式错误",
        BusinessCode.PARAM_OUT_OF_RANGE: "参数超出允许范围",
        
        # 业务逻辑相关
        BusinessCode.BUSINESS_LOGIC_ERROR: "业务逻辑错误",
        BusinessCode.OPERATION_NOT_ALLOWED: "操作不被允许",
        BusinessCode.RESOURCE_NOT_FOUND: "资源不存在",
        BusinessCode.RESOURCE_ALREADY_EXISTS: "资源已存在",
        BusinessCode.INSUFFICIENT_BALANCE: "余额不足",
        
        # 频率限制相关
        BusinessCode.RATE_LIMIT_EXCEEDED: "请求频率超限，请稍后重试",
        BusinessCode.TOO_MANY_REQUESTS: "请求过于频繁，请稍后重试",
        BusinessCode.OPERATION_TOO_FREQUENT: "操作过于频繁，请稍后重试",
        
        # 系统级错误
        BusinessCode.SYSTEM_ERROR: "系统繁忙，请稍后重试",
        BusinessCode.DATABASE_ERROR: "系统繁忙，请稍后重试",
        BusinessCode.NETWORK_ERROR: "系统繁忙，请稍后重试",
        BusinessCode.SERVICE_UNAVAILABLE: "系统繁忙，请稍后重试",
        BusinessCode.EXTERNAL_SERVICE_ERROR: "系统繁忙，请稍后重试",
    }
    
    @classmethod
    def get(cls, code: BusinessCode) -> str:
        """获取业务码对应的消息"""
        return cls._messages.get(code, "未知错误")


# 认证安全配置常量
class AuthSecurityConfig:
    """认证安全配置常量"""
    
    # 登录失败锁定配置
    MAX_LOGIN_ATTEMPTS = 5  # 最大登录尝试次数
    LOCKOUT_DURATION = 600  # 锁定时长（秒），10分钟
    LOGIN_ATTEMPT_WINDOW = 900  # 登录尝试统计窗口（秒），15分钟
    
    # 强制延迟配置
    LOGIN_DELAY_AFTER_FAILED = 2  # 登录失败后强制延迟（秒）
    LOGIN_DELAY_WHEN_LOCKED = 5  # 账号锁定时强制延迟（秒）
    
    # Redis键前缀
    LOGIN_ATTEMPTS_KEY_PREFIX = "login_attempts:"  # 登录尝试次数
    ACCOUNT_LOCKED_KEY_PREFIX = "account_locked:"  # 账号锁定状态
    
    # 日志相关
    SECURITY_LOG_LEVEL = "WARNING"  # 安全日志级别
    MASK_SENSITIVE_DATA = True  # 是否屏蔽敏感数据
