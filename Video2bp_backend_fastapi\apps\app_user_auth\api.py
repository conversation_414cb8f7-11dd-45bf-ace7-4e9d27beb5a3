"""
用户认证与账户管理接口

功能列表：
1. 验证码相关
   - 发送邮箱验证码 (/send_verification_code)

2. 用户认证
   - 用户注册 (/register)
   - 用户登录 (/login)
   - 用户登出 (/logout)

3. 用户信息管理
   - 获取指定用户信息 (/info [POST])
   - 获取当前登录用户详细信息 (/info [GET])
   - 更新用户信息 (/update)

4. 密码管理
   - 修改密码 (/change-password)

5. 订单记录
   - 分页获取用户订单记录 (/order-records)

6. 账户余额
   - 查询余额 (/balance)
   - 更新余额（充值/扣除）(/balance/update)

错误状态码：
- 400: 请求参数错误
- 401: 未授权/密码错误
- 403: 禁止访问（未登录/权限不足）
- 404: 用户不存在
- 429: 请求频率限制
- 500: 服务器内部错误
"""

import time
from fastapi import APIRouter, Body, Depends, HTTPException, Request, Response
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_429_TOO_MANY_REQUESTS,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from apps.app_user_auth.schema import (
    SendVerificationCodeRequest,
    SendVerificationCodeResponseModel,
    UserRegisterRequest,
    UserRegisterResponseModel,
    UserLoginRequest,
    UserLoginResponseModel,
    UserLogoutResponseModel,
    UserInfoRequest,
    UserInfoResponseModel,
    GetUserInfoResponseModel,
    ChangePasswordRequest,
    ChangePasswordResponseModel,
    UpdateUserRequest,
    UpdateUserResponseModel,
    OrderRecordsRequest,
    OrderRecordsResponseModel,
    ErrorResponseModel,
    BalanceUpdateRequest,
    BalanceUpdateResponseModel,
    BalanceQueryResponseModel,
)
from apps.app_user_auth.service import user_auth_service
from core.config import settings
from core.e import ErrorCode, ErrorMessage
from core.constants import BusinessCode, BusinessMessage
from db.database import get_async_db
from lib.jwt import create_access_token, get_current_user_flexible
from lib.cookie_auth import get_current_user_from_cookie
from apps.app_user.model import User
from schemas.response import StandardResponse


router = APIRouter()


@router.post(
    "/send_verification_code",
    name="发送邮箱验证码",
    response_model=SendVerificationCodeResponseModel,
)
async def send_verification_code(
    request: SendVerificationCodeRequest = Body(...),
):
    """发送邮箱验证码"""
    try:
        await user_auth_service.send_email_verification_code(request.email)
        return SendVerificationCodeResponseModel()
    except Exception as e:
        return SendVerificationCodeResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/register",
    name="用户注册",
    response_model=UserRegisterResponseModel,
    responses={
        200: {"description": "注册成功", "model": UserRegisterResponseModel},
        401: {"description": "注册失败", "model": UserRegisterResponseModel},
        500: {"description": "系统错误", "model": UserRegisterResponseModel},
    }
)
async def register(
    request: UserRegisterRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
):
    """
    用户注册接口

    - 支持邮箱密码注册
    - 用户名可选，如果不提供则使用邮箱前缀
    - 自动检查邮箱和用户名重复
    - 返回注册成功的用户信息
    """
    try:
        # 注册用户
        new_user = await user_auth_service.register_user(
            db, request.email, request.password, request.username
        )

        # 创建成功响应
        from apps.app_user_auth.schema import UserRegisterResponse
        response_data = UserRegisterResponse(
            id=new_user.id,
            email=new_user.email,
            username=new_user.username,
            created_at=new_user.created_at.isoformat() if new_user.created_at else ""
        )

        return UserRegisterResponseModel(
            code=BusinessCode.SUCCESS,
            message=BusinessMessage.get(BusinessCode.SUCCESS),
            data=response_data
        )

    except HTTPException as e:
        # 解析业务码和消息
        try:
            code, message = e.detail.split("_", 1)
            business_code = int(code)
        except (ValueError, AttributeError):
            business_code = BusinessCode.SYSTEM_ERROR
            message = BusinessMessage.get(BusinessCode.SYSTEM_ERROR)

        # 根据业务码返回相应的响应
        if business_code in [BusinessCode.USER_EMAIL_EXISTS, BusinessCode.USER_ALREADY_EXISTS]:
            return UserRegisterResponseModel(
                code=business_code,
                message=message,
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        else:
            return UserRegisterResponseModel(
                code=BusinessCode.SYSTEM_ERROR,
                message=BusinessMessage.get(BusinessCode.SYSTEM_ERROR),
            ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        # 系统级异常，隐藏具体错误信息
        return UserRegisterResponseModel(
            code=BusinessCode.SYSTEM_ERROR,
            message=BusinessMessage.get(BusinessCode.SYSTEM_ERROR),
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.post(
    "/login",
    name="用户登录",
    response_model=UserLoginResponseModel,
    responses={
        200: {"description": "登录成功", "model": UserLoginResponseModel},
        401: {"description": "认证失败", "model": UserLoginResponseModel},
        500: {"description": "系统错误", "model": UserLoginResponseModel},
    }
)
async def login(
    fastapi_request: Request,
    request: UserLoginRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
):
    """
    安全的用户登录接口

    - 统一错误提示，不区分用户不存在或密码错误
    - 登录失败次数限制，超过限制自动锁定账号
    - 强制延迟防止暴力破解
    - 详细的安全日志记录
    """
    try:
        # 获取客户端IP地址
        client_ip = fastapi_request.client.host if fastapi_request.client else None

        # 安全登录认证
        user_id, email = await user_auth_service.login_user(
            db, request.email, request.password, client_ip
        )

        # 创建JWT token
        token = create_access_token(user_id)

        # 创建成功响应
        response_data = UserLoginResponseModel(
            code=BusinessCode.SUCCESS,
            message=BusinessMessage.get(BusinessCode.SUCCESS)
        )
        response = response_data.to_json()

        # 设置Cookie（保持与Flask版本一致）
        # 1. 非HttpOnly Cookie，前端可读取
        response.set_cookie(
            "auth_status",
            "authenticated",
            max_age=7 * 24 * 60 * 60,  # 7天
            httponly=False,
            secure=not settings.DEBUG,  # 生产环境使用HTTPS
            samesite="strict"
        )

        # 2. HttpOnly Cookie，前端不可读取，用于API认证
        response.set_cookie(
            "auth_token",
            token,
            max_age=7 * 24 * 60 * 60,  # 7天
            httponly=True,
            secure=not settings.DEBUG,
            samesite="strict"
        )

        return response

    except HTTPException as e:
        # 解析业务码和消息
        try:
            code, message = e.detail.split("_", 1)
            business_code = int(code)
        except (ValueError, AttributeError):
            business_code = BusinessCode.SYSTEM_ERROR
            message = BusinessMessage.get(BusinessCode.SYSTEM_ERROR)

        # 根据业务码返回相应的响应
        if business_code in [BusinessCode.AUTH_LOGIN_FAILED, BusinessCode.AUTH_ACCOUNT_LOCKED]:
            return UserLoginResponseModel(
                code=business_code,
                message=message,
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        else:
            return UserLoginResponseModel(
                code=BusinessCode.SYSTEM_ERROR,
                message=BusinessMessage.get(BusinessCode.SYSTEM_ERROR),
            ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        # 系统级异常，隐藏具体错误信息
        return UserLoginResponseModel(
            code=BusinessCode.SYSTEM_ERROR,
            message=BusinessMessage.get(BusinessCode.SYSTEM_ERROR),
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.get(
    "/logout",
    name="用户登出",
    response_model=UserLogoutResponseModel,
)
async def logout():
    """用户登出"""
    response_data = UserLogoutResponseModel()
    response = response_data.to_json()
    
    # 删除Cookie
    response.delete_cookie("auth_status")
    response.delete_cookie("auth_token")
    
    return response


@router.post(
    "/info",
    name="获取用户信息",
    response_model=UserInfoResponseModel,
)
async def user_info(
    fastapi_request: Request,
    request: UserInfoRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_flexible),
):
    """获取用户信息"""
    try:
        user_data = await user_auth_service.get_user_info(db, request.userId)
        return UserInfoResponseModel(data=user_data)
    except HTTPException as e:
        return UserInfoResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return UserInfoResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


# 新增的API接口，根据前端文档要求

@router.get(
    "/info",
    name="获取用户信息",
    response_model=GetUserInfoResponseModel,
    responses={
        200: {"description": "成功获取用户信息", "model": GetUserInfoResponseModel},
        403: {"description": "用户未登录", "model": ErrorResponseModel},
        500: {"description": "服务器内部错误", "model": ErrorResponseModel},
    }
)
async def get_user_info(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_flexible),
):
    """获取当前登录用户的详细信息"""
    try:
        user_data = await user_auth_service.get_current_user_info(db, current_user.id)
        return GetUserInfoResponseModel(
            code=0,
            message="success",
            data=user_data
        )
    except HTTPException as e:
        error_detail = e.detail
        if "用户未登录" in error_detail:
            return GetUserInfoResponseModel(
                code=403,
                message="用户未登录",
            ).to_json(status_code=HTTP_403_FORBIDDEN)
        else:
            return GetUserInfoResponseModel(
                code=403,
                message="用户未登录",
            ).to_json(status_code=HTTP_403_FORBIDDEN)
    except Exception as e:
        return GetUserInfoResponseModel(
            code=500,
            message="服务器内部错误，请稍后重试",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.post(
    "/change-password",
    name="修改密码",
    response_model=ChangePasswordResponseModel,
    responses={
        200: {"description": "密码修改成功", "model": ChangePasswordResponseModel},
        400: {"description": "请求参数错误", "model": ErrorResponseModel},
        401: {"description": "当前密码错误", "model": ErrorResponseModel},
        403: {"description": "用户未登录", "model": ErrorResponseModel},
        429: {"description": "请求频率限制", "model": ErrorResponseModel},
        500: {"description": "服务器内部错误", "model": ErrorResponseModel},
    }
)
async def change_password(
    request: ChangePasswordRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_flexible),
):
    """用户修改密码接口，需要验证当前密码"""
    try:
        # 验证请求参数
        if not request.currentPassword or not request.newPassword:
            return ChangePasswordResponseModel(
                code=400,
                message="请求参数错误，当前密码和新密码不能为空",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)

        if len(request.newPassword) < 6 or len(request.newPassword) > 20:
            return ChangePasswordResponseModel(
                code=400,
                message="新密码长度必须在6-20位之间",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)

        await user_auth_service.change_password(db, current_user.id, request.currentPassword, request.newPassword)
        return ChangePasswordResponseModel(
            code=0,
            message="密码修改成功",
            data={}
        )
    except HTTPException as e:
        # 根据错误类型返回对应的状态码
        error_detail = e.detail
        if "当前密码错误" in error_detail:
            return ChangePasswordResponseModel(
                code=401,
                message="当前密码错误",
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        elif "用户未登录" in error_detail:
            return ChangePasswordResponseModel(
                code=403,
                message="用户未登录",
            ).to_json(status_code=HTTP_403_FORBIDDEN)
        elif "频繁" in error_detail:
            return ChangePasswordResponseModel(
                code=429,
                message="修改密码过于频繁，请5分钟后再试",
            ).to_json(status_code=HTTP_429_TOO_MANY_REQUESTS)
        else:
            return ChangePasswordResponseModel(
                code=400,
                message="请求参数错误",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return ChangePasswordResponseModel(
            code=500,
            message="服务器内部错误，请稍后重试",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.put(
    "/update",
    name="更新用户信息",
    response_model=UpdateUserResponseModel,
    responses={
        200: {"description": "用户信息更新成功", "model": UpdateUserResponseModel},
        400: {"description": "请求参数错误或用户名已存在", "model": ErrorResponseModel},
        403: {"description": "用户未登录或权限不足", "model": ErrorResponseModel},
        500: {"description": "服务器内部错误", "model": ErrorResponseModel},
    }
)
async def update_user_info(
    request: UpdateUserRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_flexible),
):
    """更新用户信息"""
    try:
        await user_auth_service.update_user_info(db, current_user.id, request)
        return UpdateUserResponseModel(
            code=0,
            message="用户信息更新成功",
            data={}
        )
    except HTTPException as e:
        error_detail = e.detail
        if "用户名已存在" in error_detail:
            return UpdateUserResponseModel(
                code=400,
                message="用户名已存在",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
        elif "用户未登录" in error_detail:
            return UpdateUserResponseModel(
                code=403,
                message="用户未登录",
            ).to_json(status_code=HTTP_403_FORBIDDEN)
        elif "权限不足" in error_detail:
            return UpdateUserResponseModel(
                code=403,
                message="权限不足，无法修改VIP相关信息",
            ).to_json(status_code=HTTP_403_FORBIDDEN)
        else:
            return UpdateUserResponseModel(
                code=400,
                message="请求参数错误",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return UpdateUserResponseModel(
            code=500,
            message="服务器内部错误，请稍后重试",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.post(
    "/order-records",
    name="获取用户订单记录",
    response_model=OrderRecordsResponseModel,
    responses={
        200: {"description": "成功获取订单记录", "model": OrderRecordsResponseModel},
        400: {"description": "请求参数错误", "model": ErrorResponseModel},
        403: {"description": "用户未登录", "model": ErrorResponseModel},
        500: {"description": "服务器内部错误", "model": ErrorResponseModel},
    }
)
async def get_order_records(
    request: OrderRecordsRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_flexible),
):
    """分页获取当前登录用户的订单记录，按扣费时间倒序排列"""
    try:
        # 验证请求参数
        if request.pageNum < 1:
            return OrderRecordsResponseModel(
                code=400,
                message="pageNum必须大于0",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)

        if not (5 <= request.pageSize <= 50):
            return OrderRecordsResponseModel(
                code=400,
                message="pageSize必须在5-50之间",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)

        order_data = await user_auth_service.get_order_records(db, current_user.id, request.pageNum, request.pageSize)
        return OrderRecordsResponseModel(
            code=0,
            message="success",
            data=order_data
        )
    except HTTPException as e:
        error_detail = e.detail
        if "用户未登录" in error_detail:
            return OrderRecordsResponseModel(
                code=403,
                message="用户未登录",
            ).to_json(status_code=HTTP_403_FORBIDDEN)
        else:
            return OrderRecordsResponseModel(
                code=400,
                message="请求参数错误",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return OrderRecordsResponseModel(
            code=500,
            message="服务器内部错误，请稍后重试",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.get(
    "/balance",
    name="查询账户余额",
    response_model=BalanceQueryResponseModel,
    responses={
        200: {"description": "查询成功", "model": BalanceQueryResponseModel},
        401: {"description": "未授权，需要登录", "model": ErrorResponseModel},
        404: {"description": "用户不存在", "model": ErrorResponseModel},
        500: {"description": "服务器内部错误", "model": ErrorResponseModel},
    }
)
async def get_user_balance(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """查询当前用户的账户余额"""
    try:
        balance = await user_auth_service.get_user_balance(db, current_user.id)
        return BalanceQueryResponseModel(
            code=0,
            message="查询成功",
            data={"balance": float(balance)}
        )
    except HTTPException as e:
        error_detail = e.detail
        if "用户不存在" in error_detail:
            return BalanceQueryResponseModel(
                code=404,
                message="用户不存在",
            ).to_json(status_code=HTTP_404_NOT_FOUND)
        else:
            return BalanceQueryResponseModel(
                code=400,
                message=error_detail.split("_", 1)[1] if "_" in error_detail else error_detail,
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return BalanceQueryResponseModel(
            code=500,
            message="服务器内部错误，请稍后重试",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.post(
    "/balance/update",
    name="更新账户余额",
    response_model=BalanceUpdateResponseModel,
    responses={
        200: {"description": "更新成功", "model": BalanceUpdateResponseModel},
        400: {"description": "请求参数错误", "model": ErrorResponseModel},
        401: {"description": "未授权，需要登录", "model": ErrorResponseModel},
        404: {"description": "用户不存在", "model": ErrorResponseModel},
        500: {"description": "服务器内部错误", "model": ErrorResponseModel},
    }
)
async def update_user_balance(
    request: BalanceUpdateRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """更新用户账户余额（充值或扣除）"""
    try:
        result = await user_auth_service.update_user_balance(
            db, current_user.id, request.amount, request.operation
        )
        return BalanceUpdateResponseModel(
            code=0,
            message="余额更新成功",
            data=result
        )
    except HTTPException as e:
        error_detail = e.detail
        if "用户不存在" in error_detail:
            return BalanceUpdateResponseModel(
                code=404,
                message="用户不存在",
            ).to_json(status_code=HTTP_404_NOT_FOUND)
        elif "余额不足" in error_detail:
            return BalanceUpdateResponseModel(
                code=400,
                message="余额不足，无法扣除",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
        elif "操作类型无效" in error_detail:
            return BalanceUpdateResponseModel(
                code=400,
                message="操作类型无效，仅支持recharge（充值）或deduct（扣除）",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
        else:
            return BalanceUpdateResponseModel(
                code=400,
                message=error_detail.split("_", 1)[1] if "_" in error_detail else error_detail,
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return BalanceUpdateResponseModel(
            code=500,
            message="服务器内部错误，请稍后重试",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)
