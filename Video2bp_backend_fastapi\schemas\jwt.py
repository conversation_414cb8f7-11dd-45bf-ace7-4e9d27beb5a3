from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class JWTMeta(BaseModel):
    """JWT元数据模型"""
    exp: datetime  # 过期时间
    sub: str       # 用户ID
    iat: Optional[datetime] = None  # 签发时间


class JWTUser(BaseModel):
    """JWT用户信息模型"""
    username: str
    user_id: int
    email: Optional[str] = None


class JWTPayload(BaseModel):
    """JWT载荷模型"""
    sub: str  # 用户ID
    exp: int  # 过期时间戳
    iat: Optional[int] = None  # 签发时间戳


class TokenResponse(BaseModel):
    """Token响应模型"""
    access_token: str
    token_type: str = "bearer"
    expires_in: Optional[int] = None
