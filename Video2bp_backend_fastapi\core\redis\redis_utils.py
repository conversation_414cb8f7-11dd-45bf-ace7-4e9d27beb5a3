"""
Redis工具类
提供常用的Redis操作封装
"""

import json
import logging
from typing import Any, Optional, Union, List, Dict
from datetime import datetime, timedelta

from .redis_manager import get_redis, get_async_redis

logger = logging.getLogger(__name__)


class RedisUtils:
    """Redis工具类 - 同步版本"""
    
    @staticmethod
    def set_string(key: str, value: str, expire_seconds: Optional[int] = None) -> bool:
        """设置字符串值"""
        try:
            client = get_redis()
            if expire_seconds:
                return client.setex(key, expire_seconds, value)
            else:
                return client.set(key, value)
        except Exception as e:
            logger.error(f"Redis set_string error: {e}")
            return False
    
    @staticmethod
    def get_string(key: str) -> Optional[str]:
        """获取字符串值"""
        try:
            client = get_redis()
            return client.get(key)
        except Exception as e:
            logger.error(f"Redis get_string error: {e}")
            return None
    
    @staticmethod
    def set_json(key: str, value: Any, expire_seconds: Optional[int] = None) -> bool:
        """设置JSON值"""
        try:
            json_str = json.dumps(value, ensure_ascii=False)
            return RedisUtils.set_string(key, json_str, expire_seconds)
        except Exception as e:
            logger.error(f"Redis set_json error: {e}")
            return False
    
    @staticmethod
    def get_json(key: str) -> Optional[Any]:
        """获取JSON值"""
        try:
            json_str = RedisUtils.get_string(key)
            if json_str:
                return json.loads(json_str)
            return None
        except Exception as e:
            logger.error(f"Redis get_json error: {e}")
            return None
    
    @staticmethod
    def delete(key: str) -> bool:
        """删除键"""
        try:
            client = get_redis()
            return bool(client.delete(key))
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    @staticmethod
    def exists(key: str) -> bool:
        """检查键是否存在"""
        try:
            client = get_redis()
            return bool(client.exists(key))
        except Exception as e:
            logger.error(f"Redis exists error: {e}")
            return False
    
    @staticmethod
    def expire(key: str, seconds: int) -> bool:
        """设置键的过期时间"""
        try:
            client = get_redis()
            return bool(client.expire(key, seconds))
        except Exception as e:
            logger.error(f"Redis expire error: {e}")
            return False
    
    @staticmethod
    def ttl(key: str) -> int:
        """获取键的剩余生存时间"""
        try:
            client = get_redis()
            return client.ttl(key)
        except Exception as e:
            logger.error(f"Redis ttl error: {e}")
            return -1
    
    @staticmethod
    def incr(key: str, amount: int = 1) -> Optional[int]:
        """递增计数器"""
        try:
            client = get_redis()
            return client.incr(key, amount)
        except Exception as e:
            logger.error(f"Redis incr error: {e}")
            return None
    
    @staticmethod
    def decr(key: str, amount: int = 1) -> Optional[int]:
        """递减计数器"""
        try:
            client = get_redis()
            return client.decr(key, amount)
        except Exception as e:
            logger.error(f"Redis decr error: {e}")
            return None


class AsyncRedisUtils:
    """Redis工具类 - 异步版本"""
    
    @staticmethod
    async def set_string(key: str, value: str, expire_seconds: Optional[int] = None) -> bool:
        """设置字符串值"""
        try:
            client = await get_async_redis()
            if expire_seconds:
                return await client.setex(key, expire_seconds, value)
            else:
                return await client.set(key, value)
        except Exception as e:
            logger.error(f"Redis async set_string error: {e}")
            return False
    
    @staticmethod
    async def get_string(key: str) -> Optional[str]:
        """获取字符串值"""
        try:
            client = await get_async_redis()
            return await client.get(key)
        except Exception as e:
            logger.error(f"Redis async get_string error: {e}")
            return None
    
    @staticmethod
    async def set_json(key: str, value: Any, expire_seconds: Optional[int] = None) -> bool:
        """设置JSON值"""
        try:
            json_str = json.dumps(value, ensure_ascii=False)
            return await AsyncRedisUtils.set_string(key, json_str, expire_seconds)
        except Exception as e:
            logger.error(f"Redis async set_json error: {e}")
            return False
    
    @staticmethod
    async def get_json(key: str) -> Optional[Any]:
        """获取JSON值"""
        try:
            json_str = await AsyncRedisUtils.get_string(key)
            if json_str:
                return json.loads(json_str)
            return None
        except Exception as e:
            logger.error(f"Redis async get_json error: {e}")
            return None
    
    @staticmethod
    async def delete(key: str) -> bool:
        """删除键"""
        try:
            client = await get_async_redis()
            return bool(await client.delete(key))
        except Exception as e:
            logger.error(f"Redis async delete error: {e}")
            return False
    
    @staticmethod
    async def exists(key: str) -> bool:
        """检查键是否存在"""
        try:
            client = await get_async_redis()
            return bool(await client.exists(key))
        except Exception as e:
            logger.error(f"Redis async exists error: {e}")
            return False
    
    @staticmethod
    async def expire(key: str, seconds: int) -> bool:
        """设置键的过期时间"""
        try:
            client = await get_async_redis()
            return bool(await client.expire(key, seconds))
        except Exception as e:
            logger.error(f"Redis async expire error: {e}")
            return False
    
    @staticmethod
    async def ttl(key: str) -> int:
        """获取键的剩余生存时间"""
        try:
            client = await get_async_redis()
            return await client.ttl(key)
        except Exception as e:
            logger.error(f"Redis async ttl error: {e}")
            return -1
    
    @staticmethod
    async def incr(key: str, amount: int = 1) -> Optional[int]:
        """递增计数器"""
        try:
            client = await get_async_redis()
            return await client.incr(key, amount)
        except Exception as e:
            logger.error(f"Redis async incr error: {e}")
            return None
    
    @staticmethod
    async def decr(key: str, amount: int = 1) -> Optional[int]:
        """递减计数器"""
        try:
            client = await get_async_redis()
            return await client.decr(key, amount)
        except Exception as e:
            logger.error(f"Redis async decr error: {e}")
            return None


# 缓存装饰器
def redis_cache(key_prefix: str, expire_seconds: int = 3600):
    """Redis缓存装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = RedisUtils.get_json(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            RedisUtils.set_json(cache_key, result, expire_seconds)
            return result
        return wrapper
    return decorator


def async_redis_cache(key_prefix: str, expire_seconds: int = 3600):
    """异步Redis缓存装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = await AsyncRedisUtils.get_json(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await AsyncRedisUtils.set_json(cache_key, result, expire_seconds)
            return result
        return wrapper
    return decorator
