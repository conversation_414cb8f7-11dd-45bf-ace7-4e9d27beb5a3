# 用户注册功能文档

## 概述

本项目实现了基本的用户注册功能，支持邮箱密码注册，无需邮箱验证码，提供简单快捷的用户注册体验。

## 功能特性

### 1. 基本注册
- **邮箱密码注册**：用户使用邮箱和密码进行注册
- **用户名可选**：可以提供用户名，如果不提供则使用邮箱前缀作为用户名
- **自动验证**：自动检查邮箱和用户名是否已被注册
- **安全密码**：密码使用MD5加密存储（与原Flask版本兼容）

### 2. 数据验证
- **邮箱格式验证**：确保邮箱格式正确
- **密码长度验证**：密码长度6-32位
- **用户名长度验证**：用户名长度3-20位（可选）
- **重复性检查**：防止邮箱和用户名重复注册

### 3. 安全特性
- **统一业务码**：使用统一的业务响应码
- **错误信息隐藏**：系统错误不暴露技术细节
- **数据库事务**：确保数据一致性

## API接口

### 注册接口

```http
POST /api/user/register
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123",
    "username": "myusername"  // 可选
}
```

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| email | string | 是 | 邮箱地址，必须是有效的邮箱格式 |
| password | string | 是 | 密码，长度6-32位 |
| username | string | 否 | 用户名，长度3-20位，不提供则使用邮箱前缀 |

#### 响应格式

**注册成功 (200)**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "email": "<EMAIL>",
        "username": "myusername",
        "created_at": "2024-01-15T10:30:45.123456"
    }
}
```

**邮箱已存在 (401)**:
```json
{
    "code": 1102,
    "message": "邮箱已被注册",
    "data": null
}
```

**用户名已存在 (401)**:
```json
{
    "code": 1101,
    "message": "用户已存在",
    "data": null
}
```

**参数验证失败 (422)**:
```json
{
    "detail": [
        {
            "type": "string_too_short",
            "loc": ["body", "password"],
            "msg": "String should have at least 6 characters",
            "input": "123"
        }
    ]
}
```

**系统错误 (500)**:
```json
{
    "code": 5000,
    "message": "系统繁忙，请稍后重试",
    "data": null
}
```

## 业务码说明

| 业务码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 0 | 注册成功 | 200 |
| 1101 | 用户已存在 | 401 |
| 1102 | 邮箱已被注册 | 401 |
| 5000 | 系统繁忙 | 500 |

## 数据库设计

### users表结构

```sql
CREATE TABLE `users` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    `username` VARCHAR(80) NULL COMMENT '用户名',
    `email` VARCHAR(120) NOT NULL UNIQUE COMMENT '邮箱',
    `phone` VARCHAR(120) NULL COMMENT '手机号',
    `password` VARCHAR(128) NOT NULL COMMENT '密码',
    `balance` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '账户余额',
    `vip_level` VARCHAR(80) NOT NULL DEFAULT '1' COMMENT 'VIP等级',
    `status` SMALLINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `current_plan_id` INT NULL COMMENT '当前套餐ID',
    `failed_login_attempts` INT NOT NULL DEFAULT 0 COMMENT '登录失败次数',
    `last_failed_login` DATETIME NULL COMMENT '最后一次登录失败时间',
    `account_locked_until` DATETIME NULL COMMENT '账户锁定到期时间',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_email` (`email`),
    INDEX `idx_username` (`username`)
);
```

## 代码结构

```
apps/app_user_auth/
├── api.py                 # 注册API接口
├── service.py             # 注册业务逻辑
├── schema.py              # 请求响应模型
└── ...

apps/app_user/
├── model.py               # 用户数据模型
└── ...

core/
├── constants/
│   └── business_codes.py  # 业务码定义
└── ...
```

## 使用示例

### 1. Python requests示例

```python
import requests

# 注册用户
response = requests.post(
    "http://localhost:8000/api/user/register",
    json={
        "email": "<EMAIL>",
        "password": "test123456",
        "username": "testuser"
    }
)

if response.status_code == 200:
    data = response.json()
    if data['code'] == 0:
        print(f"注册成功! 用户ID: {data['data']['id']}")
    else:
        print(f"注册失败: {data['message']}")
```

### 2. JavaScript fetch示例

```javascript
fetch('http://localhost:8000/api/user/register', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'test123456',
        username: 'testuser'
    })
})
.then(response => response.json())
.then(data => {
    if (data.code === 0) {
        console.log('注册成功!', data.data);
    } else {
        console.log('注册失败:', data.message);
    }
});
```

### 3. curl命令示例

```bash
curl -X POST "http://localhost:8000/api/user/register" \
     -H "Content-Type: application/json" \
     -d '{
         "email": "<EMAIL>",
         "password": "test123456",
         "username": "testuser"
     }'
```

## 测试

### 运行API测试

```bash
# 1. 启动FastAPI服务器
python main.py

# 2. 在新终端运行API测试
python test_register_api.py
```

### 测试用例

测试脚本包含以下测试用例：
1. **基本注册测试**：正常的邮箱密码用户名注册
2. **无用户名注册测试**：只提供邮箱和密码
3. **重复邮箱测试**：使用已注册的邮箱
4. **密码过短测试**：密码长度不足
5. **邮箱格式错误测试**：无效的邮箱格式

## 注意事项

1. **数据库连接**：确保MySQL数据库正在运行且连接配置正确
2. **表结构同步**：运行数据库迁移确保表结构最新
3. **密码安全**：当前使用MD5加密，生产环境建议使用更安全的加密方式
4. **邮箱验证**：当前版本不包含邮箱验证，后续可以添加
5. **用户名唯一性**：确保用户名在数据库中的唯一性约束

## 后续扩展

1. **邮箱验证**：添加邮箱验证码功能
2. **手机注册**：支持手机号注册
3. **社交登录**：集成第三方登录（微信、QQ等）
4. **密码强度**：增强密码强度验证
5. **用户协议**：添加用户协议和隐私政策确认
