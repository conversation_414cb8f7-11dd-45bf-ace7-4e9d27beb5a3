#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNG to WebP 批量转换工具
"""

import os
import argparse
from PIL import Image

def convert_png_to_webp(input_dir, output_dir, quality=85, lossless=False):
    """
    批量转换PNG到WebP
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        quality: 压缩质量 (0-100)
        lossless: 是否使用无损压缩
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    converted_count = 0
    failed_count = 0
    
    for root, dirs, files in os.walk(input_dir):
        for file in files:
            if file.lower().endswith('.png'):  # ✅ 修复：改为 .png
                png_path = os.path.join(root, file)
                
                # 生成相对路径保持目录结构
                rel_path = os.path.relpath(png_path, input_dir)
                webp_path = os.path.join(output_dir, rel_path)
                webp_path = os.path.splitext(webp_path)[0] + '.webp'
                
                # 创建必要的子目录
                os.makedirs(os.path.dirname(webp_path), exist_ok=True)
                
                try:
                    with Image.open(png_path) as img:
                        if lossless:
                            img.save(webp_path, 'WebP', lossless=True)
                        else:
                            img.save(webp_path, 'WebP', quality=quality)
                    
                    print(f"✓ {rel_path}")
                    converted_count += 1
                    
                except Exception as e:
                    print(f"✗ 转换失败 {rel_path}: {e}")
                    failed_count += 1
    
    print(f"\n转换完成！成功: {converted_count}, 失败: {failed_count}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='PNG to WebP 批量转换工具')
    parser.add_argument('input_dir', help='输入目录路径')
    parser.add_argument('output_dir', help='输出目录路径')
    parser.add_argument('-q', '--quality', type=int, default=85, help='压缩质量 (0-100)')
    parser.add_argument('-l', '--lossless', action='store_true', help='使用无损压缩')
    
    args = parser.parse_args()
    
    convert_png_to_webp(args.input_dir, args.output_dir, args.quality, args.lossless)
