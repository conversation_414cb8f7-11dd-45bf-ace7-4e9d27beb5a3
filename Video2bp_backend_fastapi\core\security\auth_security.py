"""
认证安全服务
提供安全的用户认证功能，包括登录失败锁定、日志记录等
"""

import asyncio
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Optional, Tu<PERSON>

from fastapi import HTTPException
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_401_UNAUTHORIZED, HTTP_500_INTERNAL_SERVER_ERROR

from apps.app_user.model import User
from core.config import settings
from core.constants import BusinessCode, BusinessMessage, AuthSecurityConfig
from core.redis import get_redis
from .security_logger import (
    log_login_attempt,
    log_account_locked,
    log_account_unlocked,
    log_system_error
)


class AuthSecurityService:
    """认证安全服务类"""
    
    def __init__(self):
        """初始化认证安全服务"""
        self.redis_client = get_redis()
    
    def _get_login_attempts_key(self, email: str) -> str:
        """获取登录尝试次数的Redis键"""
        return f"{AuthSecurityConfig.LOGIN_ATTEMPTS_KEY_PREFIX}{email}"
    
    def _get_account_locked_key(self, email: str) -> str:
        """获取账号锁定状态的Redis键"""
        return f"{AuthSecurityConfig.ACCOUNT_LOCKED_KEY_PREFIX}{email}"
    
    def _mask_email(self, email: str) -> str:
        """屏蔽邮箱敏感信息用于日志记录"""
        if not AuthSecurityConfig.MASK_SENSITIVE_DATA:
            return email
        
        if "@" in email:
            local, domain = email.split("@", 1)
            if len(local) <= 2:
                masked_local = "*" * len(local)
            else:
                masked_local = local[0] + "*" * (len(local) - 2) + local[-1]
            return f"{masked_local}@{domain}"
        return email
    
    async def _log_security_event(self, event_type: str, email: str, details: str, ip_address: str = None):
        """记录安全事件到日志"""
        # 使用专门的安全日志记录器
        if event_type in ["LOGIN_SUCCESS"]:
            log_login_attempt(email, True, ip_address)
        elif event_type in ["LOGIN_FAILED_USER_NOT_FOUND", "LOGIN_FAILED_WRONG_PASSWORD"]:
            reason = "user_not_found" if "NOT_FOUND" in event_type else "wrong_password"
            log_login_attempt(email, False, ip_address, reason)
        elif event_type == "ACCOUNT_LOCKED":
            log_account_locked(email, AuthSecurityConfig.LOCKOUT_DURATION, ip_address)
        elif event_type == "ACCOUNT_MANUALLY_UNLOCKED":
            log_account_unlocked(email, "manual")
        elif event_type in ["LOGIN_SYSTEM_ERROR"]:
            log_system_error("authentication", details, email)
    
    async def _force_delay(self, delay_seconds: int):
        """强制延迟，防止暴力破解"""
        await asyncio.sleep(delay_seconds)
    
    async def _get_login_attempts(self, email: str) -> int:
        """获取登录失败次数"""
        key = self._get_login_attempts_key(email)
        attempts = self.redis_client.get(key)
        return int(attempts) if attempts else 0
    
    async def _increment_login_attempts(self, email: str) -> int:
        """增加登录失败次数"""
        key = self._get_login_attempts_key(email)
        attempts = self.redis_client.incr(key)
        # 设置过期时间为统计窗口时长
        self.redis_client.expire(key, AuthSecurityConfig.LOGIN_ATTEMPT_WINDOW)
        return attempts
    
    async def _reset_login_attempts(self, email: str):
        """重置登录失败次数"""
        key = self._get_login_attempts_key(email)
        self.redis_client.delete(key)
    
    async def _is_account_locked(self, email: str) -> bool:
        """检查账号是否被锁定"""
        key = self._get_account_locked_key(email)
        locked_until = self.redis_client.get(key)
        if locked_until:
            locked_until_time = datetime.fromisoformat(locked_until)
            if datetime.now() < locked_until_time:
                return True
            else:
                # 锁定时间已过，删除锁定记录
                self.redis_client.delete(key)
        return False
    
    async def _lock_account(self, email: str):
        """锁定账号"""
        key = self._get_account_locked_key(email)
        locked_until = datetime.now() + timedelta(seconds=AuthSecurityConfig.LOCKOUT_DURATION)
        self.redis_client.setex(key, AuthSecurityConfig.LOCKOUT_DURATION, locked_until.isoformat())
        
        await self._log_security_event(
            "ACCOUNT_LOCKED",
            email,
            f"Account locked for {AuthSecurityConfig.LOCKOUT_DURATION} seconds due to too many failed login attempts"
        )
    
    def _hash_password(self, password: str) -> str:
        """使用MD5加密密码（保持与现有系统兼容）"""
        return hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    
    async def authenticate_user(
        self, 
        db: AsyncSession, 
        email: str, 
        password: str,
        ip_address: str = None
    ) -> Tuple[Optional[User], BusinessCode]:
        """
        安全的用户认证
        
        Args:
            db: 数据库会话
            email: 邮箱
            password: 密码
            ip_address: 客户端IP地址
            
        Returns:
            Tuple[Optional[User], BusinessCode]: 用户对象和业务码
        """
        try:
            # 1. 检查账号是否被锁定
            if await self._is_account_locked(email):
                await self._force_delay(AuthSecurityConfig.LOGIN_DELAY_WHEN_LOCKED)
                await self._log_security_event(
                    "LOGIN_ATTEMPT_WHILE_LOCKED",
                    email,
                    "Login attempt while account is locked",
                    ip_address
                )
                return None, BusinessCode.AUTH_ACCOUNT_LOCKED
            
            # 2. 查询用户
            result = await db.execute(select(User).filter(User.email == email))
            user = result.scalars().first()
            
            # 3. 验证用户存在性和密码
            password_correct = False
            if user and user.check_password(password):
                password_correct = True
            
            # 4. 处理认证结果
            if password_correct:
                # 认证成功，重置失败次数
                await self._reset_login_attempts(email)
                await self._log_security_event(
                    "LOGIN_SUCCESS",
                    email,
                    "User login successful",
                    ip_address
                )
                return user, BusinessCode.SUCCESS
            else:
                # 认证失败，增加失败次数
                attempts = await self._increment_login_attempts(email)
                
                # 强制延迟
                await self._force_delay(AuthSecurityConfig.LOGIN_DELAY_AFTER_FAILED)
                
                # 记录失败原因（仅在日志中区分，响应统一）
                if not user:
                    await self._log_security_event(
                        "LOGIN_FAILED_USER_NOT_FOUND",
                        email,
                        f"Login failed: user not found (attempt {attempts})",
                        ip_address
                    )
                else:
                    await self._log_security_event(
                        "LOGIN_FAILED_WRONG_PASSWORD",
                        email,
                        f"Login failed: wrong password (attempt {attempts})",
                        ip_address
                    )
                
                # 检查是否需要锁定账号
                if attempts >= AuthSecurityConfig.MAX_LOGIN_ATTEMPTS:
                    await self._lock_account(email)
                    return None, BusinessCode.AUTH_ACCOUNT_LOCKED
                
                return None, BusinessCode.AUTH_LOGIN_FAILED
                
        except Exception as e:
            # 系统错误，记录日志但不暴露具体错误信息
            await self._log_security_event(
                "LOGIN_SYSTEM_ERROR",
                email,
                f"System error during login: {str(e)}",
                ip_address
            )
            return None, BusinessCode.SYSTEM_ERROR
    
    async def get_remaining_lockout_time(self, email: str) -> Optional[int]:
        """
        获取账号剩余锁定时间（秒）
        
        Args:
            email: 邮箱
            
        Returns:
            Optional[int]: 剩余锁定时间，None表示未锁定
        """
        key = self._get_account_locked_key(email)
        locked_until = self.redis_client.get(key)
        if locked_until:
            locked_until_time = datetime.fromisoformat(locked_until)
            remaining = (locked_until_time - datetime.now()).total_seconds()
            return max(0, int(remaining))
        return None
    
    async def unlock_account(self, email: str):
        """
        手动解锁账号（管理员功能）
        
        Args:
            email: 邮箱
        """
        await self._reset_login_attempts(email)
        key = self._get_account_locked_key(email)
        self.redis_client.delete(key)
        
        await self._log_security_event(
            "ACCOUNT_MANUALLY_UNLOCKED",
            email,
            "Account manually unlocked by administrator"
        )
