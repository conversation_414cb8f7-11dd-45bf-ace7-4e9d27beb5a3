<template>
  <div class="capture-page">
    <div class="capture-content">
      <div class="main-container">
        <!-- 左侧三块区域 -->
        <div class="left-section">
          <!-- 第一块：视频上传区域 -->
          <div class="upload-area">
            <!-- 如果没有视频，显示文件选择组件 -->
            <SimpleVideoUploader v-if="!localVideoUrl" @file-selected="handleFileSelected"
              @file-error="handleFileError" />
            <!-- 如果有视频，显示视频播放器 -->
            <div v-else class="video-player-container">
              <video ref="videoPlayer" :src="localVideoUrl" class="uploaded-video" crossorigin="anonymous"
                @loadedmetadata="handleVideoLoaded" @loadeddata="handleVideoLoaded" @canplay="handleVideoLoaded"
                @timeupdate="handleTimeUpdate" @ended="handleVideoEnded">
                您的浏览器不支持视频播放。
              </video>
            </div>
          </div>

          <!-- 第二块：视频控制栏 -->
          <div class="video-controls">
            <div class="controls-left">
              <div class="time-display">{{ displayTime }}</div>
            </div>

            <div class="controls-center">
              <!-- 视频起始点 -->
              <FastBackwardOutlined @click="goToStart" class="control-icon" />
              <!-- 后退一帧 -->
              <StepBackwardOutlined @click="stepBackward" class="control-icon" />
              <!-- 播放/暂停 -->
              <CaretRightOutlined v-if="!isPlaying" @click="togglePlayPause" class="control-icon play-icon" />
              <PauseOutlined v-else @click="togglePlayPause" class="control-icon play-icon" />
              <!-- 前进一帧 -->
              <StepForwardOutlined @click="stepForward" class="control-icon" />
              <!-- 视频结束点 -->
              <FastForwardOutlined @click="goToEnd" class="control-icon" />
            </div>

            <div class="controls-right">
              <button class="control-btn volume-btn" @click="toggleMute">
                <svg v-if="!isMuted" width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                  <path
                    d="M19.07 4.93C20.9447 6.80528 21.9979 9.34836 21.9979 12C21.9979 14.6516 20.9447 17.1947 19.07 19.07"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M15.54 8.46C16.4774 9.39764 17.0039 10.6692 17.0039 12C17.0039 13.3308 16.4774 14.6024 15.54 15.54"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                  <line x1="23" y1="9" x2="17" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                  <line x1="17" y1="9" x2="23" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
              </button>
            </div>
          </div>

          <!-- 第三块：视频详情区域 -->
          <div class="video-details">
            <div class="video-editing-header">
              <div class="video-edit-tips">视频编辑</div>
              <div class="editing-actions">
                <div class="action-btn secondary" @click="clearVideo">
                  <i class="iconfont icon-reset"></i> 重置
                </div>
                <div class="action-btn undo" @click="undoSelection" v-if="selectionStart !== selectionEnd">
                  <UndoOutlined /> 撤销裁剪
                </div>
                <div class="action-btn primary" @click="saveSelection">
                  <SaveOutlined /> 保存
                </div>
                <div class="action-btn cut" @click="previewCut" v-if="selectionStart !== selectionEnd">
                  ✂️ 预览剪切
                </div>
              </div>
            </div>
            <div class="video-editing-process">
              <VideoCropper v-if="localVideoUrl && duration > 0" :video-url="localVideoUrl" :duration="duration"
                v-model:current-time="currentTime" v-model:selection-start="selectionStart"
                v-model:selection-end="selectionEnd" />

              <div v-else class="timeline-placeholder">
                <div class="placeholder-text">选择视频后将在此显示视频编辑工具</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧导出格式选项 -->
        <div class="export-section">
          <div class="export-title">输出格式</div>
          <div class="format-options">
            <div v-for="(format, index) in formatList" :key="index" class="format-option"
              :class="{ selected: selectedFormatIndex === index }" @click="selectFormat(index)">
              <div class="format-name">{{ format.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 动作生成Modal -->
    <ActionGenerationModal v-model:open="showActionModal" @start-generation="handleStartGeneration"
      :is-uploading="isUploading" :upload-progress="uploadProgress" />

    <!-- 全页面进度条Loading -->
    <div v-if="isUploading" class="page-loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
        </div>
        <div class="loading-text">
          {{ loadingText }}
        </div>
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
          </div>
          <div class="progress-percentage">{{ uploadProgress }}%</div>
        </div>
        <div class="loading-tip">
          请耐心等待，正在处理您的视频...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useApi } from '~/composables/useApi'
import {
  PauseOutlined,
  CaretRightOutlined,
  FastBackwardOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  FastForwardOutlined,
  SaveOutlined,
  UndoOutlined
} from '@ant-design/icons-vue'
import SimpleVideoUploader from './components/SimpleVideoUploader.vue'

import VideoCropper from './components/VideoCropper.vue'
import ActionGenerationModal from './components/ActionGenerationModal.vue'
import { formatTime } from '~/utils'

defineOptions({
  name: 'CaptureView',
})

// API实例
const { capture } = useApi()

// 状态管理
const uploadedVideoUrl = ref<string>('')
const selectedFile = ref<File | null>(null)
const localVideoUrl = ref<string>('')
const videoPlayer = ref<HTMLVideoElement>()

// 视频控制状态
const isPlaying = ref(false) // 是否正在播放
const currentTime = ref(0) // 当前播放时间
const duration = ref(0) // 视频总时长
const volume = ref(1) // 音量
const isMuted = ref(false) // 是否静音
const frameRate = ref(30) // 默认帧率，实际会从视频中获取

// 时间轴选择状态
const selectionStart = ref(0)
const selectionEnd = ref(0)

// 预览定时器
const previewTimer = ref<NodeJS.Timeout | null>(null)

// 格式选项数据
const formatList = ref([
  { label: 'Gif FBX', value: 1 },
  { label: 'Gif FBX', value: 2 },
  { label: 'Gif FBX', value: 3 },
  { label: 'Gif FBX', value: 4 },
])

// 选中的格式索引
const selectedFormatIndex = ref(0)

// Modal控制
const showActionModal = ref(false)

// 上传状态
const isUploading = ref(false)
const uploadProgress = ref(0)

// 加载状态文本
const loadingText = computed(() => {
  if (!isUploading.value) return ''

  const progress = uploadProgress.value
  if (progress === 0) {
    return '正在初始化处理...'
  } else if (progress <= 30) {
    return '正在截取视频片段...'
  } else if (progress <= 60) {
    return '视频截取处理中...'
  } else if (progress <= 90) {
    return '正在上传视频文件...'
  } else if (progress < 100) {
    return '正在创建文件记录...'
  } else {
    return '处理完成！'
  }
})

// 处理文件选择
const handleFileSelected = (file: File) => {
  // 清理之前的本地URL
  if (localVideoUrl.value) {
    URL.revokeObjectURL(localVideoUrl.value)
  }

  // 存储文件和创建本地预览URL
  selectedFile.value = file
  localVideoUrl.value = URL.createObjectURL(file)

  // 等待DOM更新后检查视频元素
  nextTick(() => {
    if (videoPlayer.value) {
      // 如果视频已经有duration，立即设置
      if (videoPlayer.value.duration > 0) {
        duration.value = videoPlayer.value.duration
      }
    }
  })
}

// 处理文件选择错误
const handleFileError = (error: string) => {
  message.error('视频选择失败:' + error)
}

// 计算显示时间
const displayTime = computed(() => {
  return `${formatTime(currentTime.value)}/${formatTime(duration.value)}`
})

// 处理视频加载完成
const handleVideoLoaded = () => {
  if (videoPlayer.value) {
    duration.value = videoPlayer.value.duration

    // 如果duration是NaN或无效值，尝试重新获取
    if (isNaN(duration.value) || duration.value <= 0) {
      setTimeout(() => {
        if (videoPlayer.value && videoPlayer.value.duration > 0) {
          duration.value = videoPlayer.value.duration
        }
      }, 100)
    }
  }
}

// 时间轴点击 切换视频播放时间
watch(currentTime, (newTime) => {
  if (videoPlayer.value && videoPlayer.value.readyState >= videoPlayer.value.HAVE_METADATA) {
    const difference = Math.abs(videoPlayer.value.currentTime - newTime);
    if (difference > 0.1) { // 设置一个小的阈值
      videoPlayer.value.currentTime = newTime;
    }
  }
});

// 处理视频播放时间更新
const handleTimeUpdate = () => {
  if (videoPlayer.value) {
    const videoElementTime = videoPlayer.value.currentTime;
    if (Math.abs(currentTime.value - videoElementTime) > 0.01) { // 可以用一个更小的阈值
      currentTime.value = videoElementTime;
    }
  }
};

// 处理视频结束
const handleVideoEnded = () => {
  isPlaying.value = false
}

// 播放/暂停切换
const togglePlayPause = () => {
  if (!videoPlayer.value) {
    return
  }

  if (isPlaying.value) {
    videoPlayer.value.pause()
    isPlaying.value = false
  } else {
    videoPlayer.value.play()
    isPlaying.value = true
  }
}

// 跳转到视频开始
const goToStart = () => {
  if (!videoPlayer.value) return
  videoPlayer.value.currentTime = 0
  currentTime.value = 0
}

// 跳转到视频结束
const goToEnd = () => {
  if (!videoPlayer.value) return
  videoPlayer.value.currentTime = duration.value
  currentTime.value = duration.value
}

// 音量控制
const toggleMute = () => {
  if (!videoPlayer.value) return

  if (isMuted.value) {
    videoPlayer.value.volume = volume.value
    isMuted.value = false
  } else {
    videoPlayer.value.volume = 0
    isMuted.value = true
  }
}

// 前进一帧
const stepForward = () => {
  if (!videoPlayer.value) return

  // 暂停视频
  if (isPlaying.value) {
    videoPlayer.value.pause()
    isPlaying.value = false
  }

  // 计算一帧的时间（秒）
  const frameTime = 1 / frameRate.value
  const newTime = Math.min(currentTime.value + frameTime, duration.value)

  videoPlayer.value.currentTime = newTime
  currentTime.value = newTime
}

// 后退一帧
const stepBackward = () => {
  if (!videoPlayer.value) return

  // 暂停视频
  if (isPlaying.value) {
    videoPlayer.value.pause()
    isPlaying.value = false
  }

  // 计算一帧的时间（秒）
  const frameTime = 1 / frameRate.value
  const newTime = Math.max(currentTime.value - frameTime, 0)

  videoPlayer.value.currentTime = newTime
  currentTime.value = newTime
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!localVideoUrl.value) return

  switch (event.code) {
    case 'Space':
      event.preventDefault()
      togglePlayPause()
      break
    case 'ArrowLeft':
      event.preventDefault()
      stepBackward()
      break
    case 'ArrowRight':
      event.preventDefault()
      stepForward()
      break
    case 'Home':
      event.preventDefault()
      goToStart()
      break
    case 'End':
      event.preventDefault()
      goToEnd()
      break
    case 'KeyM':
      event.preventDefault()
      toggleMute()
      break
  }
}

// 组件挂载时添加键盘监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时移除键盘监听和清理内存
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  // 清理本地URL
  if (localVideoUrl.value) {
    URL.revokeObjectURL(localVideoUrl.value)
  }
})

// =======================
// 时间轴选择和制作视频相关
// =======================

// 获取当前选择范围
const getSelectionRange = () => {
  return {
    start: selectionStart.value,
    end: selectionEnd.value,
    duration: selectionEnd.value - selectionStart.value
  }
}

// 撤销裁剪选择
const undoSelection = () => {
  // 重置选择范围
  selectionStart.value = 0
  selectionEnd.value = 0

  // 重置播放时间到开始位置
  currentTime.value = 0
  if (videoPlayer.value) {
    videoPlayer.value.currentTime = 0
  }

  console.log('已撤销裁剪选择，重置为完整视频')
}

// 保存选择设置
const saveSelection = () => {
  if (!selectedFile.value) return;
  showActionModal.value = true
}

// 预览剪切效果
const previewCut = () => {
  if (!videoPlayer.value) return

  const selection = getSelectionRange()
  console.log(`预览剪切片段: ${selection.start.toFixed(2)}s - ${selection.end.toFixed(2)}s`)

  // 跳转到选择开始位置
  videoPlayer.value.currentTime = selection.start
  currentTime.value = selection.start

  // 播放选择的片段
  if (!isPlaying.value) {
    togglePlayPause()
  }

  // 清除之前的定时器
  if (previewTimer.value) {
    clearTimeout(previewTimer.value)
    previewTimer.value = null
  }

  // 监听时间更新，在到达结束位置时暂停
  const checkEndTime = () => {
    if (videoPlayer.value && videoPlayer.value.currentTime >= selection.end) {
      videoPlayer.value.pause()
      isPlaying.value = false
      videoPlayer.value.removeEventListener('timeupdate', checkEndTime)
      if (previewTimer.value) {
        clearTimeout(previewTimer.value)
        previewTimer.value = null
      }
    }
  }

  videoPlayer.value.addEventListener('timeupdate', checkEndTime)

  // 备用定时器，防止事件监听失效
  previewTimer.value = setTimeout(() => {
    if (videoPlayer.value && isPlaying.value) {
      videoPlayer.value.pause()
      isPlaying.value = false
      videoPlayer.value.removeEventListener('timeupdate', checkEndTime)
    }
    previewTimer.value = null
  }, (selection.end - selection.start + 0.5) * 1000) // 多加0.5秒缓冲
}

// 清除视频，重新选择
const clearVideo = () => {
  // 清理本地URL
  if (localVideoUrl.value) {
    URL.revokeObjectURL(localVideoUrl.value)
  }

  uploadedVideoUrl.value = ''
  selectedFile.value = null
  localVideoUrl.value = ''
  isPlaying.value = false
  currentTime.value = 0
  duration.value = 0
  selectionStart.value = 0
  selectionEnd.value = 0
}

// 选择格式选项
const selectFormat = (index: number) => {
  selectedFormatIndex.value = index
  console.log('选择格式:', formatList.value[index].label, '索引:', index)
}

// 弹窗的保存按钮
const handleStartGeneration = async (data: any) => {
  console.log('开始动作生成:', data)
  showActionModal.value = false;

  // 如果没有选择文件，直接返回
  if (!selectedFile.value) {
    message.warning('没有选择文件')
    return
  }

  try {
    // 开始处理
    isUploading.value = true
    uploadProgress.value = 0

    // 获取选择范围
    const selection = getSelectionRange()
    const { useVideoProcessor } = await import('~/composables/useVideoProcessor')
    const { trimVideo, needsTrimming, getVideoInfo } = useVideoProcessor()

    let fileToUpload = selectedFile.value

    // 检查是否需要截取视频
    const videoInfo = await getVideoInfo(selectedFile.value)
    const shouldTrim = needsTrimming(selection.start, selection.end, videoInfo.duration)

    if (shouldTrim) {
      // 截取视频
      fileToUpload = await trimVideo(
        selectedFile.value,
        {
          startTime: selection.start,
          endTime: selection.end,
          outputFormat: 'mp4'
        },
        {
          onProgress: (progress) => {
            uploadProgress.value = Math.round(progress * 0.6) // 截取占60%进度
            console.log('截取进度:', progress + '%')
          },
          onLog: (message) => {
            console.log('截取日志:', message)
          }
        }
      )

      console.log('视频截取完成，开始上传截取后的视频...')
    } else {
      console.log('无需截取，直接上传完整视频...')
    }

    // 上传进度回调
    const onProgress = (e: any) => {
      if (e.total) {
        const baseProgress = shouldTrim ? 60 : 0 // 如果截取了，上传从60%开始
        const uploadProgressPercent = Math.round((e.loaded / e.total) * (shouldTrim ? 30 : 90)) // 上传占30%或90%
        uploadProgress.value = baseProgress + uploadProgressPercent
        console.log('上传进度:', uploadProgress.value + '%')
      }
    }

    // 上传视频
    const uploadResult: any = await capture?.uploadSVideo(
      fileToUpload,
      onProgress
    )

    if (!uploadResult?.url) {
      throw new Error('上传失败，未获取到视频URL')
    }

    // 创建文件参数
    const selectionData = {
      id: uploadResult.id,
      videoUrl: uploadResult.url,
      startTime: shouldTrim ? 0 : selection.start,
      endTime: shouldTrim ? (selection.end - selection.start) : selection.end,
      duration: shouldTrim ? (selection.end - selection.start) : selection.duration,
      originalSelection: selection, // 原始选择信息
      wasTrimmed: shouldTrim, // 是否进行了裁剪
      formData: data, // 表单数据
      outputFormat: formatList.value[selectedFormatIndex.value].value
    }

    // 更新进度到90%，开始创建文件
    uploadProgress.value = 90

    // 创建文件
    const createFileResult = await capture?.createFile(selectionData)

    if (!createFileResult) {
      throw '文件创建失败：未知错误'
    }

    uploadProgress.value = 100
    message.success('文件创建成功！')

    setTimeout(() => {
      navigateTo('/my-project')
    }, 1000)


  } catch (error) {
    console.error('处理失败:', error)

    const progress = uploadProgress.value
    let errorMessage = '处理失败，请重试'

    if (progress < 60) {
      errorMessage = '视频截取失败，请检查视频格式'
    } else if (progress < 90) {
      errorMessage = '视频上传失败，请检查网络连接'
    } else if (progress < 100) {
      errorMessage = '文件创建失败，请重试'
    }

    if (error) {
      errorMessage = error as string
    }

    message.error(errorMessage)
  } finally {
    isUploading.value = false
    uploadProgress.value = 0
  }
}
</script>

<style lang="scss" scoped>
.capture-page {
  width: 100%;
  height: 100vh;
  background-image: url('~/assets/images/upgrade-bg.webp');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  user-select: none;

  .capture-content {
    width: 100%;
    height: 100%;
    padding: rem(172) rem(120) rem(120);

    .main-container {
      display: flex;
      gap: rem(8);
      height: 100%;

      // 左侧三块区域
      .left-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: rem(8);

        // 第一块：视频上传区域
        .upload-area {
          flex: 1;
          background: rgba(42, 40, 68, 0.8);
          border: 2px dashed rgba(255, 255, 255, 0.3);
          border-radius: rem(12);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          // 视频播放器容器
          .video-player-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;

            .uploaded-video {
              width: 100%;
              height: 100%;
              object-fit: contain;
              background: #000;
              border-radius: rem(10);
            }

            .video-actions {
              position: absolute;
              top: rem(16);
              right: rem(16);
              z-index: 10;

              button {
                background: rgba(0, 0, 0, 0.6);
                border: none;
                color: #fff;
                backdrop-filter: blur(rem(4));

                &:hover {
                  background: rgba(0, 0, 0, 0.8);
                  color: #ff4d4f;
                }
              }
            }
          }
        }

        // 第二块：视频控制栏
        .video-controls {
          background: rgba(42, 40, 68, 0.9);
          border-radius: rem(8);
          padding: rem(14) rem(40);
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #ffffff;

          .controls-left {
            width: rem(200);

            .time-display {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: rem(16);
              color: #FFFFFF;
              line-height: rem(24);
              text-align: left;
              font-style: normal;
            }
          }

          .controls-center {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: rem(16);

            .anticon {
              width: rem(40);
              height: rem(40);
            }

            .control-icon {
              font-size: rem(28);
              cursor: pointer;
              transition: all 0.2s ease;
              color: #ffffff;
              user-select: none;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              padding: rem(4);
              border-radius: rem(4);

              &:hover {
                color: #0091ff;
                background: rgba(255, 255, 255, 0.1);
              }

              &:active {
                background: rgba(0, 145, 255, 0.2);
              }

              &.play-icon {
                color: #0091ff;

                &:hover {
                  color: #00d4ff;
                  // transform: scale(1.15);
                  background: rgba(0, 145, 255, 0.1);
                }

                &:active {
                  // transform: scale(1.05);
                  background: rgba(0, 145, 255, 0.3);
                }
              }
            }
          }

          .controls-right {
            width: rem(200);
            text-align: right;

            .volume-btn {
              background: none;
              border: none;
              color: #ffffff;
              cursor: pointer;
              padding: rem(8);
              border-radius: rem(4);
              transition: all 0.2s ease;

              &:hover {
                background: rgba(255, 255, 255, 0.1);
                color: #0091ff;
              }

              svg {
                width: rem(28);
                height: rem(28);
              }
            }
          }
        }

        // 第三块：视频详情区域
        .video-details {
          display: flex;
          flex-direction: column;
          padding: 0 rem(24) rem(24);
          background: rgba(42, 40, 68, 0.8);
          border-radius: rem(12);

          .video-editing-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: rem(91);

            font-style: normal;
            position: relative;

            .video-edit-tips {
              font-family: SourceHanSerifSC, SourceHanSerifSC;
              font-weight: bold;
              font-size: rem(24);
              color: #FFFFFF;
              line-height: rem(38);
              text-align: left;
            }


            &::after {
              content: '';
              width: 100%;
              height: rem(1);
              width: 100%;
              background: linear-gradient(270deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
              position: absolute;
              bottom: 0;
              left: 0;
            }
          }

          .editing-actions {
            display: flex;
            gap: rem(14);

            .icon-reset {
              font-size: rem(14);
              margin-right: rem(8);
            }

            .action-btn {
              @include action-btn;

              &.undo {
                background: rgba(255, 193, 7, 0.1);
                border: 1px solid rgba(255, 193, 7, 0.3);
                color: #ffc107;

                &:hover {
                  background: rgba(255, 193, 7, 0.2);
                  border-color: rgba(255, 193, 7, 0.5);
                }
              }
            }

            button {
              width: rem(100);
              height: rem(48);
              font-family: SourceHanSerifSC, SourceHanSerifSC;
              font-weight: 400;
              font-size: rem(18);
              color: #FFFFFF;
              line-height: rem(26);
              text-align: right;
              font-style: normal;
            }
          }

          // 视频编辑进度区域
          .video-editing-process {
            margin-top: rem(50);
            position: relative;
            min-height: rem(140);

            .timeline-placeholder {
              width: 100%;
              height: rem(140);
              background: rgba(61, 60, 70, 0.3);
              border: rem(2) dashed rgba(255, 255, 255, 0.2);
              border-radius: rem(8);
              display: flex;
              align-items: center;
              justify-content: center;

              .placeholder-text {
                font-family: SourceHanSerifSC, SourceHanSerifSC;
                font-weight: 400;
                font-size: rem(16);
                color: rgba(255, 255, 255, 0.5);
                text-align: center;
              }
            }

            .gp-bar {
              width: 100%;
              height: rem(16);
              background-color: rgba(61, 60, 70, .8);
              font-size: rem(12);
              display: flex;
              align-items: center;
              color: #FFFFFF;
            }

            .gp-bar-content {
              width: 100%;
              height: rem(80);
              background-color: rgba(61, 60, 70, .2);
              position: relative;

              .gp-bar-content-drag {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                border: rem(1) solid #0080ff;
                background: transparent;
                opacity: 0.6;
                border-radius: rem(10);
                cursor: move;
              }
            }

            .frame-bar {
              width: rem(15);
              height: rem(100);
              position: absolute;
              left: rem(-5);
              top: rem(-5);

              img {
                width: 100%;
                height: 100%;
              }
            }

            .cut-bar {
              display: flex;
              position: absolute;
              left: rem(-5);
              top: rem(-35);

              img {
                width: rem(20);
                height: rem(20);

                &:first-child {
                  margin-right: rem(20);
                }
              }
            }
          }

          // 视频裁剪区域
          .video-cropper-section {
            margin-top: rem(20);
          }
        }
      }



      // 右侧导出格式选项 - 延伸到底部
      .export-section {
        width: rem(260);
        background: rgba(42, 40, 68, 0.8);
        border-radius: rem(12);
        padding: rem(24);
        display: flex;
        flex-direction: column;

        .export-title {
          color: #ffffff;
          font-size: rem(16);
          font-weight: 500;
          margin-bottom: rem(20);
          text-align: center;
        }

        .format-options {
          display: flex;
          flex-direction: column;
          overflow-y: auto;
          gap: rem(16);
          flex: 1;

          .format-option {
            width: 100%;
            height: rem(212);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: rem(8);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;

            &:hover {
              background: rgba(0, 145, 255, 0.15);
              border-color: #0091ff;
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 145, 255, 0.2);

              .format-name {
                color: #0091ff;
                transform: scale(1.05);
              }
            }

            // 选中状态样式
            &.selected {
              background: rgba(0, 145, 255, 0.25);
              border-color: #0091ff;
              box-shadow: 0 4px 15px rgba(0, 145, 255, 0.3);

              .format-name {
                color: #00d4ff;
                font-weight: 600;
              }

              // 选中状态下的悬停效果
              &:hover {
                background: rgba(0, 145, 255, 0.35);
                border-color: #00d4ff;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 145, 255, 0.4);

                .format-name {
                  color: #ffffff;
                  transform: scale(1.05);
                }
              }
            }

            .format-name {
              color: #ffffff;
              font-size: rem(14);
              font-weight: 500;
              transition: all 0.3s ease;
            }
          }
        }
      }
    }
  }
}

// 全页面进度条Loading样式
.page-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(rem(5));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-in-out;
}

.loading-content {
  background: rgba(42, 40, 68, 0.95);
  border-radius: rem(16);
  padding: rem(40);
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 rem(20) rem(40) rgba(0, 0, 0, 0.3);
  min-width: rem(400);
  max-width: rem(500);
}

.loading-spinner {
  margin-bottom: rem(24);
  display: flex;
  justify-content: center;
}

.spinner-ring {
  width: rem(60);
  height: rem(60);
  border: rem(4) solid rgba(255, 255, 255, 0.1);
  border-top: rem(4) solid #0091ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: rem(18);
  color: #ffffff;
  margin-bottom: rem(24);
  font-weight: 500;
}

.progress-container {
  margin-bottom: rem(16);
}

.progress-bar {
  width: 100%;
  height: rem(8);
  background: rgba(255, 255, 255, 0.1);
  border-radius: rem(4);
  overflow: hidden;
  margin-bottom: rem(12);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0091ff 0%, #00d4ff 100%);
  border-radius: rem(4);
  transition: width 0.3s ease;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
  }
}

.progress-percentage {
  font-size: rem(16);
  color: #00d4ff;
  font-weight: 600;
}

.loading-tip {
  font-size: rem(14);
  color: rgba(255, 255, 255, 0.7);
  margin-top: rem(16);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}
</style>
