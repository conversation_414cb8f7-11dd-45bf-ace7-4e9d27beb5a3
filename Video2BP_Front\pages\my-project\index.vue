<template>
    <div class="my-project">
        <div class="project-content">
            <div class="ec-head">
                <div class="ch-left">
                    <div class="hl-title">我的项目</div>
                    <div class="hl-zm">My Project</div>
                </div>
            </div>
            <div class="ec-body">
                <!-- 加载状态 -->
                <div v-if="loading" class="loading-container">
                    <Loading overlay size="large" text="加载中..." />
                </div>

                <!-- 错误状态 -->
                <ErrorState v-else-if="error" :message="error" @retry="retryFetch" />

                <!-- 数据列表 -->
                <div v-else class="cb-items">
                    <div class="item" v-for="item in resourceList" :key="item.id">
                        <div class="item-image" @click="handleItemClick(item)">
                            <img :src="item.image" :alt="item.name" />
                        </div>
                        <div class="item-name">
                            <span>{{ item.name }}</span>
                            <div class="download-icon" @click="handleDownload(item)">
                                <DownloadOutlined />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页组件 -->
                <PaginationWrapper v-if="!loading && !error" :total="total" :current="currentPage" :page-size="pageSize"
                    @change="handlePageChange" />
            </div>
        </div>

        <!-- 3D数字人预览Modal -->
        <DigitalHumanModal v-model:visible="modalVisible" :project-data="selectedProject" @repair="handleModalExport"
            @download="handleModalDownload" />
    </div>
</template>

<script setup lang="ts">
import { DownloadOutlined } from '@ant-design/icons-vue';
import Loading from '~/components/common/Loading.vue'
import ErrorState from '~/components/common/ErrorState.vue'
import PaginationWrapper from '~/components/common/PaginationWrapper.vue'
import DigitalHumanModal from './components/DigitalHumanModal.vue'
import type { ProjectItem } from '~/types/my-project'
import { useApi } from '~/composables/useApi'
defineOptions({
    name: 'MyProjectView',
})

definePageMeta({
    title: '我的项目',
    description: '',
    layout: 'default',
    layoutconfig: {
        // headerVisible: false,
        // footerVisible: false,
    },
})

// 使用导入的 ProjectItem 类型
type ResourceItem = ProjectItem

// 获取API服务实例
const { myProject } = useApi()

// API 分页数据管理
const resourceList = ref<ResourceItem[]>([]) // 当前页面的数据列表
const currentPage = ref(1) // 当前页码
const pageSize = ref(8) // 每页显示8个项目，正好填满4x2网格
const total = ref(0) // 总记录数，从 API 返回
const loading = ref(true) // 加载状态
const error = ref<string | null>(null) // 错误信息

// Modal相关状态
const modalVisible = ref(false) // Modal显示状态
const selectedProject = ref<ResourceItem | null>(null) // 选中的项目数据

// API 调用函数
const fetchResourceList = async (page: number = 1, size: number = 8) => {
    try {
        loading.value = true
        error.value = null

        const result = await myProject?.getProjectList({
            pageNum: page,
            pageSize: size,
        })

        if (!result) {
            throw new Error('请求失败')
        }

        const { data, total: totalCount, pageNum, pageSize: responsePageSize } = result.data
        resourceList.value = data
        total.value = totalCount
        currentPage.value = pageNum
        pageSize.value = responsePageSize

        console.log('获取项目列表成功:', result)

    } catch (err) {
        console.error('获取项目列表失败:', err)
        error.value = err as string
    } finally {
        loading.value = false
    }
}

// 分页变化处理函数
const handlePageChange = async (page: number) => {
    await fetchResourceList(page, pageSize.value)
}

// 重试函数
const retryFetch = async () => {
    await fetchResourceList(currentPage.value, pageSize.value)
}

// 点击项目图片处理函数
const handleItemClick = (item: ResourceItem) => {
    selectedProject.value = item
    modalVisible.value = true
}

// Modal 2d检测修骨
const handleModalExport = (data: ResourceItem) => {
    console.log('导出文档分析:', data.name)

    navigateTo({
        path: '/projects',
        query: { projectId: data.id }
    })
}

// Modal下载处理函数
const handleModalDownload = (data: ResourceItem) => {
    console.log('下载文件:', data.name)
}

const handleDownload = (item: ResourceItem) => {
    console.log('下载资源:', item.name)
}

// 页面初始化
onMounted(() => {
    fetchResourceList(1, pageSize.value)
})

</script>

<style lang="scss" scoped>
.my-project {
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    user-select: none;

    .project-content {
        display: flex;
        flex-direction: column;
        min-height: calc(100vh - rem(403));
        padding: rem(171) rem(120) rem(50);
        background-image: url('~/assets/images/upgrade-bg.webp');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;

        .ec-head {
            display: flex;
            justify-content: space-between;
            margin-bottom: rem(46);

            .ch-left {
                display: flex;
                flex-direction: column;

                .hl-title {
                    font-family: SourceHanSerifSC, SourceHanSerifSC;
                    font-weight: bold;
                    font-size: rem(48);
                    color: #FFFFFF;
                    line-height: rem(69);
                    text-align: left;
                    font-style: normal;
                    margin-bottom: rem(12);
                }

                .hl-zm {
                    font-family: SourceHanSerifSC, SourceHanSerifSC;
                    font-weight: bold;
                    font-size: rem(14);
                    line-height: rem(20);
                    letter-spacing: rem(11);
                    text-align: left;
                    font-style: normal;
                    text-transform: uppercase;
                    background: linear-gradient(to bottom, #0091FF 0%, #B620E0 100%);
                    -webkit-background-clip: text;
                    background-clip: text;
                    color: transparent;
                }
            }

        }

        .ec-body {
            flex: 1;
            display: flex;
            flex-direction: column;

            .loading-container {
                @include loading-container;
            }

            .cb-items {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: rem(49) rem(24);
                background: transparent;

                .item {
                    position: relative;
                    border-radius: rem(8);
                    overflow: hidden;
                    cursor: pointer;
                    transition: all 0.3s ease;


                    &:hover {
                        transform: translateY(rem(-5));
                        box-shadow: 0 rem(10) rem(30) rgba(0, 0, 0, 0.3);

                        .item-overlay {
                            opacity: 1;
                        }
                    }

                    .item-image {
                        position: relative;
                        width: 100%;
                        height: rem(226);
                        overflow: hidden;
                        margin-bottom: rem(23);

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            transition: transform 0.3s ease;
                        }
                    }

                    .item-name {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        span {
                            font-family: SourceHanSerifSC, SourceHanSerifSC;
                            font-weight: bold;
                            font-size: rem(24);
                            color: #FFFFFF;
                            font-style: normal;
                        }

                        .download-icon {
                            font-size: rem(24);
                            color: #FFFFFF;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
}

// 加载动画
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>