# 安全认证系统文档

## 概述

本项目实现了一套完整的安全认证系统，包括登录失败锁定、统一业务码管理、安全日志记录等功能，有效防止暴力破解攻击并提高系统安全性。

## 主要特性

### 1. 统一业务码管理
- 所有业务响应使用统一的业务码和消息
- 认证成功：`{"code": 0, "message": "success"}`
- 认证失败：`{"code": 1000, "message": "用户名或密码错误"}`
- 账号锁定：`{"code": 1001, "message": "账号已锁定，请10分钟后重试"}`
- 系统错误：`{"code": 5000, "message": "系统繁忙，请稍后重试"}`

### 2. 安全认证机制
- **统一错误提示**：不区分"用户不存在"或"密码错误"，统一返回"用户名或密码错误"
- **登录失败锁定**：连续5次登录失败后锁定账号10分钟
- **强制延迟**：登录失败后强制延迟2秒，锁定状态下延迟5秒
- **详细日志记录**：记录真实失败原因到安全日志，响应给用户模糊提示

### 3. 系统级错误处理
- 隐藏技术细节和堆栈信息
- 统一返回"系统繁忙，请稍后重试"
- 详细错误信息记录到日志用于排查

## 文件结构

```
core/
├── constants/
│   ├── __init__.py
│   └── business_codes.py          # 业务码和消息定义
├── security/
│   ├── __init__.py
│   ├── auth_security.py           # 安全认证服务
│   └── security_logger.py         # 安全日志配置
└── ...

apps/app_user_auth/
├── api.py                         # 认证API接口
├── service.py                     # 认证业务服务
└── ...

logs/security/                     # 安全日志目录
└── security.log                   # 安全事件日志
```

## 核心组件

### 1. BusinessCode 枚举类
定义所有业务响应码：
```python
class BusinessCode(IntEnum):
    SUCCESS = 0                    # 操作成功
    AUTH_LOGIN_FAILED = 1000      # 用户名或密码错误
    AUTH_ACCOUNT_LOCKED = 1001    # 账号已锁定
    SYSTEM_ERROR = 5000           # 系统繁忙
    # ... 更多业务码
```

### 2. AuthSecurityService 安全认证服务
提供安全的用户认证功能：
```python
async def authenticate_user(
    self, 
    db: AsyncSession, 
    email: str, 
    password: str,
    ip_address: str = None
) -> Tuple[Optional[User], BusinessCode]
```

### 3. 安全配置常量
```python
class AuthSecurityConfig:
    MAX_LOGIN_ATTEMPTS = 5         # 最大登录尝试次数
    LOCKOUT_DURATION = 600         # 锁定时长（秒）
    LOGIN_DELAY_AFTER_FAILED = 2   # 登录失败后延迟
    LOGIN_DELAY_WHEN_LOCKED = 5    # 锁定状态下延迟
```

## API 接口

### 登录接口
```http
POST /api/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**成功响应 (200)**:
```json
{
    "code": 0,
    "message": "success",
    "data": null
}
```

**认证失败 (401)**:
```json
{
    "code": 1000,
    "message": "用户名或密码错误",
    "data": null
}
```

**账号锁定 (401)**:
```json
{
    "code": 1001,
    "message": "账号已锁定，请10分钟后重试",
    "data": null
}
```

**系统错误 (500)**:
```json
{
    "code": 5000,
    "message": "系统繁忙，请稍后重试",
    "data": null
}
```

## 安全日志

### 日志格式
```
2024-01-15T10:30:45.123456 | WARNING | security | LOGIN_FAILED | email: u***@example.com | success: false | ip: ************* | reason: wrong_password
```

### 日志事件类型
- `LOGIN_SUCCESS`: 登录成功
- `LOGIN_FAILED`: 登录失败
- `ACCOUNT_LOCKED`: 账号锁定
- `ACCOUNT_UNLOCKED`: 账号解锁
- `SYSTEM_ERROR`: 系统错误

### 日志配置
- 日志文件：`logs/security/security.log`
- 轮转策略：按日轮转，保留30天
- 敏感信息：自动屏蔽邮箱等敏感数据

## 数据库变更

需要在 `users` 表中添加以下字段：
```sql
ALTER TABLE `users` 
ADD COLUMN `failed_login_attempts` INT NOT NULL DEFAULT 0 COMMENT '登录失败次数',
ADD COLUMN `last_failed_login` DATETIME NULL COMMENT '最后一次登录失败时间',
ADD COLUMN `account_locked_until` DATETIME NULL COMMENT '账户锁定到期时间';
```

## 使用示例

### 1. 在API中使用安全认证
```python
from core.security import AuthSecurityService
from core.constants import BusinessCode, BusinessMessage

async def login(request: UserLoginRequest, db: AsyncSession):
    auth_service = AuthSecurityService()
    
    user, business_code = await auth_service.authenticate_user(
        db, request.email, request.password, client_ip
    )
    
    if business_code == BusinessCode.SUCCESS:
        # 登录成功，创建token
        token = create_access_token(user.id)
        return success_response(token)
    else:
        # 登录失败，返回相应错误
        return error_response(business_code)
```

### 2. 手动解锁账号
```python
auth_service = AuthSecurityService()
await auth_service.unlock_account("<EMAIL>")
```

### 3. 检查锁定状态
```python
remaining_time = await auth_service.get_remaining_lockout_time("<EMAIL>")
if remaining_time:
    print(f"账号锁定中，剩余时间: {remaining_time} 秒")
```

## 测试

运行测试脚本验证安全认证功能：
```bash
python test_auth_security.py
```

## 安全建议

1. **定期监控安全日志**：关注异常登录尝试和账号锁定事件
2. **调整安全参数**：根据实际需求调整锁定时长和尝试次数
3. **IP白名单**：考虑为管理员IP设置白名单
4. **告警机制**：集成告警系统，及时通知安全事件
5. **日志备份**：定期备份安全日志到安全存储
