import {
	createApiService,
	createAuthService,
	createUserService,
	createVideoService,
	createCompanyNewsService,
	createMyProjectService,
	createResourceService,
	createBusinessService,
	type ApiService,
} from '~/services';

// 缓存API服务实例
let apiInstance: ApiService | null = null;
let authServiceInstance: ReturnType<typeof createAuthService> | null = null;
let userServiceInstance: ReturnType<typeof createUserService> | null = null;
let videoServiceInstance: ReturnType<typeof createVideoService> | null = null;
let companyNewsServiceInstance: ReturnType<
	typeof createCompanyNewsService
> | null = null;
let myProjectServiceInstance: ReturnType<typeof createMyProjectService> | null =
	null;
let resourceServiceInstance: ReturnType<typeof createResourceService> | null =
	null;
let businessServiceInstance: ReturnType<typeof createBusinessService> | null =
	null;

export function useApi() {
	// 在composable内部安全地使用useRuntimeConfig
	const config = useRuntimeConfig();

	// 延迟初始化：只在第一次调用时创建实例
	if (!apiInstance) {
		apiInstance = createApiService(config.public.apiBase);
		authServiceInstance = createAuthService(apiInstance);
		userServiceInstance = createUserService(apiInstance);
		videoServiceInstance = createVideoService(apiInstance);
		companyNewsServiceInstance = createCompanyNewsService(apiInstance);
		myProjectServiceInstance = createMyProjectService(apiInstance);
		resourceServiceInstance = createResourceService(apiInstance);
		businessServiceInstance = createBusinessService(apiInstance);
	}

	return {
		api: apiInstance,
		auth: authServiceInstance,
		user: userServiceInstance,
		capture: videoServiceInstance,
		companyNews: companyNewsServiceInstance,
		myProject: myProjectServiceInstance,
		resource: resourceServiceInstance,
		business: businessServiceInstance,
	};
}
