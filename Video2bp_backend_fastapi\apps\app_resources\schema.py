from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl
from schemas.base import BaseSchema
from schemas.response import StandardResponse


class Resource(BaseSchema):
    """资源模型"""
    id: int = Field(..., description="资源ID")
    title: str = Field(..., description="资源标题")
    description: str = Field(..., description="资源描述")
    type: str = Field(..., description="资源类型")
    url: HttpUrl = Field(..., description="资源链接")
    thumbnail: Optional[HttpUrl] = Field(None, description="缩略图URL")
    size: Optional[str] = Field(None, description="文件大小")
    format: Optional[str] = Field(None, description="文件格式")
    createdAt: str = Field(..., description="创建时间")


class ResourceListResponse(BaseSchema):
    """资源列表响应"""
    pass  # 直接返回资源列表数组


class ResourceListResponseModel(StandardResponse):
    """资源列表响应模型"""
    data: Optional[List[Resource]] = None
