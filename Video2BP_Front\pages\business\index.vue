<template>
  <div class="business-page">
    <div class="business-content">
      <div class="sc-box">
        <div class="cb-title">行业赋能，未来无界</div>
        <div class="cb-intro">
          提供最新的AI视频动捕技术，帮助多行业企业实现高效AIGC创作及创新发展。
        </div>
      </div>
      <div class="sc-swiper">
        <SwiperProduct />
      </div>
      <div class="sc-support">
        <SupportProduct :style="{ background: 'linear-gradient(to bottom, #1d1934 0%, #19162b 100%)' }" />
      </div>
      <div class="sc-bg">
        <SwiperBusiness />
        <div class="sc-api">
          <div class="sc-api-title">选择适合您的API套餐</div>
          <ApiPackages />
        </div>
        <div class="sc-contact">
          <ContactForm />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SwiperProduct from '../home/<USER>/SwiperProduct.vue'
import SupportProduct from '../home/<USER>/SupportProduct.vue'
import SwiperBusiness from './components/SwiperBusiness.vue'
import ApiPackages from './components/ApiPackages.vue'
import ContactForm from './components/ContactForm.vue'

defineOptions({
  name: 'BusinessView',
})
</script>

<style lang="scss" scoped>
.business-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

}

.business-content {
  flex: 1;

  .sc-bg {
    width: 100%;

    // background-image: url('~/assets/images/business-big-bg.webp');
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
    background: linear-gradient(to bottom, #19162b 0%, #14121f 100%);
  }

  .sc-box {
    width: 100%;
    height: 100vh;
    background-image: url('~/assets/images/business-bg.webp');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .cb-title {
      padding-top: rem(289);
      position: relative;
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: bold;
      font-size: rem(72);
      color: #ffebb3;
      line-height: rem(104);
      text-align: center;
      font-style: normal;
      margin-bottom: rem(95);

      &::after {
        content: '';
        width: rem(172);
        height: rem(10);
        background: #ffebb3;
        border-radius: rem(1);
        position: absolute;
        left: 50%;
        bottom: rem(-20);
        transform: translateX(-50%);
      }
    }

    .cb-intro {
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: 500;
      font-size: rem(40);
      color: #ffffff;
      line-height: rem(72);
      text-align: center;
      font-style: normal;
    }
  }

  .sc-api {
    position: relative;
    overflow: hidden;

    .sc-api-title {
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: bold;
      font-size: rem(48);
      color: #ffffff;
      text-align: center;
      margin-bottom: rem(28);
    }
  }
}
</style>
