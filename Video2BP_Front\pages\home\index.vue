<template>
  <div class="home-page">
    <div class="home-content">
      <VideoBanner />
    </div>
    <div class="home-case">
      <div class="ec-title">
        <div class="ct-text">用户案例及使用评价</div>
        <div class="ct-eng">User cases and usage reviews</div>
      </div>
      <div class="ec-content">
        <Evaluate />
      </div>
    </div>
    <div class="home-swiper">
      <SwiperProduct />
    </div>
    <div class="home-surport">
      <SupportProduct />
    </div>
  </div>
</template>

<script setup lang="ts">
import SwiperProduct from './components/SwiperProduct.vue'
import SupportProduct from './components/SupportProduct.vue'
import VideoBanner from './components/VideoBanner.vue'
import Evaluate from './components/Evaluate.vue'

defineOptions({
  name: 'HomeView',
})

definePageMeta({
  title: '首页',
  description: '',

  // // === 布局和样式 ===
  // layout: 'blog',           // 使用的布局模板
  // colorMode: 'dark',        // 颜色模式

  // // === 路由相关 ===
  // alias: ['/article', '/post'],  // 路由别名

  // // === 权限和中间件 ===
  // middleware: ['auth'],     // 页面中间件
  requiresAuth: true,       // 是否需要认证
  // roles: ['admin', 'editor'], // 允许的角色

  // // === 页面行为 ===
  // keepalive: true,          // 保持页面活跃状态
  // pageTransition: {         // 页面过渡动画
  //   name: 'slide',
  //   mode: 'out-in'
  // },

  // // === SEO 相关 ===
  // robots: 'index,follow',   // 搜索引擎爬虫指令

  // // === 自定义数据 ===
  // category: 'technology',   // 页面分类
  // tags: ['vue', 'nuxt'],   // 页面标签
  // difficulty: 'intermediate', // 难度级别
  // estimatedReadTime: 5,     // 预估阅读时间
})

// SEO 配置
useSeoMeta({
  title: '首页',
  description: '首页描述',
  keywords: '首页关键词',
})
</script>

<style lang="scss" scoped>
.home-page {
  width: 100%;
  // min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.home-content {
  width: 100%;
  flex: 1;
  min-height: 100vh;
}

//案例部分
.home-case {
  padding-top: rem(89);
  padding-bottom: rem(120);
  background: linear-gradient(to bottom, #100f14 0%, #1d1935 100%);

  .ec-title {
    padding-left: rem(120);
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: bold;
    text-align: left;
    font-style: normal;

    .ct-text {
      font-size: rem(72);
      color: #ffffff;
      line-height: rem(104);
      margin-bottom: rem(5);
    }

    .ct-eng {
      font-size: rem(24);
      color: #ffffff;
      line-height: rem(35);
      letter-spacing: rem(20);
      text-transform: uppercase;
      background: linear-gradient(to bottom, #0091ff 0%, #b620e0 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      margin-bottom: rem(36);
    }
  }
}
</style>
