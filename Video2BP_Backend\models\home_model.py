from sqlalchemy import String, Integer, func, Column, Boolean, Text, DateTime, BigInteger
from sqlalchemy.dialects.mysql import LONGTEXT

from app import mysql_db


class News(mysql_db.Model):
    __tablename__ = 'news'

    id = Column(BigInteger, primary_key=True)
    name = Column(String(255), default="")
    type = Column(Integer, nullable=False)
    is_display = Column(Boolean, default=True)
    cover = Column(String(500), nullable=False)
    title = Column(String(255), nullable=False)
    author = Column(String(100), nullable=False, default="灵宇 AI 动捕")
    profile_picture = Column(String(500), nullable=False)
    excerpt = Column(Text, nullable=False)
    tag = Column(String(100))
    content = Column(LONGTEXT, nullable=False)
    is_pinned = Column(Boolean, default=False)
    created_at = Column(DateTime, nullable=False, default=func.now())
