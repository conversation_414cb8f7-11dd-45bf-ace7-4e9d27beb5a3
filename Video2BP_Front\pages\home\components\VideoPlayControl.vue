<template>
  <div :class="['play-box', { playing: isPlaying }]" @click.stop="onToggle">
    <img src="~/assets/images/play-button.webp" class="play-button" alt="" />
    <img src="~/assets/images/pause-button.webp" class="play-button" alt="" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  videoRef: HTMLVideoElement | null
  initialPlaying?: boolean
}>()

const emit = defineEmits(['toggle'])

const isPlaying = ref(props.initialPlaying || false)

const onToggle = () => {
  emit('toggle')
}

// 监听视频状态变化
watch(() => props.videoRef, (newRef) => {
  if (newRef) {
    // 同步初始状态
    isPlaying.value = !newRef.paused
  }
}, { immediate: true })

// 监听 initialPlaying 变化
watch(() => props.initialPlaying, (newValue) => {
  isPlaying.value = newValue || false
})

defineExpose({
  isPlaying
})
</script>

<style lang="scss" scoped>
.play-box {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  position: absolute;
  left: 0;
  top: 0;
  z-index: 100;
  user-select: none;
  cursor: pointer;

  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: rem(100);
    height: rem(100);
    cursor: pointer;
    z-index: 999;
    display: none;
  }

  &:hover {
    .play-button {
      &:first-child {
        display: flex;
      }

      &:last-child {
        display: none;
      }
    }
  }

  &.playing {
    .play-button {
      display: none;
    }

    &:hover {
      .play-button {
        &:first-child {
          display: none;
        }

        &:last-child {
          display: flex;
        }
      }
    }
  }
}
</style>
