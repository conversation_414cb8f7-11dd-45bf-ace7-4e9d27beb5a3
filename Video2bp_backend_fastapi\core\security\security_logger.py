"""
安全日志配置
专门用于记录安全相关事件，如登录失败、账户锁定等
"""

import logging
import logging.handlers
import os
from datetime import datetime
from pathlib import Path

from core.constants import AuthSecurityConfig


class SecurityLogFormatter(logging.Formatter):
    """安全日志格式化器"""
    
    def format(self, record):
        """格式化日志记录"""
        # 添加时间戳
        record.timestamp = datetime.now().isoformat()
        
        # 基本格式
        log_format = (
            "%(timestamp)s | %(levelname)s | %(name)s | "
            "%(message)s"
        )
        
        formatter = logging.Formatter(log_format)
        return formatter.format(record)


def setup_security_logger():
    """设置安全日志记录器"""
    
    # 创建日志目录
    log_dir = Path("logs/security")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建安全日志记录器
    security_logger = logging.getLogger("security")
    security_logger.setLevel(getattr(logging, AuthSecurityConfig.SECURITY_LOG_LEVEL))
    
    # 避免重复添加处理器
    if security_logger.handlers:
        return security_logger
    
    # 文件处理器 - 按日期轮转
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_dir / "security.log",
        when="midnight",
        interval=1,
        backupCount=30,  # 保留30天的日志
        encoding="utf-8"
    )
    file_handler.setLevel(logging.WARNING)
    file_handler.setFormatter(SecurityLogFormatter())
    
    # 控制台处理器（仅在开发环境）
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(SecurityLogFormatter())
    
    # 添加处理器
    security_logger.addHandler(file_handler)
    
    # 开发环境添加控制台输出
    if os.getenv("DEBUG", "false").lower() == "true":
        security_logger.addHandler(console_handler)
    
    # 防止日志向上传播到根记录器
    security_logger.propagate = False
    
    return security_logger


# 初始化安全日志记录器
security_logger = setup_security_logger()


def log_security_event(event_type: str, details: dict, level: str = "WARNING"):
    """
    记录安全事件
    
    Args:
        event_type: 事件类型
        details: 事件详情
        level: 日志级别
    """
    log_level = getattr(logging, level.upper(), logging.WARNING)
    
    # 格式化详情信息
    detail_str = " | ".join([f"{k}: {v}" for k, v in details.items()])
    message = f"{event_type} | {detail_str}"
    
    security_logger.log(log_level, message)


def log_login_attempt(email: str, success: bool, ip_address: str = None, reason: str = None):
    """记录登录尝试"""
    details = {
        "email": email,
        "success": success,
        "ip": ip_address or "unknown"
    }
    
    if reason:
        details["reason"] = reason
    
    event_type = "LOGIN_SUCCESS" if success else "LOGIN_FAILED"
    level = "INFO" if success else "WARNING"
    
    log_security_event(event_type, details, level)


def log_account_locked(email: str, duration: int, ip_address: str = None):
    """记录账户锁定"""
    details = {
        "email": email,
        "duration_seconds": duration,
        "ip": ip_address or "unknown"
    }
    
    log_security_event("ACCOUNT_LOCKED", details, "WARNING")


def log_account_unlocked(email: str, method: str = "auto"):
    """记录账户解锁"""
    details = {
        "email": email,
        "method": method  # auto, manual
    }
    
    log_security_event("ACCOUNT_UNLOCKED", details, "INFO")


def log_system_error(operation: str, error: str, email: str = None):
    """记录系统错误"""
    details = {
        "operation": operation,
        "error": error
    }
    
    if email:
        details["email"] = email
    
    log_security_event("SYSTEM_ERROR", details, "ERROR")
