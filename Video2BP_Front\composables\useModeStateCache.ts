import { ref, type Ref } from 'vue';
import { deepClone } from '~/utils';
import type { SkeletonFrame } from '~/pages/repair-bone/hooks/useSkeletonAnimation';

/**
 * 模式状态接口定义
 */
export interface ModeState {
	// 基础播放状态
	currentFrame: number;
	isPlaying: boolean;
	playbackSpeed: number;

	// 数据状态
	skeletonData: SkeletonFrame[];

	// KP模式特有状态
	selectedPoints?: Set<string> | string[];
	copiedData?: any;
	editHistory?: any[];

	// IK模式特有状态
	footIKStates?: Record<string, any>;

	// 视频状态
	videoCurrentTime: number;

	// 组件状态
	componentSpecificState?: Record<string, any>;

	// 元数据
	lastSavedAt: number;
	version: string;
}

/**
 * 模式状态缓存接口
 */
export interface ModeStateCache {
	kp: ModeState | null;
	ik: ModeState | null;
}

/**
 * 状态缓存版本
 */
const STATE_VERSION = '1.0.0';

/**
 * 状态过期时间（毫秒）- 30分钟
 */
const STATE_EXPIRY_TIME = 30 * 60 * 1000;

/**
 * 模式状态缓存管理 Composable
 */
export function useModeStateCache() {
	// 状态缓存存储
	const stateCache = ref<ModeStateCache>({
		kp: null,
		ik: null,
	});

	/**
	 * 验证状态是否有效
	 */
	const isStateValid = (state: ModeState | null): boolean => {
		if (!state) return false;

		// 检查版本兼容性
		if (state.version !== STATE_VERSION) {
			console.warn('状态版本不兼容，将忽略缓存状态');
			return false;
		}

		// 检查是否过期
		const now = Date.now();
		if (now - state.lastSavedAt > STATE_EXPIRY_TIME) {
			console.warn('状态已过期，将忽略缓存状态');
			return false;
		}

		// 检查必要字段
		if (
			typeof state.currentFrame !== 'number' ||
			typeof state.isPlaying !== 'boolean' ||
			!Array.isArray(state.skeletonData)
		) {
			console.warn('状态数据不完整，将忽略缓存状态');
			return false;
		}

		return true;
	};

	/**
	 * 保存当前模式状态
	 */
	const saveCurrentModeState = (
		mode: 'kp' | 'ik',
		currentState: Partial<ModeState>
	): void => {
		try {
			// 构建完整状态对象
			const fullState: ModeState = {
				currentFrame: currentState.currentFrame ?? 0,
				isPlaying: currentState.isPlaying ?? false,
				playbackSpeed: currentState.playbackSpeed ?? 1,
				skeletonData: currentState.skeletonData
					? deepClone(currentState.skeletonData)
					: [],
				videoCurrentTime: currentState.videoCurrentTime ?? 0,
				lastSavedAt: Date.now(),
				version: STATE_VERSION,
				...currentState,
			};

			// 处理Set类型的selectedPoints
			if (currentState.selectedPoints instanceof Set) {
				fullState.selectedPoints = Array.from(
					currentState.selectedPoints
				);
			}

			// 深拷贝复杂对象
			if (currentState.copiedData) {
				fullState.copiedData = deepClone(currentState.copiedData);
			}

			if (currentState.editHistory) {
				fullState.editHistory = deepClone(currentState.editHistory);
			}

			if (currentState.footIKStates) {
				fullState.footIKStates = deepClone(currentState.footIKStates);
			}

			if (currentState.componentSpecificState) {
				fullState.componentSpecificState = deepClone(
					currentState.componentSpecificState
				);
			}

			// 保存到缓存
			stateCache.value[mode] = fullState;

			console.log(`已保存 ${mode.toUpperCase()} 模式状态:`, {
				currentFrame: fullState.currentFrame,
				isPlaying: fullState.isPlaying,
				dataLength: fullState.skeletonData.length,
				timestamp: new Date(fullState.lastSavedAt).toLocaleTimeString(),
			});
		} catch (error) {
			console.error(`保存 ${mode} 模式状态失败:`, error);
		}
	};

	/**
	 * 恢复指定模式状态
	 */
	const restoreModeState = (mode: 'kp' | 'ik'): ModeState | null => {
		try {
			const cachedState = stateCache.value[mode];

			if (!isStateValid(cachedState)) {
				// 清理无效状态
				stateCache.value[mode] = null;
				return null;
			}

			console.log(`正在恢复 ${mode.toUpperCase()} 模式状态:`, {
				currentFrame: cachedState!.currentFrame,
				isPlaying: cachedState!.isPlaying,
				dataLength: cachedState!.skeletonData.length,
				savedAt: new Date(
					cachedState!.lastSavedAt
				).toLocaleTimeString(),
			});

			// 返回深拷贝的状态，避免引用问题
			const restoredState = deepClone(cachedState!);

			// 恢复Set类型的selectedPoints
			if (Array.isArray(restoredState.selectedPoints)) {
				restoredState.selectedPoints = new Set(
					restoredState.selectedPoints
				);
			}

			return restoredState;
		} catch (error) {
			console.error(`恢复 ${mode} 模式状态失败:`, error);
			return null;
		}
	};

	/**
	 * 清理指定模式状态
	 */
	const clearModeState = (mode: 'kp' | 'ik'): void => {
		stateCache.value[mode] = null;
		console.log(`已清理 ${mode.toUpperCase()} 模式状态`);
	};

	/**
	 * 清理所有状态
	 */
	const clearAllStates = (): void => {
		stateCache.value = { kp: null, ik: null };
		console.log('已清理所有模式状态');
	};

	/**
	 * 检查是否有缓存状态
	 */
	const hasCachedState = (mode: 'kp' | 'ik'): boolean => {
		return isStateValid(stateCache.value[mode]);
	};

	/**
	 * 获取状态缓存信息（用于调试）
	 */
	const getCacheInfo = () => {
		return {
			kp: stateCache.value.kp
				? {
						currentFrame: stateCache.value.kp.currentFrame,
						lastSavedAt: new Date(
							stateCache.value.kp.lastSavedAt
						).toLocaleString(),
						version: stateCache.value.kp.version,
					}
				: null,
			ik: stateCache.value.ik
				? {
						currentFrame: stateCache.value.ik.currentFrame,
						lastSavedAt: new Date(
							stateCache.value.ik.lastSavedAt
						).toLocaleString(),
						version: stateCache.value.ik.version,
					}
				: null,
		};
	};

	return {
		saveCurrentModeState,
		restoreModeState,
		clearModeState,
		clearAllStates,
		hasCachedState,
		getCacheInfo,
	};
}
