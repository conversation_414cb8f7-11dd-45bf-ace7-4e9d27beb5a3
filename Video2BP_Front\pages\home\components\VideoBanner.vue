<template>
  <div class="video-banner">
    <div class="video-bottom">
      <i class="iconfont icon-jiantou1"></i>
    </div>
    <video ref="videoRef" class="background-video" playsinline muted loop autoplay preload="metadata"
      @loadeddata="onVideoLoaded">
      <source src="http://***************/assets/video/demo.mp4" type="video/mp4" />
    </video>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineOptions({
  name: 'VideoBanner',
})

const videoRef = ref<HTMLVideoElement | null>(null)

// 处理视频加载完成事件
const onVideoLoaded = () => {
  if (videoRef.value) {
    const playPromise = videoRef.value.play()
    if (playPromise !== undefined) {
      playPromise.then(() => {
        console.log('视频自动开始播放')
      }).catch(error => {
        console.error('自动播放视频时出错:', error)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.video-banner {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;

  .background-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
  }

  .video-bottom {
    position: absolute;
    bottom: rem(50);
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;

    i {
      font-size: rem(80);
      color: #ffffff;
    }
  }
}
</style>
