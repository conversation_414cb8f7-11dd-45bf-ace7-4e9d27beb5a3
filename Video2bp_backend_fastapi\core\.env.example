# 数据库配置示例文件
# 复制此文件为 .env 并修改相应配置

# 项目基本配置
PROJECT_NAME=视频动捕API系统
VERSION=1.0.0
DEBUG=true
API_PREFIX=/api

# 安全配置
SECRET_KEY=your-super-secret-key-here-change-in-production

# JWT配置
JWT_TOKEN_PREFIX=Bearer
ACCESS_TOKEN_EXPIRE_MINUTES=4320

# Redis配置（可选）
REDIS_URL=redis://localhost:6379/0

# 数据库配置
# MySQL配置示例
DB_CONNECTION=mysql+pymysql://username:password@localhost:3306/video2bp_db?charset=utf8mb4
ASYNC_DB_CONNECTION=mysql+aiomysql://username:password@localhost:3306/video2bp_db?charset=utf8mb4

# SQLite配置示例（开发环境）
# DB_CONNECTION=sqlite:///./video2bp.db
# ASYNC_DB_CONNECTION=sqlite+aiosqlite:///./video2bp.db

# PostgreSQL配置示例
# DB_CONNECTION=postgresql://username:password@localhost:5432/video2bp_db
# ASYNC_DB_CONNECTION=postgresql+asyncpg://username:password@localhost:5432/video2bp_db

# 字符集
CHARSET=utf8mb4

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=500MB

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
