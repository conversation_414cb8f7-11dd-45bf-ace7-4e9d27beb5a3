from sqlalchemy import DateT<PERSON>, String, Integer, func, Column, SmallInteger, Boolean, BigInteger
from sqlalchemy.dialects.mysql import DECIMAL

from app import mysql_db


class User(mysql_db.Model):
    __tablename__ = "users"
    id = Column(BigInteger, primary_key=True)
    username = Column(String(80))
    email = Column(String(120), unique=True, nullable=False)
    phone = Column(String(120))
    password = Column(String(128), nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now())
    vip_level = Column(String(80), nullable=False, default="1")
    status = Column(SmallInteger, nullable=False, default=1)
    current_plan_id = Column(Integer)
    current_subscription_id = Column(Integer)


class UserVideos(mysql_db.Model):
    __tablename__ = "user_videos"
    id = Column(BigInteger, primary_key=True)
    user_id = Column(Integer, nullable=False)
    filename = Column(String(128))
    bp_filename = Column(String(128))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now())
    status = Column(SmallInteger, nullable=False, default=1)


class UserOrders(mysql_db.Model):
    __tablename__ = "user_orders"
    id = Column(BigInteger, primary_key=True)
    order_number = Column(String(128), unique=True, nullable=False)
    user_id = Column(Integer, nullable=False)
    plan_id = Column(Integer, nullable=False)
    created_at = Column(DateTime, default=func.now())
    payed_at = Column(DateTime)
    updated_at = Column(DateTime, default=func.now())
    amount = Column(DECIMAL(10, 2), nullable=False)
    payment_method = Column(String(30), nullable=False)
    transaction_id = Column(String(128), nullable=False)
    status = Column(SmallInteger, nullable=False, default=1)


class UserSubscriptions(mysql_db.Model):
    __tablename__ = "user_subscriptions"
    id = Column(BigInteger, primary_key=True)
    order_id = Column(Integer, nullable=False)
    user_id = Column(Integer, nullable=False)
    plan_id = Column(Integer, nullable=False)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    is_active = Column(Boolean)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now())
    auto_renew = Column(Boolean)
