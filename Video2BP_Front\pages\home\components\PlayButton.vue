<template>
  <div :class="['common-play-button', className]">
    <svg t="1748331183201" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
      p-id="1022" width="60" height="60">
      <defs>
        <linearGradient id="playGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color: #0091ff; stop-opacity: 1" />
          <stop offset="100%" style="stop-color: #b620e0; stop-opacity: 1" />
        </linearGradient>
      </defs>
      <path
        d="M744.727273 551.563636L325.818182 795.927273c-30.254545 18.618182-69.818182-4.654545-69.818182-39.563637v-488.727272c0-34.909091 39.563636-58.181818 69.818182-39.563637l418.909091 244.363637c30.254545 16.290909 30.254545 62.836364 0 79.127272z"
        p-id="1023" fill="url(#playGradient)"></path>
    </svg>
  </div>

</template>
<script setup lang="ts">
defineProps({
  className: {
    type: String,
    default: ''
  }
})
</script>
<style lang="scss" scoped>
.common-play-button {
  width: rem(80);
  height: rem(80);
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 1;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 rem(4) rem(10) rgba(0, 0, 0, 0.1);
}

.icon {
  margin-left: rem(5);
}
</style>
