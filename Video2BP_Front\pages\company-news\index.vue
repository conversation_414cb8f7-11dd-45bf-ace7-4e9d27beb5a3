<template>
  <div class="company-page">
    <HeadMenu :style="{
      position: 'fixed',
      top: '0',
      background: 'linear-gradient(to right, #13111a 0%, #1e1b31 50%, #13111a 100%)',
    }">
      <div class="yc-tabs">
        <div v-for="tab in tabs" :key="tab.id" class="yc-tab-item" :class="{ active: tab.active }"
          @click="changeTab(tab.id)">
          {{ tab.name }}
        </div>
      </div>
    </HeadMenu>
    <div class="company-news-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <Loading overlay size="large" text="加载中..." />
      </div>

      <!-- 错误状态 -->
      <ErrorState v-else-if="error" :message="error" @retry="retryFetch" />

      <!-- 新闻列表 -->
      <div v-else class="news-content">
        <div class="company-news-list">
          <div v-for="item in newsItems" :key="item.id" class="news-card">
            <div class="news-image">
              <img :src="item.image" alt="news image" />
              <div class="tag">{{ item.tag }}</div>
            </div>
            <div class="news-title">{{ item.title }}</div>
            <div class="news-footer">
              <div class="author">
                <img v-if="item.authorAvatar" :src="item.authorAvatar" alt="author" class="avatar" />
                <span v-else class="official-icon"></span>
                <span class="author-name">{{ item.author }}</span>
              </div>
              <div class="date">{{ item.date }}</div>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <PaginationWrapper v-if="!loading && !error" :total="total" :current="currentPage" :page-size="pageSize"
          @change="handlePageChange" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { TabItem, NewsItem } from '~/types/business'
import HeadMenu from '~/components/layout/AppHeader.vue'
import Loading from '~/components/common/Loading.vue'
import ErrorState from '~/components/common/ErrorState.vue'
import PaginationWrapper from '~/components/common/PaginationWrapper.vue'
import { useApi } from '~/composables/useApi';

defineOptions({
  name: 'CompanyNewsView',
})

definePageMeta({
  layout: 'default',
  layoutconfig: {
    headerVisible: false,
  },
})

const { companyNews } = useApi();

const tabs = ref<TabItem[]>([
  { id: 1, name: '所有', active: true },
  { id: 2, name: '官方新闻', active: false },
  { id: 3, name: '实用教程', active: false },
  { id: 4, name: '体验分享', active: false },
  { id: 5, name: '更新公告', active: false },
])

// 分页数据管理
const newsItems = ref<NewsItem[]>([]) // 当前页面的新闻列表
const currentPage = ref(1) // 当前页码
const pageSize = ref(9) // 每页显示9条新闻（3列2行）
const total = ref(0) // 总记录数
const loading = ref(true) // 加载状态
const error = ref<string>() // 错误信息

// 获取当前激活的标签
const activeTab = computed(() => tabs.value.find(tab => tab.active))



// API 调用函数
const fetchNewsList = async (page: number = 1, size: number = 9, tabId: number = 1) => {
  try {
    loading.value = true
    error.value = ''

    // 
    const result = await companyNews?.getNewsList({
      pageNum: page,
      pageSize: size,
      category: tabId,
    })

    if (!result) {
      throw new Error('请求失败')
    }

    const { data, total: totalCount, pageNum, pageSize: responsePageSize } = result.data
    newsItems.value = data
    total.value = totalCount
    currentPage.value = pageNum
    pageSize.value = responsePageSize

    console.log('获取新闻列表成功:', result)

  } catch (err) {
    console.error('获取新闻列表失败:', err)
    error.value = err as string || '获取新闻列表失败'
  } finally {
    loading.value = false
  }
}

// 分页变化处理函数
const handlePageChange = async (page: number) => {
  const activeTabId = activeTab.value?.id || 1
  await fetchNewsList(page, pageSize.value, activeTabId)
}

// 重试函数
const retryFetch = async () => {
  const activeTabId = activeTab.value?.id || 1
  await fetchNewsList(currentPage.value, pageSize.value, activeTabId)
}

const changeTab = async (id: number) => {
  tabs.value.forEach((tab) => {
    tab.active = tab.id === id
  })
  // 切换标签时重置分页并重新获取数据
  await fetchNewsList(1, pageSize.value, id)
}

// 页面初始化
onMounted(() => {
  fetchNewsList(1, pageSize.value, 1)
})
</script>
<style scoped lang="scss">
.company-page {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-image: url('~/assets/images/upgrade-bg.webp');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;

  .company-news-content {
    min-height: calc(100vh - rem(403));
    padding: rem(220) rem(120) rem(120);
    display: flex;
    flex-direction: column;

    .loading-container {
      @include loading-container;
    }
  }

  .news-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .yc-tabs {
    display: flex;
    gap: rem(20);
    padding: rem(4) 0;

    .yc-tab-item {
      padding: rem(14) rem(38) rem(16);
      cursor: pointer;
      border-radius: rem(24);
      transition: all 0.3s;

      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: bold;
      font-size: rem(32);
      color: #ffffff;
      line-height: rem(32);
      text-align: left;
      font-style: normal;

      &.active {
        background: #ffffff;
        box-shadow: 0 rem(1) rem(6) rem(1) rgba(255, 255, 255, 0.8);
        border-radius: rem(48);
        color: #0b042a;
      }

      &:hover:not(.active) {
        color: #fff;
      }
    }
  }

  .company-news-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: rem(24);
    margin-bottom: rem(60);

    .news-card {
      background: #2d2943;
      border-radius: rem(16);
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .news-image {
        position: relative;
        // 使用aspect-ratio替代固定高度，保持16:9比例
        aspect-ratio: 16/9;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .tag {
          position: absolute;
          top: rem(16);
          right: rem(16);
          background: rgba(0, 0, 0, 0.45);
          padding: rem(6) rem(12) rem(9);
          border-radius: rem(3);

          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: 400;
          font-size: rem(18);
          color: #ffffff;
          line-height: rem(26);
          text-align: center;
          font-style: normal;
        }
      }

      .news-title {
        padding: rem(23) 0 rem(7) rem(24);

        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: bold;
        font-size: rem(24);
        color: #ffffff;
        line-height: rem(32);
        text-align: left;
        font-style: normal;
      }

      .news-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 rem(24) rem(25);

        .author {
          display: flex;
          align-items: center;

          .avatar {
            width: rem(24);
            height: rem(24);
            border-radius: 50%;
            margin-right: rem(3);
          }

          .official-icon {
            width: rem(24);
            height: rem(24);
            background: #3498db;
            border-radius: 50%;
            margin-right: rem(8);
          }

          .author-name {
            font-family: SourceHanSerifSC, SourceHanSerifSC;
            font-weight: 400;
            font-size: rem(18);
            color: rgba(255, 255, 255, 0.5);
            line-height: rem(26);
            text-align: left;
            font-style: normal;
          }
        }

        .date {
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: 400;
          font-size: rem(18);
          color: rgba(255, 255, 255, 0.5);
          line-height: rem(26);
          text-align: center;
          font-style: normal;
        }
      }
    }
  }
}
</style>
