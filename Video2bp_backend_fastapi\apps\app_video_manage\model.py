from sqlalchemy import Column, String, Integer, SmallInteger

from models.base import Base
from models.mixins import DateTimeModelMixin


class UserVideos(Base["UserVideos"], DateTimeModelMixin):
    """用户视频模型"""
    __tablename__ = "user_videos"

    user_id = Column(Integer, nullable=False, index=True)
    filename = Column(String(128), nullable=True)
    bp_filename = Column(String(128), nullable=True)
    status = Column(SmallInteger, nullable=False, default=1)
