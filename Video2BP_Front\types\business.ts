export interface TabItem {
	id: number;
	name: string;
	active: boolean;
}

export interface NewsItem {
	id: number;
	title: string;
	image: string;
	date: string;
	author: string;
	authorAvatar?: string;
	tag: string;
}

export interface ContactFormParams {
	phone: string;
	companyName: string;
	project: string;
}

export interface ContactFormResponse {
	code: number;
	message: string;
	data: {};
}

// 业务轮播图相关类型
export interface BusinessItem {
	id: number;
	title: string;
	image: string;
	details: string[];
}

export interface BusinessListParams {}

export interface BusinessListResponse {
	code: number;
	message: string;
	data: BusinessItem[];
}

// 产品轮播图相关类型
export interface ProductItem {
	id: number;
	title: string;
	description: string;
	videoUrl: string;
}

export interface ProductListParams {}

export interface ProductListResponse {
	code: number;
	message: string;
	data: ProductItem[];
}

// 用户案例相关类型
export interface UserCaseItem {
	id: number;
	name: string;
	avatar: string;
	time: string;
	content: string;
	preview: string;
}

export interface UserCaseListParams {}

export interface UserCaseListResponse {
	code: number;
	message: string;
	data: UserCaseItem[];
}
