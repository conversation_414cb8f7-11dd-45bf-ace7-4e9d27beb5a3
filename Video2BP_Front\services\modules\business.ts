import type {
	ContactFormParams,
	ContactFormResponse,
	BusinessListParams,
	BusinessItem,
	ProductListParams,
	ProductItem,
	UserCaseListParams,
	UserCaseItem,
} from '~/types/business';
import type { ApiService } from '../api';

const API_PREFIX = '/business';

export function createBusinessService(apiService: ApiService) {
	return {
		submitContactForm(
			data: ContactFormParams
		): Promise<ContactFormResponse> {
			return apiService.post(`${API_PREFIX}/contact`, data);
		},

		getBusinessList(
			data: BusinessListParams = {}
		): Promise<BusinessItem[]> {
			return apiService.post(`${API_PREFIX}/list`, data);
		},

		getProductList(data: ProductListParams = {}): Promise<ProductItem[]> {
			return apiService.post(`${API_PREFIX}/products`, data);
		},

		getUserCases(data: UserCaseListParams = {}): Promise<UserCaseItem[]> {
			return apiService.post(`${API_PREFIX}/user-cases`, data);
		},
	};
}
