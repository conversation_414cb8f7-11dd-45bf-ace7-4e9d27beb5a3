<template>
  <div class="carousel-container">
    <swiper :slidesPerView="3" :spaceBetween="24" :centeredSlides="true" :slideShadows="false" :loop="true"
      :pagination="false" :navigation="false" :modules="modules" class="mySwiper">
      <swiper-slide v-for="slide in slides" :key="slide.id" class="swiper-slide">
        <div class="slide-content">
          <div class="ec-img">
            <img :src="slide.image" :alt="slide.title" />
          </div>

          <div class="ec-title">
            {{ slide.title }}
          </div>
          <div class="ec-info">
            <div class="ec-info-item" v-for="(item, index) in slide.details" :key="index">
              {{ item }}
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination, Navigation, EffectCoverflow } from 'swiper/modules'
import type { BusinessItem } from '~/types/business'
import { useApi } from '~/composables/useApi'

// 导入Swiper样式
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'
import 'swiper/css/effect-coverflow'

// 定义模块
const modules = [Pagination, Navigation, EffectCoverflow]

const { business } = useApi()

// 响应式数据管理
const slides = ref<BusinessItem[]>([])

// API 调用函数
const fetchBusinessList = async () => {
  try {
    //
    const result = await business?.getBusinessList({})

    if (result) {
      slides.value = result
      console.log('获取业务列表成功:', result)
    }
  } catch (err) {
    console.error('获取业务列表失败:', err)
  }
}

// 页面初始化
onMounted(() => {
  fetchBusinessList()
})
</script>

<style scoped lang="scss">
.carousel-container {
  width: 100%;
  // padding: rem(73) 0 0;
  user-select: none;
  background-image: url('~/assets/images/business-sbg.webp');
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: 0 rem(390);
}

.mySwiper {
  width: 100%;
  height: 100%;
  padding: 0 rem(24) rem(100) 0;
}

.swiper-slide {
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;

  .ec-img {
    img {
      transition: all 0.3s ease;
    }
  }
}

/* 非活动幻灯片的样式 */
:deep(.swiper-slide-prev),
:deep(.swiper-slide-next) {
  opacity: 0.7;

  .ec-img {
    img {
      transform: scale(0.9);
    }
  }
}

/* 活动幻灯片的样式 */
:deep(.swiper-slide-active) {
  .ec-img {
    img {
      transform: scale(1.1);
    }
  }

  opacity: 1;
  z-index: 2;
}

.slide-content {
  width: 100%;
  height: 100%;
  border-radius: rem(8);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.slide-content .ec-img {
  width: 100%;
  height: rem(320);
  box-shadow: 0 rem(4) rem(10) rgba(0, 0, 0, 0.1);

  img {
    width: 100%;
    height: 100%;
  }
}

.ec-title {
  margin-top: rem(26);
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: bold;
  font-size: rem(60);
  color: #ffffff;
  line-height: rem(86);
  text-align: center;
  font-style: normal;
}

.ec-info {
  margin-top: rem(0);

  .ec-info-item {
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: bold;
    font-size: rem(60);
    color: #98dffb;
    line-height: rem(104);
    text-align: center;
    font-style: normal;

    &:nth-child(2) {
      margin-bottom: rem(55);
      position: relative;

      &::before {
        content: '';
        width: rem(68);
        height: rem(4);
        background: #ffffff;
        position: absolute;
        bottom: rem(-22);
        left: 50%;
        transform: translateX(-50%);
      }
    }

    &:nth-child(3),
    &:nth-child(4) {
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: 400;
      font-size: rem(40);
      color: #ffffff;
      line-height: rem(72);
      text-align: center;
      font-style: normal;
    }
  }
}
</style>
