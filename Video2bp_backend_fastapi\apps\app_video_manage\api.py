from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile, Body
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_404_NOT_FOUND,
    HTTP_413_REQUEST_ENTITY_TOO_LARGE,
    HTTP_415_UNSUPPORTED_MEDIA_TYPE,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from apps.app_video_manage.schema import (
    VideoUploadResponseModel,
    VideoListRequest,
    VideoListResponseModel,
    ChunkUploadResponseModel,
    MergeChunksResponseModel,
    UploadStatusResponseModel,
    VideoProcessRequest,
    VideoProcessResponseModel,
    VideoUploadStatusRequest,
    VideoUploadStatusResponseModel,
    SimpleVideoUploadRequest,
    SimpleVideoUploadResponseModel,
    CreateFileRequest,
    CreateFileResponseModel,
    VideoErrorResponseModel,
    MultiVideoUploadRequest,
    MultiVideoUploadResponseModel,
    UploadProgressRequest,
    UploadProgressResponseModel,
)
from apps.app_video_manage.service import video_manage_service
from core.e import ErrorCode, ErrorMessage
from db.database import get_async_db
from lib.cookie_auth import get_current_user_from_cookie
from apps.app_user.model import User
from schemas.response import StandardResponse


router = APIRouter()


@router.post(
    "/upload",
    name="视频上传",
    response_model=VideoUploadResponseModel,
)
async def video_upload(
    fileData: UploadFile = File(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """视频上传"""
    try:
        result = await video_manage_service.upload_video(db, current_user.id, fileData)
        return VideoUploadResponseModel(data=result)
    except HTTPException as e:
        return VideoUploadResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return VideoUploadResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/list",
    name="视频列表查询",
    response_model=VideoListResponseModel,
)
async def video_list(
    request: VideoListRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """获取用户视频列表"""
    try:
        result = await video_manage_service.get_video_list(
            db, current_user.id, request.startPage, request.pageSize
        )
        return VideoListResponseModel(data=result)
    except Exception as e:
        return VideoListResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/download",
    name="视频下载",
)
async def video_download(
    videoId: int = Query(..., description="视频ID"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """视频下载"""
    try:
        file_path = await video_manage_service.get_video_path(db, current_user.id, videoId)
        return FileResponse(
            path=file_path,
            media_type='application/octet-stream',
            filename=f"video_{videoId}.mp4"
        )
    except HTTPException as e:
        return StandardResponse(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)



@router.get(
    "/download/all",
    name="打包下载",
)
async def download_all(
    videoId: int = Query(..., description="视频ID"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """打包下载视频和骨骼点文件"""
    try:
        memory_file = await video_manage_service.get_video_all(db, current_user.id, videoId)

        def iterfile():
            yield from memory_file

        return StreamingResponse(
            iterfile(),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=video_{videoId}_package.zip"}
        )
    except HTTPException as e:
        return StandardResponse(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)


@router.post(
    "/upload_chunk",
    name="分片上传",
    response_model=ChunkUploadResponseModel,
)
async def upload_chunk(
    chunk: UploadFile = File(...),
    chunkNumber: int = Form(...),
    totalChunks: int = Form(...),
    fileId: str = Form(...),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """分片上传"""
    try:
        result = await video_manage_service.save_chunk(
            current_user.id, fileId, chunk, chunkNumber, totalChunks
        )
        return ChunkUploadResponseModel(data=result)
    except Exception as e:
        return ChunkUploadResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/merge_chunks",
    name="合并分片",
    response_model=MergeChunksResponseModel,
)
async def merge_chunks(
    fileId: str = Form(...),
    filename: str = Form(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """合并分片"""
    try:
        result = await video_manage_service.merge_chunks(
            db, current_user.id, fileId, filename
        )
        return MergeChunksResponseModel(data=result)
    except HTTPException as e:
        return MergeChunksResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return MergeChunksResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/upload/status",
    name="上传查询",
    response_model=VideoUploadStatusResponseModel,
)
async def video_upload_status(
    fileId: str = Query(..., description="视频id"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """查询上传状态"""
    try:
        result = await video_manage_service.get_video_upload_status(db, current_user.id, fileId)
        return VideoUploadStatusResponseModel(data=result)
    except Exception as e:
        return VideoUploadStatusResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


# 新增的API接口，根据前端文档要求

@router.post(
    "/process",
    name="视频处理",
    response_model=VideoProcessResponseModel,
)
async def video_process(
    request: VideoProcessRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """视频处理"""
    try:
        await video_manage_service.process_video(db, current_user.id, request.video_id)
        return VideoProcessResponseModel(data={})
    except HTTPException as e:
        return VideoProcessResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return VideoProcessResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/process_status",
    name="处理查询",
)
async def video_process_status(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """查询处理状态"""
    try:
        result = await video_manage_service.get_process_status(db, current_user.id)
        return StandardResponse(data=result)
    except Exception as e:
        return StandardResponse(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/simple-upload",
    name="视频上传",
    response_model=SimpleVideoUploadResponseModel,
    responses={
        200: {"description": "视频上传成功", "model": SimpleVideoUploadResponseModel},
        400: {"description": "请求参数错误", "model": VideoErrorResponseModel},
        401: {"description": "未授权，需要登录", "model": VideoErrorResponseModel},
        413: {"description": "文件过大", "model": VideoErrorResponseModel},
        415: {"description": "不支持的媒体类型", "model": VideoErrorResponseModel},
        500: {"description": "服务器内部错误", "model": VideoErrorResponseModel},
    }
)
async def simple_video_upload(
    video: UploadFile = File(..., description="视频文件"),
    filename: str = Form(..., description="文件名称"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """上传视频文件并获取视频ID和访问URL，支持上传进度监听"""
    try:
        # 检查文件大小（500MB限制）
        if video.size and video.size > 500 * 1024 * 1024:
            return SimpleVideoUploadResponseModel(
                code=400,
                message="文件大小超过限制，建议不超过500MB",
            ).to_json(status_code=HTTP_413_REQUEST_ENTITY_TOO_LARGE)

        # 检查文件格式
        allowed_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        if f'.{file_ext}' not in allowed_extensions:
            return SimpleVideoUploadResponseModel(
                code=400,
                message="文件格式不支持，仅支持mp4, avi, mov, wmv, flv, mkv格式",
            ).to_json(status_code=HTTP_415_UNSUPPORTED_MEDIA_TYPE)

        result = await video_manage_service.simple_upload_video(db, current_user.id, video, filename)
        return SimpleVideoUploadResponseModel(
            code=0,
            message="视频上传成功",
            data=result
        )
    except HTTPException as e:
        error_detail = e.detail
        if "未授权" in error_detail or "需要登录" in error_detail:
            return SimpleVideoUploadResponseModel(
                code=401,
                message="未授权，需要登录",
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        elif "文件格式" in error_detail:
            return SimpleVideoUploadResponseModel(
                code=400,
                message="文件格式不支持，仅支持mp4, avi, mov, wmv, flv, mkv格式",
            ).to_json(status_code=HTTP_415_UNSUPPORTED_MEDIA_TYPE)
        elif "文件大小" in error_detail:
            return SimpleVideoUploadResponseModel(
                code=400,
                message="文件大小超过限制，建议不超过500MB",
            ).to_json(status_code=HTTP_413_REQUEST_ENTITY_TOO_LARGE)
        else:
            return SimpleVideoUploadResponseModel(
                code=400,
                message="请求参数错误",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return SimpleVideoUploadResponseModel(
            code=500,
            message="服务器内部错误，请稍后重试",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.post(
    "/create-file",
    name="创建文件记录",
    response_model=CreateFileResponseModel,
    responses={
        200: {"description": "文件记录创建成功", "model": CreateFileResponseModel},
        400: {"description": "请求参数错误", "model": VideoErrorResponseModel},
        401: {"description": "未授权，需要登录", "model": VideoErrorResponseModel},
        404: {"description": "视频资源不存在", "model": VideoErrorResponseModel},
        500: {"description": "服务器内部错误", "model": VideoErrorResponseModel},
    }
)
async def create_file_record(
    request: CreateFileRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """基于已上传的视频创建文件记录，支持视频裁剪和格式转换配置"""
    try:
        # 验证请求参数
        if request.endTime <= request.startTime:
            return CreateFileResponseModel(
                code=400,
                message="时间范围错误，endTime必须大于startTime",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)

        if not (1 <= request.outputFormat <= 4):
            return CreateFileResponseModel(
                code=400,
                message="请求参数错误，outputFormat必须为1-4之间的数字",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)

        result = await video_manage_service.create_file_record(db, current_user.id, request)
        return CreateFileResponseModel(
            code=0,
            message="文件记录创建成功",
            data=result
        )
    except HTTPException as e:
        error_detail = e.detail
        if "未授权" in error_detail or "需要登录" in error_detail:
            return CreateFileResponseModel(
                code=401,
                message="未授权，需要登录",
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        elif "视频资源不存在" in error_detail or "视频不存在" in error_detail:
            return CreateFileResponseModel(
                code=404,
                message="视频资源不存在，请先上传视频",
            ).to_json(status_code=HTTP_404_NOT_FOUND)
        elif "时间范围错误" in error_detail:
            return CreateFileResponseModel(
                code=400,
                message="时间范围错误，endTime必须大于startTime",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
        elif "outputFormat" in error_detail:
            return CreateFileResponseModel(
                code=400,
                message="请求参数错误，outputFormat必须为1-4之间的数字",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
        else:
            return CreateFileResponseModel(
                code=400,
                message="请求参数错误",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return CreateFileResponseModel(
            code=500,
            message="服务器内部错误，请稍后重试",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.post(
    "/multi-upload/start",
    name="开始多文件上传",
    response_model=MultiVideoUploadResponseModel,
    responses={
        200: {"description": "多文件上传任务创建成功", "model": MultiVideoUploadResponseModel},
        400: {"description": "请求参数错误", "model": VideoErrorResponseModel},
        401: {"description": "未授权，需要登录", "model": VideoErrorResponseModel},
        500: {"description": "服务器内部错误", "model": VideoErrorResponseModel},
    }
)
async def start_multi_upload(
    request: MultiVideoUploadRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """开始多文件上传任务，最多支持5个文件同时上传"""
    try:
        result = await video_manage_service.start_multi_upload(current_user.id, request.filenames)
        return MultiVideoUploadResponseModel(
            code=0,
            message="多文件上传任务创建成功",
            data=result
        )
    except HTTPException as e:
        error_detail = e.detail
        if "未授权" in error_detail or "需要登录" in error_detail:
            return MultiVideoUploadResponseModel(
                code=401,
                message="未授权，需要登录",
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        elif "最多只能" in error_detail:
            return MultiVideoUploadResponseModel(
                code=400,
                message="最多只能同时上传5个文件",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
        elif "格式不支持" in error_detail:
            return MultiVideoUploadResponseModel(
                code=400,
                message="文件格式不支持，仅支持mp4, avi, mov, wmv, flv, mkv格式",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
        else:
            return MultiVideoUploadResponseModel(
                code=400,
                message=error_detail.split("_", 1)[1] if "_" in error_detail else error_detail,
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return MultiVideoUploadResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.post(
    "/multi-upload/file",
    name="上传单个文件",
    response_model=SimpleVideoUploadResponseModel,
    responses={
        200: {"description": "文件上传成功", "model": SimpleVideoUploadResponseModel},
        400: {"description": "请求参数错误", "model": VideoErrorResponseModel},
        401: {"description": "未授权，需要登录", "model": VideoErrorResponseModel},
        404: {"description": "上传任务不存在", "model": VideoErrorResponseModel},
        413: {"description": "文件过大", "model": VideoErrorResponseModel},
        415: {"description": "不支持的媒体类型", "model": VideoErrorResponseModel},
        500: {"description": "服务器内部错误", "model": VideoErrorResponseModel},
    }
)
async def upload_file_in_batch(
    uploadId: str = Form(..., description="批量上传任务ID"),
    filename: str = Form(..., description="文件名称"),
    video: UploadFile = File(..., description="视频文件"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """在批量上传任务中上传单个文件"""
    try:
        # 检查文件大小（500MB限制）
        if video.size and video.size > 500 * 1024 * 1024:
            return SimpleVideoUploadResponseModel(
                code=400,
                message="文件大小超过限制，建议不超过500MB",
            ).to_json(status_code=HTTP_413_REQUEST_ENTITY_TOO_LARGE)

        # 检查文件格式
        allowed_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        if f'.{file_ext}' not in allowed_extensions:
            return SimpleVideoUploadResponseModel(
                code=400,
                message="文件格式不支持，仅支持mp4, avi, mov, wmv, flv, mkv格式",
            ).to_json(status_code=HTTP_415_UNSUPPORTED_MEDIA_TYPE)

        await video_manage_service.upload_file_in_batch(db, uploadId, filename, video, current_user.id)

        return SimpleVideoUploadResponseModel(
            code=0,
            message="文件上传成功",
            data={"id": uploadId, "url": f"/api/video/progress/{uploadId}"}
        )
    except HTTPException as e:
        error_detail = e.detail
        if "未授权" in error_detail or "需要登录" in error_detail:
            return SimpleVideoUploadResponseModel(
                code=401,
                message="未授权，需要登录",
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        elif "不存在" in error_detail:
            return SimpleVideoUploadResponseModel(
                code=404,
                message="上传任务不存在",
            ).to_json(status_code=HTTP_404_NOT_FOUND)
        else:
            return SimpleVideoUploadResponseModel(
                code=400,
                message=error_detail.split("_", 1)[1] if "_" in error_detail else error_detail,
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return SimpleVideoUploadResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.post(
    "/multi-upload/progress",
    name="查询上传进度",
    response_model=UploadProgressResponseModel,
    responses={
        200: {"description": "查询成功", "model": UploadProgressResponseModel},
        400: {"description": "请求参数错误", "model": VideoErrorResponseModel},
        401: {"description": "未授权，需要登录", "model": VideoErrorResponseModel},
        404: {"description": "上传任务不存在", "model": VideoErrorResponseModel},
        500: {"description": "服务器内部错误", "model": VideoErrorResponseModel},
    }
)
async def get_upload_progress(
    request: UploadProgressRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """查询多文件上传进度"""
    try:
        result = await video_manage_service.get_upload_progress(request.uploadId, current_user.id)
        return UploadProgressResponseModel(
            code=0,
            message="查询成功",
            data=result
        )
    except HTTPException as e:
        error_detail = e.detail
        if "未授权" in error_detail or "需要登录" in error_detail:
            return UploadProgressResponseModel(
                code=401,
                message="未授权，需要登录",
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        elif "不存在" in error_detail:
            return UploadProgressResponseModel(
                code=404,
                message="上传任务不存在",
            ).to_json(status_code=HTTP_404_NOT_FOUND)
        elif "无权访问" in error_detail:
            return UploadProgressResponseModel(
                code=403,
                message="无权访问此上传任务",
            ).to_json(status_code=HTTP_404_NOT_FOUND)  # 为了安全，返回404而不是403
        else:
            return UploadProgressResponseModel(
                code=400,
                message=error_detail.split("_", 1)[1] if "_" in error_detail else error_detail,
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return UploadProgressResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)
