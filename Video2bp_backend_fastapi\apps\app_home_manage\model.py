from sqlalchemy import Column, String, Inte<PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.dialects.mysql import LONGTEXT

from models.base import Base
from models.mixins import DateTimeModelMixin


class News(Base["News"], DateTimeModelMixin):
    """新闻模型"""
    __tablename__ = "news"

    name = Column(String(255), default="")
    type = Column(Integer, nullable=False)
    is_display = Column(Boolean, default=True)
    cover = Column(String(500), nullable=False)
    title = Column(String(255), nullable=False)
    author = Column(String(100), nullable=False, default="灵宇 AI 动捕")
    profile_picture = Column(String(500), nullable=False)
    excerpt = Column(Text, nullable=False)
    tag = Column(String(100))
    content = Column(LONGTEXT, nullable=False)
    is_pinned = Column(Boolean, default=False)
