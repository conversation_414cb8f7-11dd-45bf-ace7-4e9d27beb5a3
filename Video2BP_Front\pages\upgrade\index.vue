<template>
  <div class="upgrade-page">
    <div class="upgrade-content">
      <div class="ec-head">
        <div class="ch-left">
          <div class="hl-title">升级您的计划</div>
          <div class="hl-zm">User cases and usage reviews</div>
        </div>
        <div class="ch-right">
          <div v-for="(tab, index) in planTabs" :key="index" class="hr-item" :class="{ active: planType === tab.value }"
            @click="switchPlanType(tab.value)">
            {{ tab.label }}
          </div>
        </div>
      </div>
      <div class="ec-body">
        <PricingCards :plan-type="planType" />
      </div>
      <div class="ec-tips">
        <div class="ct-title">Q&A</div>
        <div class="faq-container">
          <div v-for="(faq, index) in faqList" :key="index" class="faq-item" :class="{ active: activeIndex === index }"
            @click="toggleFaq(index)">
            <div class="faq-question">
              {{ faq.question }}
              <div class="arrow" :class="{ up: activeIndex === index }">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                </svg>
              </div>
            </div>
            <div v-show="activeIndex === index" class="faq-answer">
              <p v-for="(line, lineIndex) in faq.answer" :key="lineIndex">{{ line }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PricingCards from './components/PricingCards.vue'
import type { PlanType, PlanTab } from '~/types/upgrade'

defineOptions({
  name: 'UpgradeView',
})

// Plan tabs configuration
const planTabs: PlanTab[] = [
  { label: '年卡', value: 'year' },
  { label: '月卡', value: 'month' },
]

const planType = ref<PlanType>(planTabs[0].value)
const activeIndex = ref<number | null>(null)

// FAQ data
const faqList = [
  {
    question: '我怎么获得V币?',
    answer: [
      '每个免费用户每月可获赠 50V 币进行自由创作',
      '每月初，免费用户 V 币余额不足 50V 会自动刷新至 50V，超过 50V则不会刷新',
      '用户可以通过购买月卡或年卡获赠大量 V 币，解锁 VIP 功能。',
      '用户也可以通过邀请好友获得 V 币奖励',
    ],
  },
  {
    question: '我购买了月卡版，可以再购买年卡版吗?',
    answer: ['可以，购买年卡后会自动升级为年卡会员，月卡剩余时间将按比例折算为V币返还。'],
  },
  {
    question: '我推荐其他人加入有奖励吗?',
    answer: [
      '有的，您可以通过邀请链接邀请好友注册，每成功邀请一位新用户，您将获得50V币奖励。',
      '被邀请的新用户也将获得额外的20V币奖励。',
    ],
  },
  {
    question: '购买后订单在哪里显示?',
    answer: [
      '购买成功后，您可以在"个人中心"→"我的订单"中查看所有订单记录。',
      '您也会收到一封包含订单详情的确认邮件。',
    ],
  },
  {
    question: '购买后，我有支付方面的疑惑，我应该找谁?',
    answer: [
      '如有任何支付相关问题，请联系我们的客服团队：<EMAIL>',
      '或者通过应用内的"帮助中心"→"联系客服"提交工单，我们会在24小时内回复您。',
    ],
  },
]

const switchPlanType = (type: PlanType) => {
  planType.value = type
}

const toggleFaq = (index: number) => {
  activeIndex.value = activeIndex.value === index ? null : index
}
</script>

<style scoped lang="scss">
.upgrade-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  user-select: none;

  .upgrade-content {
    flex: 1;
    padding: rem(171) rem(120) rem(83);
    background-image: url('~/assets/images/upgrade-bg.webp');
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;

    .ec-head {
      display: flex;
      justify-content: space-between;
      margin-bottom: rem(84);

      .ch-left {
        .hl-title {
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: bold;
          font-size: rem(48);
          color: #ffffff;
          line-height: rem(69);
          text-align: left;
          font-style: normal;
          margin-bottom: rem(12);
        }

        .hl-zm {
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: bold;
          font-size: rem(14);
          color: #ffffff;
          line-height: rem(20);
          letter-spacing: rem(11);
          text-align: left;
          font-style: normal;
          text-transform: uppercase;
          background: linear-gradient(to bottom, #0091ff 0%, #b620e0 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .ch-right {
        display: flex;
        align-items: center;

        .hr-item {
          flex: 1;
          cursor: pointer;
          padding: rem(18) rem(64) rem(22);
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: bold;
          font-size: rem(32);
          color: #ffffff;
          line-height: rem(32);
          text-align: left;
          font-style: normal;
          background-color: rgba(255, 255, 255, 0.1);

          &.active {
            background: #0a84ff;
          }
        }
      }
    }

    .ec-body {
      margin-bottom: rem(60);
    }

    .ec-tips {
      .ct-title {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: bold;
        font-size: rem(72);
        color: #ffffff;
        line-height: rem(104);
        text-align: center;
        font-style: normal;
        margin-bottom: rem(25);
      }

      .faq-container {
        width: 100%;
        margin: 0 auto;
      }

      .faq-item {
        margin-bottom: rem(40);
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

        .faq-question {
          position: relative;
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          align-items: center;

          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: bold;
          font-size: rem(36);
          color: #ffffff;
          line-height: rem(38);
          text-align: left;
          font-style: normal;

          .arrow {
            transition: transform 0.3s ease;

            &.up {
              transform: rotate(180deg);
            }
          }
        }

        &.active {
          &::after {
            content: '';
            position: absolute;
            bottom: rem(-4);
            left: 0;
            width: 100%;
            height: rem(4);
            background: linear-gradient(270deg, rgba(0, 196, 255, 0.1) 0%, #0091ff 100%);
          }

          border-bottom: none;
        }

        .faq-answer {
          padding: 20px 0;
          opacity: 0.8;
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: 400;
          font-size: rem(18);
          color: #ffffff;
          line-height: rem(28);
          text-align: left;
          font-style: normal;
          //   p {
          //     margin: 8px 0;
          //   }
        }
      }
    }
  }
}
</style>
