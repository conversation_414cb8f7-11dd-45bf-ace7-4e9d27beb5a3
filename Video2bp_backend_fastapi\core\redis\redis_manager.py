"""
Redis连接管理器
提供统一的Redis连接和操作接口
"""

import asyncio
import logging
from typing import Optional, Any, Union
from contextlib import asynccontextmanager

import redis
try:
    from redis import asyncio as aioredis
except ImportError:
    # 兼容旧版本Redis
    try:
        import aioredis
    except ImportError:
        aioredis = None
from redis.exceptions import ConnectionError, TimeoutError, RedisError

from core.config import settings

logger = logging.getLogger(__name__)


class RedisManager:
    """Redis连接管理器"""
    
    def __init__(self):
        self._sync_client: Optional[redis.Redis] = None
        self._async_client: Optional[aioredis.Redis] = None
        self._connection_pool: Optional[redis.ConnectionPool] = None
        self._async_connection_pool: Optional[aioredis.ConnectionPool] = None
        
    def _parse_redis_url(self, url: str) -> dict:
        """解析Redis URL"""
        try:
            # 处理不同的Redis URL格式
            if url.startswith('redis://'):
                # 标准格式: redis://[password@]host:port/db
                parts = url.replace('redis://', '').split('/')
                db = int(parts[1]) if len(parts) > 1 and parts[1] else 0
                
                host_part = parts[0]
                if '@' in host_part:
                    password, host_port = host_part.split('@', 1)
                else:
                    password = None
                    host_port = host_part
                
                if ':' in host_port:
                    host, port = host_port.split(':', 1)
                    port = int(port)
                else:
                    host = host_port
                    port = 6379
                    
                return {
                    'host': host,
                    'port': port,
                    'db': db,
                    'password': password,
                    'decode_responses': True,
                    'socket_timeout': 5,
                    'socket_connect_timeout': 5,
                    'retry_on_timeout': True,
                    'health_check_interval': 30,
                }
            else:
                # 简单格式: host:port
                if ':' in url:
                    host, port = url.split(':', 1)
                    port = int(port)
                else:
                    host = url
                    port = 6379
                    
                return {
                    'host': host,
                    'port': port,
                    'db': 0,
                    'decode_responses': True,
                    'socket_timeout': 5,
                    'socket_connect_timeout': 5,
                    'retry_on_timeout': True,
                    'health_check_interval': 30,
                }
        except Exception as e:
            logger.error(f"Failed to parse Redis URL: {url}, error: {e}")
            # 返回默认配置
            return {
                'host': 'localhost',
                'port': 6379,
                'db': 0,
                'decode_responses': True,
                'socket_timeout': 5,
                'socket_connect_timeout': 5,
                'retry_on_timeout': True,
                'health_check_interval': 30,
            }
    
    def get_sync_client(self) -> redis.Redis:
        """获取同步Redis客户端"""
        if self._sync_client is None:
            try:
                if not settings.REDIS_URL:
                    logger.warning("Redis URL not configured, using default localhost:6379")
                    redis_config = {
                        'host': 'localhost',
                        'port': 6379,
                        'db': 0,
                        'decode_responses': True,
                    }
                else:
                    redis_config = self._parse_redis_url(settings.REDIS_URL)
                
                # 创建连接池
                self._connection_pool = redis.ConnectionPool(**redis_config)
                self._sync_client = redis.Redis(connection_pool=self._connection_pool)
                
                # 测试连接
                self._sync_client.ping()
                logger.info(f"Redis sync client connected successfully to {redis_config['host']}:{redis_config['port']}")
                
            except Exception as e:
                logger.error(f"Failed to create Redis sync client: {e}")
                # 创建一个模拟客户端，避免应用崩溃
                self._sync_client = MockRedisClient()
                
        return self._sync_client
    
    async def get_async_client(self):
        """获取异步Redis客户端"""
        if self._async_client is None:
            try:
                if aioredis is None:
                    logger.warning("Async Redis not available, using mock client")
                    self._async_client = MockAsyncRedisClient()
                    return self._async_client

                if not settings.REDIS_URL:
                    logger.warning("Redis URL not configured, using default localhost:6379")
                    redis_config = {
                        'host': 'localhost',
                        'port': 6379,
                        'db': 0,
                        'decode_responses': True,
                    }
                else:
                    redis_config = self._parse_redis_url(settings.REDIS_URL)

                # 创建异步连接池
                if hasattr(aioredis, 'ConnectionPool'):
                    # Redis 5.x+ 风格
                    self._async_connection_pool = aioredis.ConnectionPool(**redis_config)
                    self._async_client = aioredis.Redis(connection_pool=self._async_connection_pool)
                else:
                    # 旧版本aioredis风格
                    self._async_client = await aioredis.create_redis_pool(
                        f"redis://{redis_config['host']}:{redis_config['port']}/{redis_config['db']}",
                        encoding='utf-8'
                    )

                # 测试连接
                await self._async_client.ping()
                logger.info(f"Redis async client connected successfully to {redis_config['host']}:{redis_config['port']}")

            except Exception as e:
                logger.error(f"Failed to create Redis async client: {e}")
                # 创建一个模拟客户端，避免应用崩溃
                self._async_client = MockAsyncRedisClient()

        return self._async_client
    
    async def close(self):
        """关闭Redis连接"""
        try:
            if self._async_client:
                await self._async_client.close()
                logger.info("Redis async client closed")
                
            if self._sync_client:
                self._sync_client.close()
                logger.info("Redis sync client closed")
                
        except Exception as e:
            logger.error(f"Error closing Redis connections: {e}")
    
    @asynccontextmanager
    async def get_async_connection(self):
        """获取异步Redis连接的上下文管理器"""
        client = await self.get_async_client()
        try:
            yield client
        except Exception as e:
            logger.error(f"Redis operation error: {e}")
            raise
    
    def test_connection(self) -> bool:
        """测试Redis连接"""
        try:
            client = self.get_sync_client()
            client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis connection test failed: {e}")
            return False
    
    async def test_async_connection(self) -> bool:
        """测试异步Redis连接"""
        try:
            client = await self.get_async_client()
            await client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis async connection test failed: {e}")
            return False


class MockRedisClient:
    """模拟Redis客户端，当Redis不可用时使用"""
    
    def __init__(self):
        self._data = {}
        logger.warning("Using MockRedisClient - Redis operations will not persist")
    
    def ping(self):
        return True
    
    def get(self, key):
        return self._data.get(key)
    
    def set(self, key, value, ex=None):
        self._data[key] = value
        return True
    
    def setex(self, key, time, value):
        self._data[key] = value
        return True
    
    def delete(self, key):
        return self._data.pop(key, None) is not None
    
    def incr(self, key):
        current = int(self._data.get(key, 0))
        self._data[key] = str(current + 1)
        return current + 1
    
    def expire(self, key, time):
        return True
    
    def close(self):
        pass


class MockAsyncRedisClient:
    """模拟异步Redis客户端"""
    
    def __init__(self):
        self._data = {}
        logger.warning("Using MockAsyncRedisClient - Redis operations will not persist")
    
    async def ping(self):
        return True
    
    async def get(self, key):
        return self._data.get(key)
    
    async def set(self, key, value, ex=None):
        self._data[key] = value
        return True
    
    async def setex(self, key, time, value):
        self._data[key] = value
        return True
    
    async def delete(self, key):
        return self._data.pop(key, None) is not None
    
    async def incr(self, key):
        current = int(self._data.get(key, 0))
        self._data[key] = str(current + 1)
        return current + 1
    
    async def expire(self, key, time):
        return True
    
    async def close(self):
        pass


# 全局Redis管理器实例
redis_manager = RedisManager()


# 便捷函数
def get_redis() -> redis.Redis:
    """获取同步Redis客户端"""
    return redis_manager.get_sync_client()


async def get_async_redis() -> aioredis.Redis:
    """获取异步Redis客户端"""
    return await redis_manager.get_async_client()


async def close_redis():
    """关闭Redis连接"""
    await redis_manager.close()
