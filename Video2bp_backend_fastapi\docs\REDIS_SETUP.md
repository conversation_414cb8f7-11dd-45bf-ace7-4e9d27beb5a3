# Redis安装和配置指南

## 概述

本项目使用Redis作为缓存和会话存储，支持用户认证、验证码存储、登录失败锁定等功能。

## Redis安装

### Windows安装

#### 方法1：使用官方安装包
1. 下载Redis for Windows：https://github.com/microsoftarchive/redis/releases
2. 下载最新版本的`.msi`文件
3. 运行安装程序，按默认设置安装
4. 安装完成后Redis会自动启动为Windows服务

#### 方法2：使用Chocolatey
```bash
# 安装Chocolatey（如果未安装）
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装Redis
choco install redis-64
```

#### 方法3：使用Docker
```bash
# 拉取Redis镜像
docker pull redis:latest

# 运行Redis容器
docker run -d --name redis-server -p 6379:6379 redis:latest

# 或者使用持久化存储
docker run -d --name redis-server -p 6379:6379 -v redis-data:/data redis:latest redis-server --appendonly yes
```

### Linux安装

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装Redis
sudo apt install redis-server

# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### CentOS/RHEL
```bash
# 安装EPEL仓库
sudo yum install epel-release

# 安装Redis
sudo yum install redis

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

### macOS安装

#### 使用Homebrew
```bash
# 安装Redis
brew install redis

# 启动Redis服务
brew services start redis
```

## Redis配置

### 基本配置检查

1. **检查Redis是否运行**：
```bash
# Windows
redis-cli ping

# Linux/macOS
redis-cli ping
```
应该返回 `PONG`

2. **检查Redis版本**：
```bash
redis-cli --version
```

3. **连接到Redis**：
```bash
redis-cli
127.0.0.1:6379> info server
```

### 项目配置

项目的Redis配置在 `core/.env` 文件中：

```env
# Redis配置
REDIS_URL=redis://localhost:6379/0
```

#### 配置说明
- `localhost:6379`: Redis服务器地址和端口
- `/0`: 使用数据库0（Redis支持16个数据库，编号0-15）

#### 其他配置选项
```env
# 带密码的Redis
REDIS_URL=redis://:password@localhost:6379/0

# 远程Redis服务器
REDIS_URL=redis://username:<EMAIL>:6379/0

# Redis Sentinel（高可用）
REDIS_URL=redis://sentinel1:26379,sentinel2:26379,sentinel3:26379/mymaster

# Redis Cluster
REDIS_URL=redis://node1:7000,node2:7000,node3:7000
```

## 测试Redis连接

运行项目提供的测试脚本：

```bash
# 激活虚拟环境
fastapi_env\Scripts\activate

# 运行Redis测试
python test_redis.py
```

测试脚本会验证：
- Redis基础连接
- 同步和异步操作
- 字符串和JSON存储
- 计数器功能
- 认证安全系统的Redis使用

## Redis在项目中的使用

### 1. 用户认证
- **验证码存储**：邮箱验证码临时存储
- **登录失败计数**：防止暴力破解攻击
- **账号锁定状态**：临时锁定恶意登录的账号

### 2. 会话管理
- **JWT Token黑名单**：已注销的token
- **用户会话信息**：临时用户状态

### 3. 缓存
- **用户信息缓存**：减少数据库查询
- **配置缓存**：系统配置信息
- **API响应缓存**：提高响应速度

## Redis键命名规范

项目使用以下键命名规范：

```
# 验证码
email_verification:{email}

# 登录安全
login_attempts:{email}
account_locked:{email}

# 用户会话
user_session:{user_id}
user_token:{user_id}

# 缓存
cache:user:{user_id}
cache:config:{key}
```

## 性能优化

### 1. 连接池配置
项目使用连接池来优化Redis连接：
- 最大连接数：100
- 连接超时：5秒
- 健康检查间隔：30秒

### 2. 数据过期策略
- 验证码：5分钟过期
- 登录失败计数：15分钟窗口
- 账号锁定：10分钟过期
- 用户缓存：1小时过期

### 3. 内存优化
- 使用合适的数据类型
- 设置合理的过期时间
- 定期清理无用数据

## 故障排除

### 常见问题

1. **连接被拒绝**
```
redis.exceptions.ConnectionError: Error 10061 connecting to localhost:6379
```
**解决方案**：
- 检查Redis服务是否启动
- 确认端口6379未被占用
- 检查防火墙设置

2. **认证失败**
```
redis.exceptions.AuthenticationError: Authentication required
```
**解决方案**：
- 检查Redis密码配置
- 更新REDIS_URL中的密码

3. **内存不足**
```
redis.exceptions.ResponseError: OOM command not allowed when used memory > 'maxmemory'
```
**解决方案**：
- 增加Redis内存限制
- 清理过期数据
- 优化数据存储

### 调试命令

```bash
# 查看Redis信息
redis-cli info

# 查看所有键
redis-cli keys "*"

# 查看特定键的值
redis-cli get "key_name"

# 查看键的过期时间
redis-cli ttl "key_name"

# 清空数据库
redis-cli flushdb

# 监控Redis命令
redis-cli monitor
```

## 生产环境建议

1. **安全配置**
   - 设置强密码
   - 禁用危险命令
   - 配置防火墙

2. **性能调优**
   - 调整内存限制
   - 配置持久化策略
   - 监控性能指标

3. **高可用性**
   - 配置Redis Sentinel
   - 设置主从复制
   - 定期备份数据

4. **监控告警**
   - 内存使用率
   - 连接数
   - 响应时间
   - 错误率
