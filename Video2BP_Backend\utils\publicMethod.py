import datetime
import os
import re
import shutil
import time
import uuid
from functools import wraps
from hashlib import md5

import jwt
from dateutil import parser
from flask import request
from flask import g

from settings import VIDEO_EXTENSIONS, SECRET_KEY
from utils.R import R
from utils.exceptions import ServerError


def zip_folder(folder_path, zip_path):
    """压缩文件夹"""
    # 获取文件夹名称
    base_folder = os.path.basename(folder_path)
    parent_dir = os.path.dirname(folder_path)

    # 切换到父目录，只压缩目标文件夹而不包含完整路径
    current_dir = os.getcwd()
    os.chdir(parent_dir)
    shutil.make_archive(zip_path, "zip", ".", base_folder)
    os.chdir(current_dir)  # 恢复原来的工作目录


def getuu_id():
    uuId = str(uuid.uuid1())
    return uuId.replace("-", "")


def get_sign(srcData, key):
    srcData = f"{srcData}:{key}"
    bodymd5 = md5(srcData.encode("utf-8")).hexdigest()
    return bodymd5.lower()


def global_str_md5(data):
    bodymd5 = md5(data.encode("utf-8")).hexdigest()
    return bodymd5.upper()


def global_get_now_time(offset_days=0, is_midnight=False):
    """获取当前时间（支持天数偏移和零点时间）
    
    Args:
        offset_days (int): 天数偏移量，默认0
        is_midnight (bool): 是否返回当日零点时间，默认False
    """
    now = datetime.datetime.now()
    if is_midnight:
        # 获取当日零点时间并应用偏移
        midnight = now.replace(hour=0, minute=0, second=0, microsecond=0)
        return midnight + datetime.timedelta(days=offset_days)
    return now + datetime.timedelta(days=offset_days)


def global_get_now_time2():
    now = parser.parse(time.strftime("%Y%m%d%H%M%S", time.localtime()))
    return now


# 获取stringTime 转化 datetime时间
def global_sting_to_time(stringTime):
    return datetime.datetime.strptime(stringTime, "%Y-%m-%d %H:%M:%S")


def global_time_to_string(time):
    # isodatetime格式时间转字符串
    if time is not None:
        return time.strftime("%Y-%m-%d %H:%M:%S")
    return ""


def verify_username(username: str):
    if not username:
        return False
    if len(username) > 32 or len(username) < 4:
        return False
    if not re.match(r"^[a-zA-Z0-9_]*$", username):
        return False
    return True


def verify_pwd(pwd):
    if not pwd:
        return False
    if len(pwd) > 32 or len(pwd) < 8:
        return False
    if not re.match(r"^[a-zA-Z0-9!\\'#$%&()*+,-./:;<=>?@\]\[^_`{|}~]*$", pwd):
        return False
    return True


def validate_pagination_params(j_data):
    """校验分页参数公共方法"""
    try:
        return (
            int(j_data.get("startPage", 1)),
            int(j_data.get("pageSize", 10))
        )
    except (TypeError, ValueError):
        raise ServerError(ServerError.INVALID_PARAMS, {
            "startPage": j_data.get("startPage"),
            "pageSize": j_data.get("pageSize")
        })


def allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in VIDEO_EXTENSIONS


def token_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return R.error(ServerError.TOKEN_MISSING)
        
        try:
            # 验证token有效性
            data = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
            # 检查token是否过期
            if data['exp'] < int(time.time()):
                return R.error(ServerError.TOKEN_EXPIRED)
            
            # 使用g对象存储用户ID
            g.user_id = data['user_id']
            
        except jwt.ExpiredSignatureError:
            return R.error(ServerError.TOKEN_EXPIRED)
        except jwt.InvalidTokenError:
            return R.error(ServerError.TOKEN_INVALID)
            
        return f(*args, **kwargs)
    return decorated_function
