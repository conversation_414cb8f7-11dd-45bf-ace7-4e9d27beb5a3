import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

type Theme = 'light' | 'dark';

export const useAppStore = defineStore('app', () => {
	// state
	const theme = ref<Theme>('light');
	const language = ref('zh-CN'); // 默认值，会被持久化数据覆盖
	const isLoading = ref(false);

	// getters
	const currentTheme = computed(() => theme.value);
	const isLoadingStatus = computed(() => isLoading.value);

	// actions
	function toggleTheme() {
		theme.value = theme.value === 'light' ? 'dark' : 'light';
	}

	function setLanguage(lang: string) {
		language.value = lang;
	}

	function setLoading(loading: boolean) {
		isLoading.value = loading;
	}

	return {
		theme,
		language,
		isLoading,
		currentTheme,
		isLoadingStatus,
		toggleTheme,
		setLanguage,
		setLoading,
	};
});
