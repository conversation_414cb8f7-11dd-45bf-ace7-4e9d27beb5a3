<template>
    <div class="evaluate-container">
        <swiper :slidesPerView="3" :spaceBetween="24" :navigation="false" :modules="modules" class="evaluate-swiper">
            <swiper-slide v-for="(item, index) in evaluateList" :key="index">
                <div class="evaluate-card">
                    <div class="user-info">
                        <div class="avatar">
                            <img :src="item.avatar" alt="用户头像" />
                        </div>
                        <div class="user-details">
                            <div class="username">{{ item.name }}</div>
                            <div class="time">评价于: {{ item.time }}</div>
                        </div>
                    </div>
                    <div class="evaluate-content">
                        {{ item.content }}
                    </div>
                    <div class="video-preview">
                        <video :ref="el => { videoRefs[index] = el as HTMLVideoElement | null }" :src="item.preview"
                            class="video-element" playsinline preload="metadata" @ended="onVideoEnded(index)"></video>
                        <VideoPlayControl :videoRef="videoRefs[index]" :initialPlaying="videoPlayingStates[index]"
                            @toggle="toggleVideo(index)" />
                    </div>
                </div>
            </swiper-slide>
        </swiper>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import type { UserCaseItem } from '~/types/business'
import { useApi } from '~/composables/useApi'
import VideoPlayControl from '~/pages/home/<USER>/VideoPlayControl.vue'


// 导入 Swiper 组件和模块
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/pagination'

// 定义 Swiper 模块
const modules = [Pagination]

const { business } = useApi()

// 使用数组存储每个轮播项的视频引用
const videoRefs = reactive<(HTMLVideoElement | null)[]>([])
// 使用数组存储每个轮播项的视频播放状态
const videoPlayingStates = reactive<boolean[]>([])

// 响应式数据管理
const evaluateList = ref<UserCaseItem[]>([])

// API 调用函数
const fetchUserCases = async () => {
    try {
        // 真实API调用
        const result = await business?.getUserCases({})

        if (result) {
            evaluateList.value = result
            console.log('获取用户案例成功:', result)
        }
    } catch (err) {
        console.error('获取用户案例失败:', err)
    }
}

// 初始化视频引用和播放状态数组
const initializeVideoArrays = () => {
    // 确保数组长度与轮播项数量匹配
    videoRefs.length = evaluateList.value.length
    videoPlayingStates.length = evaluateList.value.length

    // 初始化所有视频为暂停状态
    for (let i = 0; i < videoPlayingStates.length; i++) {
        videoPlayingStates[i] = false
    }
}

// 监听evaluateList变化，重新初始化视频数组
watch(evaluateList, () => {
    initializeVideoArrays()
}, { deep: true })

// 页面初始化
onMounted(async () => {
    await fetchUserCases()
    initializeVideoArrays()
})

// 暂停所有视频
const pauseAllVideos = () => {
    videoRefs.forEach((video, index) => {
        if (video && !video.paused) {
            video.pause()
            videoPlayingStates[index] = false
        }
    })
}

// 处理视频播放结束事件
const onVideoEnded = (index: number) => {
    console.log(`视频 ${index} 播放结束`)
    // 更新播放状态为暂停
    videoPlayingStates[index] = false

    // 确保视频回到开始位置但不自动播放
    const video = videoRefs[index]
    if (video) {
        video.currentTime = 0
    }
}

// 切换指定索引的视频播放/暂停
const toggleVideo = (index: number) => {
    console.log('尝试切换视频:', index)
    const video = videoRefs[index]
    console.log('视频元素:', video)

    if (video) {
        if (video.paused) {
            // 播放前先暂停其他视频
            pauseAllVideos()

            // 播放当前视频
            const playPromise = video.play()
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('视频开始播放')
                    videoPlayingStates[index] = true
                }).catch(error => {
                    console.error('播放视频时出错:', error)
                    // 自动播放策略阻止播放
                    videoPlayingStates[index] = false
                })
            }
        } else {
            video.pause()
            videoPlayingStates[index] = false
            console.log('视频已暂停')
        }
    } else {
        console.error('视频元素不存在')
    }
}


</script>

<style lang="scss" scoped>
.evaluate-container {
    width: 100%;
    padding: rem(20) 0;
    overflow: hidden;
}

.evaluate-swiper {
    width: 100%;
    padding: rem(10) 0;
}

// 添加 swiper-slide 样式
:deep(.swiper-slide) {
    width: 31.25% !important;
    flex-shrink: 0;
}

.evaluate-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: rem(16);
    padding: rem(24);
    backdrop-filter: blur(rem(10));
    box-shadow: 0 rem(8) rem(32) rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    gap: rem(16);
    height: auto;
    margin-bottom: rem(10);
}

.user-info {
    display: flex;
    align-items: center;
    gap: rem(12);
}

.avatar {
    width: rem(48);
    height: rem(48);
    border-radius: 50%;
    overflow: hidden;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}

.user-details {
    display: flex;
    flex-direction: column;

    .username {
        font-size: rem(16);
        font-weight: 600;
        color: #fff;
    }

    .time {
        font-size: rem(14);
        color: rgba(255, 255, 255, 0.6);
    }
}

.evaluate-content {
    font-family: STSongti-SC, STSongti-SC;
    font-weight: 400;
    font-size: rem(18);
    color: #FFFFFF;
    line-height: rem(28);
    letter-spacing: rem(1);
    text-align: justify;
    font-style: normal;
}

.video-preview {
    position: relative;
    width: 100%;
    height: rem(307);
    border-radius: rem(12);
    overflow: hidden;

    .video-element {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}
</style>
