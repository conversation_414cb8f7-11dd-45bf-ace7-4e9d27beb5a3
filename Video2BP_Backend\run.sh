#!/bin/bash
# install requirements packages
# start web service

# install requirements packages
build()
{
  pip3 install --upgrade pip==24.2 -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
  pip3 install --no-cache-dir -r requirements.txt -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
#  python setup.py build_ext --inplace
}
# start web service
start()
{
  gunicorn -c gconfig.py server:app
}

stop()
{
	pids=`ps -ef | grep "server:app" | grep -v grep  |awk '{print $2}'`
	for pid in $pids
	do
 		`kill -9 $pid`
		echo "PID $pid kill successfully!"
	done
}

if [ $1 = start ]; then
  	start
elif [ $1 = build ]; then
	build
elif [ $1 = stop ]; then
	stop
fi
