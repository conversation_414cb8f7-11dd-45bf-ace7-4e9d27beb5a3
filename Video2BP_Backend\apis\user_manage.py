import re
import time
import jwt
from flask import Blueprint, make_response

import settings
from settings import SECRET_KEY
from service import user_manage_service
from urls import URLs
from utils.R import R
from utils.exceptions import ServerError

from flask import request

blueprint = Blueprint("user_manage", __name__)

# 发送邮箱验证码
@blueprint.route(URLs.user_send_verification_code.url, methods=URLs.user_send_verification_code.methods)
def send_verification_code():
    data = request.get_json()
    email = data.get("email")
    if not email or not re.match(r"[^@]+@[^@]+\.[^@]+", email):
        return R.error(ServerError.INVALID_PARAMS, {"email": email})
    user_manage_service.send_email_verification_code(email)
    return R.success()

# 用户注册
@blueprint.route(URLs.user_register.url, methods=URLs.user_register.methods)
def register():
    data = request.get_json()
    email = data.get("email")
    password = data.get("password")
    code = data.get("code")

    if not all([email, password, code]):
        info = {"email": email, "password": password, "code": code}
        return R.error(ServerError.INVALID_PARAMS, data=info)

    if not user_manage_service.verify_email_verification_code(email, code):
        return R.error(ServerError.VERIFICATION_CODE_ERROR)

    user_manage_service.user_register(email, password)
    return R.success()


# 修改用户登录方法
@blueprint.route(URLs.user_login.url, methods=URLs.user_login.methods)
def login():
    data = request.get_json()
    email = data.get("email")
    password = data.get("password")

    if not all([email, password]):
        info = {"email": email, "password": password}
        return R.error(ServerError.INVALID_PARAMS, info)

    user_id, token, email = user_manage_service.user_login(email, password)
    payload = {
        "user_id": user_id,
        "email": email,
        "exp": int(time.time()) + 604800  # 7天=604800秒
    }
    token = jwt.encode(payload, SECRET_KEY, algorithm="HS256")

    # 3. 将Token设置到Cookie
    response = make_response(R.success())
    # 设置两个Cookie

    # 1. 非HttpOnly Cookie，前端可读取
    response.set_cookie(
        "auth_status",
        "authenticated",
        max_age=7 * 24 * 60 * 60,  # 7天
        httponly=False,
        secure=settings.DEBUG is False,  # 生产环境使用HTTPS
        samesite="Strict"
    )

    # 2. HttpOnly Cookie，前端不可读取，用于API认证
    response.set_cookie(
        "auth_token",
        token,
        max_age=7 * 24 * 60 * 60,  # 7天
        httponly=True,
        secure=settings.DEBUG is False,
        samesite="Strict"
    )
    return response


@blueprint.route(URLs.user_logout.url, methods=URLs.user_logout.methods)
def logout():
    response = make_response(R.success())
    response.delete_cookie("auth_status")
    response.delete_cookie("auth_token")
    return response


@blueprint.route(URLs.user_info.url, methods=URLs.user_info.methods)
def user_info():
    data = request.get_json()
    user_id = int(data.get("userId"))
    res = user_manage_service.user_info(user_id)
    return R.success(data=res)
