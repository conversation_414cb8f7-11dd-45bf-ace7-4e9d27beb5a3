class BusinessError(Exception):
    def __init__(self, msg, code=None, data=None):
        super().__init__(msg)
        self.msg = msg
        self.code = code
        self.data = data or {}

    def with_data(self, data=None):
        """添加额外的数据"""
        self.data = data or {}
        return self


class ServerError(BusinessError):
    """服务器相关错误"""
    PERMISSION_DENIED = (1003, "权限不足")
    INVALID_PARAMS = (1004, "缺少请求参数或请求参数不合规")
    DOCUMENT_NOT_EXIST = (1006, "该条记录不存在")
    OPERATION_ERROR = (1007, "未知异常")
    CONNECTION_ERROR = (1008, "服务连接异常")
    GN_SERVER_ERROR = (1008, "GN服务连接异常")    
    VERIFICATION_CODE_ERROR = (1009, "邮箱验证码错误或已过期")
    USER_ALREADY_EXISTS = (1010, "用户已存在")
    TOKEN_MISSING = (1011, "缺少 token")
    TOKEN_EXPIRED = (1012, "token已过期")
    TOKEN_INVALID = (1013, "无效的token")


    def __init__(self, error_type, data=None):
        code, msg = error_type
        super().__init__(msg, code, data)


class UserError(BusinessError):
    """用户相关错误"""
    USERNAME_EMPTY = (1101, "用户名不能为空")
    USER_NOT_FOUND = (1102, "用户不存在")
    USER_EXISTS = (1106, "该邮箱已注册，请直接登录")
    WRONG_PASSWORD = (1109, "密码错误")
    PERMISSION_DENIED = (1111, "权限不足")
    ErrCaptchaNotExist = (1114, "验证码已失效")
    ErrCaptchaWrong = (1115, "验证码错误")
    USER_CREATE_ERROR = (1116, "用户注册失败")

    def __init__(self, error_type):
        code, msg = error_type
        super().__init__(msg, code)


class RoleError(BusinessError):
    """角色相关错误"""
    ROLE_EXISTS = (1203, "角色名称已存在")
    ROLE_IN_USE = (1204, "角色已被使用")
    ROLE_NOT_FOUND = (1205, "用户或角色不存在")

    def __init__(self, error_type):
        code, msg = error_type
        super().__init__(msg, code)


class CaptchaError(BusinessError):
    """验证码相关错误"""
    CAPTCHA_INVALID = (3001, "验证码已失效")
    CAPTCHA_WRONG = (3002, "验证码错误")

    def __init__(self, error_type):
        code, msg = error_type
        super().__init__(msg, code)


class LoginError(BusinessError):
    """登录相关错误"""
    TOKEN_GENERATE_FAILED = (5001, "生成token失败")
    WRONG_PASSWORD = (5004, "密码错误")
    LOGIN_FAILED = (5005, "登录失败，请检查用户名和密码")
    USER_NOT_FOUND = (5006, "用户不存在")
    USER_DISABLED = (5007, "用户状态不可用")
    CONFIG_ERROR = (9000, "配置文件未配置回调地址")

    def __init__(self, error_type):
        code, msg = error_type
        super().__init__(msg, code)


class VideoError(BusinessError):
    """视频相关错误"""
    INVALID_FILE_NAME = (2001, "不支持的文件类型")
    VIDEO_NOT_FOUND = (2002, "视频不存在")
    VIDEO_FILE_MISSING = (2003, "视频文件丢失")
    BP_FILE_MISSING = (2004, "视频骨骼点文件丢失")

    def __init__(self, error_type):
        code, msg = error_type
        super().__init__(msg, code)
