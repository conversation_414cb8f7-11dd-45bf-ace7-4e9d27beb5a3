<template>
  <div class="skeleton-player-with-bg">
    <div class="player-box">
      <div class="player-container" :style="{ width: `${width}px`, height: `${height}px` }">
        <div class="rc-box" :style="{
          width: `${contentBox.width}px`,
          height: `${contentBox.height}px`,
          top: `${contentBox.top}px`,
          left: `${contentBox.left}px`,
        }"></div>
        <video ref="videoRef" src="../demo.mp4" :width="width" :height="height" class="background-video"
          @loadedmetadata="onVideoLoaded"></video>

        <!-- 骨骼叠加层 -->
        <canvas ref="skeletonCanvas" :width="width" :height="height"
          class="skeleton-canvas skeleton-canvas-disabled"></canvas>

        <!-- 边界框叠加层 -->
        <canvas v-if="showBBox" ref="bboxCanvas" :width="width" :height="height" class="bbox-canvas"></canvas>
      </div>
    </div>

    <div class="controls">
      <div class="player-process">
        <div class="frame-info">
          <span class="play-cavans" @click="isPlaying ? pause() : play()">
            {{ isPlaying ? '暂停' : '播放' }}
          </span>
          <span>{{ currentFrame + 1 }}/{{ totalFrames }}</span>
        </div>
        <!-- 自定义进度条 -->
        <div class="custom-progress-bar" ref="progressBarRef" @click="handleProgressClick">
          <div class="progress-track">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
            <div class="progress-thumb" :style="{ left: progressPercentage + '%' }" @mousedown="handleThumbMouseDown">
            </div>
          </div>
        </div>
      </div>

      <div class="player-button">
        <div @click="previousFrame" class="prev-btn">
          <LeftOutlined />
        </div>
        <div class="play-input">
          <input min="1" :max="totalFrames" v-model="displayFrameNumber" type="number">
        </div>
        <div @click="nextFrame" class="next-btn">
          <RightOutlined />
        </div>
      </div>

      <div class="player-controls">
        <div class="point-watch" @click="showSkeleton = !showSkeleton">
          <img src="~/assets/images/2d.webp" alt="">
          <span>2D检测点</span>
        </div>
        <div class="rw-watch" @click="showBBox = !showBBox">
          <img src="~/assets/images/rw.webp" alt="">
          <span>人物检测框</span>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick, toRefs } from 'vue'
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import { useSkeletonAnimation } from '../hooks/useSkeletonAnimation'
import type { SkeletonFrame } from '../hooks/useSkeletonAnimation'
import { deepClone } from '~/utils'

// 获取 CSS 变量的实际值
const getCSSVariableValue = (variableName: string): string => {
  if (typeof window !== 'undefined') {
    const value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim()
    return value || '#000000' // 默认返回黑色
  }
  // 服务端渲染时的默认值
  const defaultColors: Record<string, string> = {
    '--color-skeleton-point-default': '#00ff00',
    '--color-skeleton-point-selected': '#ff0000',
    '--color-skeleton-point-red': '#ff0000',
    '--color-skeleton-point-green': '#00ff00',
    '--color-skeleton-line-default': '#ff00ff',
    '--color-skeleton-bbox-default': '#00ffff',
    '--color-white': '#ffffff'
  }
  return defaultColors[variableName] || '#000000'
}

// Props 定义
const props = defineProps({
  skeletonData: {
    type: Array as () => SkeletonFrame[],
    required: true,
  },
  mappings: {
    type: Object,
    required: true,
  },
  videoSrc: {
    type: String,
    default: './demo.mp4',
  },
  width: {
    type: Number,
    default: 960,
  },
  height: {
    type: Number,
    default: 540,
  },
  renderOptions: {
    type: Object,
    default: () => ({}),
  },
  frameRate: {
    type: Number,
    default: 30, // 默认帧率为30fps，必须与骨骼数据的采样率和视频帧率一致
  },
})

const { mappings } = toRefs(props)

const effectiveFrameRate = ref(props.frameRate)

// 监听props中的帧率变化
watch(
  () => props.frameRate,
  (newFrameRate) => {
    effectiveFrameRate.value = newFrameRate
  },
)

// Canvas 引用
const skeletonCanvas = ref<HTMLCanvasElement | null>(null)
const bboxCanvas = ref<HTMLCanvasElement | null>(null) // 边界框画布
const videoRef = ref<HTMLVideoElement | null>(null) // 视频实例
const progressBarRef = ref<HTMLDivElement | null>(null) // 进度条引用

const showBBox = ref(true) // 是否显示边界框
const showSkeleton = ref(true) // 是否显示骨骼关键点和连线

const canvasWidth = ref(props.width) // 画布宽度
const canvasHeight = ref(props.height) // 画布高度

const skeletonDataRef = computed(() => props.skeletonData) // 骨骼数据

// 监听props中的宽高变化
watch(
  () => props.width,
  (newWidth) => {
    canvasWidth.value = newWidth
  },
)

watch(
  () => props.height,
  (newHeight) => {
    canvasHeight.value = newHeight
  },
)

const {
  currentFrame, // 当前帧
  isPlaying, // 是否播放
  playbackSpeed, // 播放速度
  totalFrames, // 总帧数
  formattedKeypoints, // 格式化关键点
  formattedBoundingBoxes, // 格式化边界框
  scaleFactors, // 缩放因子
  play: playAnimation, // 播放
  pause: pauseAnimation, // 暂停
  nextFrame, // 下一帧
  previousFrame, // 上一帧
  goToFrame, // 跳转帧
  cleanup, // 清理
  currentSkeletonData, // 当前骨骼数据
} = useSkeletonAnimation(skeletonDataRef, canvasWidth, canvasHeight, effectiveFrameRate)

// 视频相关方法
const onVideoLoaded = () => {
  if (videoRef.value) {
    // 设置视频的播放速度
    videoRef.value.playbackRate = playbackSpeed.value
  }
}

// 播放方法 - 同时控制动画和视频
const play = () => {
  if (videoRef.value) {
    videoRef.value.play()
  }
  playAnimation()
}

// 暂停方法 - 同时控制动画和视频
const pause = () => {
  if (videoRef.value) {
    videoRef.value.pause()
  }
  pauseAnimation()
}

// 监听播放速度变化，同步更新视频播放速度
watch(playbackSpeed, (newSpeed) => {
  if (videoRef.value) {
    videoRef.value.playbackRate = newSpeed
  }
})

// 计算内容框的位置和尺寸，显示实际渲染区域
const contentBox = computed(() => {
  if (!currentSkeletonData.value) return { width: 0, height: 0, top: 0, left: 0 }

  const { xOffset, yOffset } = scaleFactors.value

  // 使用 scaleFactors 计算的偏移值来确定内容框位置
  const renderWidth = canvasWidth.value - (xOffset * 2)
  const renderHeight = canvasHeight.value - (yOffset * 2)

  return {
    width: renderWidth,
    height: renderHeight,
    top: yOffset,
    left: xOffset,
  }
})

// 渲染选项
const renderOptions = computed(() => {
  return {
    ...props.renderOptions,
    pointColor: props.renderOptions.pointColor || getCSSVariableValue('--color-skeleton-point-default'), // 关键点颜色
    lineColor: props.renderOptions.lineColor || getCSSVariableValue('--color-skeleton-line-default'), // 线条颜色
    pointSize: props.renderOptions.pointSize || 5, // 关键点大小
    lineWidth: props.renderOptions.lineWidth || 2, // 线条宽度
    bboxColor: props.renderOptions.bboxColor || getCSSVariableValue('--color-skeleton-bbox-default'), // 边界框颜色
    bboxLineWidth: props.renderOptions.bboxLineWidth || 2, // 边界框宽度
    offsetX: props.renderOptions.offsetX || 0, // 偏移x
    offsetY: props.renderOptions.offsetY || 0, // 偏移y
  }
})

// 显示用的帧数（从1开始，而不是从0开始）
const displayFrameNumber = computed({
  get: () => currentFrame.value + 1,
  set: (value) => {
    const frameIndex = Math.max(1, Math.min(totalFrames.value, Number(value))) - 1
    goToFrame(frameIndex)
  },
})

// 进度条百分比
const progressPercentage = computed(() => {
  if (totalFrames.value === 0) return 0
  return (currentFrame.value / (totalFrames.value - 1)) * 100
})

// 拖拽状态
const isDragging = ref(false)



// 使用动态节点分组定义
const NODE_GROUPS = computed(() => mappings.value.NODE_GROUPS_NEW);

// IK模式下要显示的脚部节点索引（左脚+右脚）
const ikFootNodes = computed(() => [
  ...(NODE_GROUPS.value.leftFoot || []),
  ...(NODE_GROUPS.value.rightFoot || [])
]);

// 需要排除连线的节点范围 - 包含左脚、右脚、左臂、右臂
const excludedNodes = computed(() => [
  ...(NODE_GROUPS.value.leftFoot || []),
  ...(NODE_GROUPS.value.rightFoot || []),
  ...(NODE_GROUPS.value.leftArm || []),
  ...(NODE_GROUPS.value.rightArm || [])
]);

// 高度阈值：超过最低点多少像素时变红色
const heightThreshold = ref(10);

// 存储每个点在每一帧的颜色状态
// key格式: `${frameIndex}_${personIndex}_${pointIndex}`
// value: true=红色, false=绿色
const pointColorStates = ref<Map<string, boolean>>(new Map());

// 原始数据备份（用于重置功能）
const originalSkeletonData = ref<SkeletonFrame[]>([]);

// 左脚和右脚节点定义（使用动态配置）
const leftFootNodes = computed(() => NODE_GROUPS.value.leftFoot || []);
const rightFootNodes = computed(() => NODE_GROUPS.value.rightFoot || []);

// 计算左脚最大Y坐标（最低点）
const leftFootMaxY = computed(() => {
  if (!formattedKeypoints.value.length) return -Infinity;

  let maxY = -Infinity;

  formattedKeypoints.value.forEach((person) => {
    leftFootNodes.value.forEach((pointIndex: number) => {
      if (pointIndex < person.length) {
        const point = person[pointIndex];
        // 去掉score检查，处理所有点
        maxY = Math.max(maxY, point.y);
      }
    });
  });

  return maxY === -Infinity ? 0 : maxY;
});

// 计算右脚最大Y坐标（最低点）
const rightFootMaxY = computed(() => {
  if (!formattedKeypoints.value.length) return -Infinity;

  let maxY = -Infinity;

  formattedKeypoints.value.forEach((person) => {
    rightFootNodes.value.forEach((pointIndex: number) => {
      if (pointIndex < person.length) {
        const point = person[pointIndex];
        // 去掉score检查，处理所有点
        maxY = Math.max(maxY, point.y);
      }
    });
  });

  return maxY === -Infinity ? 0 : maxY;
});

// 保留原有的 lowestPointY（返回左右脚中的最大Y值）
const lowestPointY = computed(() => {
  return Math.max(leftFootMaxY.value, rightFootMaxY.value);
});

// 进度条点击事件
const handleProgressClick = (event: MouseEvent) => {
  if (!progressBarRef.value || isDragging.value) return

  const rect = progressBarRef.value.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (clickX / rect.width) * 100))
  const targetFrame = Math.round((percentage / 100) * (totalFrames.value - 1))

  goToFrame(targetFrame)
}

// 滑块拖拽开始
const handleThumbMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragging.value = true

  const handleMouseMove = (moveEvent: MouseEvent) => {
    if (!progressBarRef.value || !isDragging.value) return

    const rect = progressBarRef.value.getBoundingClientRect()
    const moveX = moveEvent.clientX - rect.left
    const percentage = Math.max(0, Math.min(100, (moveX / rect.width) * 100))
    const targetFrame = Math.round((percentage / 100) * (totalFrames.value - 1))

    goToFrame(targetFrame)
  }

  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 渲染骨骼
const renderSkeleton = () => {
  if (!skeletonCanvas.value) return;

  const ctx = skeletonCanvas.value.getContext('2d');
  if (!ctx) return;

  // 清除画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);

  // 如果不显示骨骼，直接返回（只清除画布）
  if (!showSkeleton.value) return;

  const options = renderOptions.value;
  const offsetX = options.offsetX;
  const offsetY = options.offsetY;

  // 绘制骨架线条 - 保留腿部、脊椎、头部连线，排除脚部和手臂连线
  if (formattedKeypoints.value.length > 0) {
    formattedKeypoints.value.forEach((item, personIndex) => {
      // 绘制线条，排除脚部和手臂之间的连线（保留腿部、脊椎、头部连线）
      mappings.value.SKELETON_LINKS.forEach(([i, j]: [number, number]) => {
        if (i >= item.length || j >= item.length) return;


        if (excludedNodes.value.includes(i) && excludedNodes.value.includes(j)) {
          return;
        }

        // 排除脊椎到锁骨的连线 (spine_05 -> clavicle_l/clavicle_r)
        const clavicleLeftIndex = mappings.value.JOINT_NAME_TO_INDEX['clavicle_l'];
        const clavicleRightIndex = mappings.value.JOINT_NAME_TO_INDEX['clavicle_r'];
        const spine05Index = mappings.value.JOINT_NAME_TO_INDEX['spine_05'];

        if (spine05Index !== undefined &&
          ((i === spine05Index && (j === clavicleLeftIndex || j === clavicleRightIndex)) ||
            (j === spine05Index && (i === clavicleLeftIndex || i === clavicleRightIndex)))) {
          return;
        }

        const point1 = item[i];
        const point2 = item[j];

        ctx.beginPath();
        ctx.moveTo(point1.x + offsetX, point1.y + offsetY);
        ctx.lineTo(point2.x + offsetX, point2.y + offsetY);
        ctx.strokeStyle = options.lineColor;
        ctx.lineWidth = options.lineWidth;
        ctx.stroke();
      });

      // 绘制关键点 - 只绘制左右脚部位的节点
      item.forEach((value, pointIndex) => {
        if (ikFootNodes.value.includes(pointIndex)) {
          ctx.beginPath();

          // 正常点的大小和颜色
          let pointSize = 10; // 脚部节点固定大小为10
          let pointColor = options.pointColor; // 默认绿色

          // 检查是否有存储的颜色状态
          const frameIndex = currentFrame.value;
          const stateKey = getPointStateKey(frameIndex, personIndex, pointIndex);
          const storedState = pointColorStates.value.get(stateKey);

          if (storedState !== undefined) {
            // 使用存储的状态
            pointColor = storedState ? getCSSVariableValue('--color-skeleton-point-red') : getCSSVariableValue('--color-skeleton-point-green');
          } else {
            // 使用默认的Y坐标计算逻辑
            pointColor = getDefaultPointColor(pointIndex, personIndex);
          }


          // 绘制正方形小方块（脚部节点）
          const halfSize = pointSize / 2;
          ctx.rect(value.x + offsetX - halfSize, value.y + offsetY - halfSize, pointSize, pointSize);
          ctx.fillStyle = pointColor;
          ctx.fill();
        }
      });
    });
  }
}

// 渲染边界框
const renderBoundingBoxes = () => {
  if (!bboxCanvas.value || !showBBox.value) return

  const ctx = bboxCanvas.value.getContext('2d')
  if (!ctx) return

  // 清除画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

  const options = renderOptions.value
  const offsetX = options.offsetX
  const offsetY = options.offsetY

  // 绘制边界框
  if (formattedBoundingBoxes.value.length > 0) {
    formattedBoundingBoxes.value.forEach((box) => {
      ctx.beginPath()
      ctx.rect(box.x + offsetX, box.y + offsetY, box.width, box.height)
      ctx.strokeStyle = options.bboxColor
      ctx.lineWidth = options.bboxLineWidth
      ctx.stroke()
    })
  }
}

// 更新所有渲染内容
const updateRendering = () => {
  renderSkeleton()
  renderBoundingBoxes()
}

const clearSelection = () => {
  updateRendering();
}

// 获取点的状态key
const getPointStateKey = (frameIndex: number, personIndex: number, pointIndex: number) => {
  return `${frameIndex}_${personIndex}_${pointIndex}`;
}

// 切换指定点的颜色状态
const togglePointColor = (pointIndex: number) => {
  const frameIndex = currentFrame.value;
  const personIndex = 0;

  const key = getPointStateKey(frameIndex, personIndex, pointIndex);
  const currentState = pointColorStates.value.get(key);

  // 如果没有状态，先计算默认状态
  if (currentState === undefined) {
    const redColor = getCSSVariableValue('--color-skeleton-point-red');
    const defaultIsRed = getDefaultPointColor(pointIndex, personIndex) === redColor;
    pointColorStates.value.set(key, !defaultIsRed);
  } else {
    // 切换状态
    pointColorStates.value.set(key, !currentState);
  }

  updateRendering();
}

// 获取点的默认颜色（基于Y坐标计算）
const getDefaultPointColor = (pointIndex: number, personIndex: number) => {
  const greenColor = getCSSVariableValue('--color-skeleton-point-green');
  const redColor = getCSSVariableValue('--color-skeleton-point-red');

  if (!formattedKeypoints.value.length || !formattedKeypoints.value[personIndex]) {
    return greenColor;
  }

  const point = formattedKeypoints.value[personIndex][pointIndex];
  if (!point) {
    return greenColor;
  }

  let isLeftFoot = leftFootNodes.value.includes(pointIndex);
  let isRightFoot = rightFootNodes.value.includes(pointIndex);

  if (isLeftFoot) {
    const leftMaxY = leftFootMaxY.value;
    if (leftMaxY !== -Infinity) {
      if (point.y === leftMaxY) {
        return redColor;
      } else if (point.y + heightThreshold.value <= leftMaxY) {
        return greenColor;
      } else {
        return redColor;
      }
    }
  } else if (isRightFoot) {
    const rightMaxY = rightFootMaxY.value;
    if (rightMaxY !== -Infinity) {
      if (point.y === rightMaxY) {
        return redColor;
      } else if (point.y + heightThreshold.value <= rightMaxY) {
        return greenColor;
      } else {
        return redColor;
      }
    }
  }

  return greenColor;
}

// 获取点的当前颜色状态（用于按钮显示）
const getPointCurrentColor = (pointIndex: number) => {
  const frameIndex = currentFrame.value;
  const personIndex = 0;
  const stateKey = getPointStateKey(frameIndex, personIndex, pointIndex);
  const storedState = pointColorStates.value.get(stateKey);

  if (storedState !== undefined) {
    return storedState ? 'red' : 'green';
  } else {
    const defaultColor = getDefaultPointColor(pointIndex, personIndex);
    const redColor = getCSSVariableValue('--color-skeleton-point-red');
    return defaultColor === redColor ? 'red' : 'green';
  }
}

// 获取当前帧所有脚部点的详细信息
const getCurrentFrameFootPointsInfo = () => {
  const frameIndex = currentFrame.value;


  const leftFootPoints = leftFootNodes.value.map((pointIndex: number) => ({
    id: pointIndex,
    color: getPointCurrentColor(pointIndex),
    isRed: getPointCurrentColor(pointIndex) === 'red'
  }));

  const rightFootPoints = rightFootNodes.value.map((pointIndex: number) => ({
    id: pointIndex,
    color: getPointCurrentColor(pointIndex),
    isRed: getPointCurrentColor(pointIndex) === 'red'
  }));

  return {
    frameIndex,
    leftFoot: leftFootPoints,
    rightFoot: rightFootPoints,
    allPoints: [...leftFootPoints, ...rightFootPoints]
  };
}

// 根据脚部位置和索引获取对应的pointIndex
const getPointIndexByFootPosition = (foot: 'left' | 'right', index: 0 | 1) => {
  if (foot === 'left') {
    return leftFootNodes.value[index];
  } else {
    return rightFootNodes.value[index];
  }
}

// 检测当前帧是否有骨骼数据
const hasCurrentFrameData = computed(() => {
  return (currentSkeletonData.value?.instance_info[0]?.bbox_score ?? 0) > 0;
});

// 创建原始数据备份
const createBackup = () => {
  originalSkeletonData.value = deepClone(props.skeletonData);
};

// 重置当前帧到初始状态
const resetCurrentFrame = () => {
  if (!originalSkeletonData.value.length || !currentSkeletonData.value) {
    console.log('没有可用的备份数据或当前帧数据');
    return;
  }

  const currentFrameIndex = currentFrame.value;
  if (currentFrameIndex >= originalSkeletonData.value.length) {
    console.log('当前帧索引超出备份数据范围');
    return;
  }

  // 从备份数据中恢复当前帧
  const originalFrame = originalSkeletonData.value[currentFrameIndex];
  const currentFrame_data = currentSkeletonData.value;

  // 逐个恢复关键点数据，保持响应式
  originalFrame.instance_info.forEach((originalInstance, instanceIndex) => {
    if (currentFrame_data.instance_info[instanceIndex]) {
      originalInstance.keypoints.forEach((originalKeypoint, keypointIndex) => {
        if (currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex]) {
          currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex][0] = originalKeypoint[0];
          currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex][1] = originalKeypoint[1];
        }
      });

      // 恢复边界框数据
      if (originalInstance.bbox && currentFrame_data.instance_info[instanceIndex].bbox) {
        currentFrame_data.instance_info[instanceIndex].bbox = deepClone(originalInstance.bbox);
      }
      if (originalInstance.bbox_score !== undefined) {
        currentFrame_data.instance_info[instanceIndex].bbox_score = originalInstance.bbox_score;
      }
    }
  });

  console.log('重置当前帧:', currentFrameIndex);

  // 强制触发响应式更新
  nextTick(() => {
    updateRendering();
  });
};

// 重置所有帧到初始状态 - 完全重新初始化组件
const resetAllFrames = () => {
  if (!originalSkeletonData.value.length) {
    console.log('没有可用的备份数据');
    return;
  }
  pause();

  // 恢复骨骼数据
  const currentData = skeletonDataRef.value;

  // 逐帧恢复数据，保持响应式
  for (let frameIndex = 0; frameIndex < Math.min(currentData.length, originalSkeletonData.value.length); frameIndex++) {
    const originalFrame = originalSkeletonData.value[frameIndex];
    const currentFrame_data = currentData[frameIndex];

    // 逐个恢复关键点数据
    originalFrame.instance_info.forEach((originalInstance, instanceIndex) => {
      if (currentFrame_data.instance_info[instanceIndex]) {
        originalInstance.keypoints.forEach((originalKeypoint, keypointIndex) => {
          if (currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex]) {
            currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex][0] = originalKeypoint[0];
            currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex][1] = originalKeypoint[1];
          }
        });

        // 恢复边界框数据
        if (originalInstance.bbox && currentFrame_data.instance_info[instanceIndex].bbox) {
          currentFrame_data.instance_info[instanceIndex].bbox = deepClone(originalInstance.bbox);
        }
        if (originalInstance.bbox_score !== undefined) {
          currentFrame_data.instance_info[instanceIndex].bbox_score = originalInstance.bbox_score;
        }
      }
    });
  }

  // 重置组件状态
  isDragging.value = false;

  //  重置视频状态
  goToFrame(0); // 跳转到第一帧

  //  重置视频播放状态
  if (videoRef.value) {
    videoRef.value.currentTime = 0;
    videoRef.value.pause();
  }

  nextTick(() => {
    createBackup();
    updateRendering();
    console.log('重置所有帧完成，组件已完全重新初始化');
  });
};

// 监听帧变化
watch(currentFrame, () => {
  updateRendering()
})

// 监听骨骼显示状态变化
watch(showSkeleton, (newValue) => {
  console.log('骨骼显示状态变更为:', newValue)
  if (!newValue && skeletonCanvas.value) {
    // 如果不显示骨骼，清除骨骼画布
    const ctx = skeletonCanvas.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)
    }
  } else {
    // 如果显示骨骼，重新渲染
    renderSkeleton()
  }
})

// 监听边界框显示状态变化
watch(showBBox, (newValue) => {
  console.log('边界框显示状态变更为:', newValue)
  if (!newValue && bboxCanvas.value) {
    // 如果不显示边界框，清除边界框画布
    const ctx = bboxCanvas.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)
    }
  }
})

watch(bboxCanvas, (canvas) => {
  if (canvas && showBBox.value) {
    nextTick(() => {
      renderBoundingBoxes()
    })
  }
})

// 监听数据变化
watch(
  [formattedKeypoints, formattedBoundingBoxes],
  () => {
    updateRendering()
  },
  { deep: true },
)

// 同步视频和骨骼动画的帧
watch(currentFrame, (frame) => {
  if (videoRef.value && videoRef.value.duration) {
    // 计算视频应该跳转到的时间点
    const timeInSeconds = frame / effectiveFrameRate.value

    // 如果时间点在视频范围内，则跳转
    if (timeInSeconds <= videoRef.value.duration) {
      videoRef.value.currentTime = timeInSeconds
    }
  }
})

onMounted(() => {
  updateRendering()
  if (props.skeletonData && props.skeletonData.length > 0) {
    createBackup();
  }
})

onBeforeUnmount(() => {
  cleanup()
})

// 恢复脚部IK状态
const restoreFootIKStates = (states: Record<string, any>) => {
  try {
    if (states && typeof states === 'object') {
      // 恢复点颜色状态
      if (states.pointColorStates && states.pointColorStates instanceof Map) {
        pointColorStates.value = new Map(states.pointColorStates)
      } else if (states.pointColorStates && Array.isArray(states.pointColorStates)) {
        // 处理序列化后的Map数据
        pointColorStates.value = new Map(states.pointColorStates)
      }

      console.log('已恢复IK模式状态:', pointColorStates.value.size, '个点状态')
    }
  } catch (error) {
    console.error('恢复IK状态失败:', error)
  }
}

// 获取脚部IK状态
const getFootIKStates = () => {
  return {
    pointColorStates: Array.from(pointColorStates.value.entries()) // 转换为可序列化的数组
  }
}

// 获取完整状态（用于状态缓存）
const getFullState = () => {
  return {
    currentFrame: currentFrame.value,
    isPlaying: isPlaying.value,
    playbackSpeed: playbackSpeed.value,
    videoCurrentTime: videoRef.value?.currentTime || 0,
    footIKStates: getFootIKStates()
  }
}

defineExpose({
  currentFrame,
  totalFrames,
  play,
  pause,
  nextFrame,
  previousFrame,
  goToFrame,
  clearSelection,
  resetCurrentFrame,
  resetAllFrames,
  hasCurrentFrameData, // 暴露当前帧数据检测状态
  ikFootNodes, // 暴露当前显示的脚部节点
  excludedNodes, // 暴露排除连线的节点范围（脚部+手臂）
  heightThreshold, // 暴露高度阈值，允许外部调整
  lowestPointY, // 暴露当前帧最低点Y坐标（只读）
  leftFootMaxY, // 暴露左脚最大Y坐标（只读）
  rightFootMaxY, // 暴露右脚最大Y坐标（只读）
  leftFootNodes, // 暴露左脚节点定义
  rightFootNodes, // 暴露右脚节点定义
  togglePointColor, // 暴露点颜色切换函数
  getPointCurrentColor, // 暴露获取点当前颜色函数
  pointColorStates, // 暴露点颜色状态存储
  getCurrentFrameFootPointsInfo, // 暴露获取当前帧脚部点信息函数
  getPointIndexByFootPosition, // 暴露根据脚部位置获取pointIndex函数
  // 状态管理相关方法
  restoreFootIKStates,
  getFootIKStates,
  getFullState,
  isPlaying,
  playbackSpeed,
  videoRef,
});

</script>

<style scoped lang="scss">
.rc-box {
  position: absolute;
  border: 1px solid red;
  z-index: 100;
  pointer-events: none;
}

.skeleton-player-with-bg {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  user-select: none;
}

.player-box {
  flex: 1;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.player-container {
  position: relative;
  overflow: hidden;
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.skeleton-canvas,
.bbox-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.skeleton-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  cursor: default;

  &:hover {
    cursor: grab;
  }

  &:active {
    cursor: grabbing;
  }

  &.skeleton-canvas-disabled {
    pointer-events: none;
    cursor: default;

    &:hover {
      cursor: default;
    }

    &:active {
      cursor: default;
    }
  }
}

.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: rem(76);
  padding-left: rem(28);

  .player-process {
    display: flex;
    align-items: center;
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: 400;
    font-size: rem(18);
    color: #FFFFFF;
    line-height: rem(26);
    text-align: right;
    font-style: normal;
    height: rem(76);

    .frame-info {
      width: rem(130);
      height: 100%;
      margin-right: rem(12);
      display: flex;
      align-items: center;

      span {
        &:first-child {
          width: rem(45);
          margin-right: rem(5);
          text-align: left;
        }

        &:last-child {
          flex: 1;
          text-align: left;
        }
      }
    }

    // 自定义进度条样式
    .custom-progress-bar {
      width: rem(200);
      height: rem(12);
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;

      .progress-track {
        width: 100%;
        height: rem(12);
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: rem(10);
        position: relative;
      }

      .progress-fill {
        height: 100%;
        background-color: #ffffff;
        border-radius: rem(10);
        transition: width 0.1s ease;
      }

      .progress-thumb {
        position: absolute;
        top: 50%;
        width: rem(24);
        height: rem(24);
        background-color: #ffffff;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        cursor: grab;
        box-shadow: 0 0 rem(16) 0 rgba(255, 255, 255, 0.7);
        transition: left 0.1s ease;

        &:hover {
          transform: translate(-50%, -50%) scale(1.1);
        }

        &:active {
          cursor: grabbing;
          transform: translate(-50%, -50%) scale(1.2);
        }
      }
    }
  }

  .play-cavans {
    cursor: pointer;
  }

  .player-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: rem(76);
    width: rem(160);

    .prev-btn,
    .next-btn {
      font-size: rem(20);
      color: #fff;
      cursor: pointer;
      flex: 1;
    }

    .prev-btn {
      text-align: right;
      padding-right: rem(15);
    }

    .next-btn {
      text-align: left;
      padding-left: rem(15);
    }

    .play-input {
      width: rem(60);
      height: rem(32);

      input {
        width: 100%;
        height: 100%;
        text-align: center;
        border-radius: rem(2);
        background-color: rgba(61, 60, 70, 1);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: rem(18);
        color: #FFFFFF;
        line-height: rem(26);
        font-style: normal;
        text-align: center;
      }
    }
  }

  .player-controls {
    display: flex;
    align-items: center;
    height: rem(76);
    padding-right: rem(28);

    .point-watch,
    .rw-watch {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: rem(24);

      span {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(18);
        color: #FFFFFF;
        line-height: rem(26);
        text-align: right;
        font-style: normal;
      }
    }

    .rw-watch {
      margin-right: 0;
    }

    img {
      width: rem(20);
      height: rem(20);
      margin-right: rem(3);
    }
  }

  .point-color-controls {
    display: flex;
    gap: rem(8);
    margin-left: rem(24);

    .color-btn {
      padding: rem(6) rem(12);
      border: 1px solid #fff;
      background-color: transparent;
      color: #fff;
      border-radius: rem(4);
      cursor: pointer;
      font-size: rem(14);
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      &:active {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.skeleton-selection-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: rem(20) 0;

  .selection-group {
    display: flex;
    gap: rem(10);
    align-items: center;

    .skeleton-btn {
      padding: rem(8) rem(16);
      border: 1px solid #ccc;
      background-color: #f5f5f5;
      color: #333;
      border-radius: rem(4);
      cursor: pointer;
      font-size: rem(14);
      transition: all 0.3s ease;

      &:hover {
        background-color: #e0e0e0;
        border-color: #999;
      }

      &.clear-btn {
        background-color: #ff6b6b;
        color: white;
        border-color: #ff5252;

        &:hover {
          background-color: #ff5252;
        }
      }

      &.reset-btn {
        background-color: #4CAF50;
        color: white;
        border-color: #45a049;

        &:hover {
          background-color: #45a049;
        }
      }

      .iconfont {
        margin-right: rem(4);
      }
    }
  }
}
</style>
