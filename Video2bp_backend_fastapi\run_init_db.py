#!/usr/bin/env python3
"""
数据库初始化运行脚本
使用方法：python run_init_db.py
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from db.init_database import main

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 视频动捕项目 - 数据库初始化工具")
    print("=" * 60)
    
    try:
        asyncio.run(main())
        print("\n" + "=" * 60)
        print("🎉 数据库初始化完成！")
        print("=" * 60)
        
        print("\n📋 接下来你可以：")
        print("1. 启动FastAPI服务：uvicorn main:app --reload")
        print("2. 访问API文档：http://127.0.0.1:8000/docs")
        print("3. 开始开发和测试API接口")
        
    except Exception as e:
        print(f"\n❌ 初始化失败：{e}")
        print("\n🔧 请检查：")
        print("1. 数据库连接配置是否正确")
        print("2. 数据库服务是否正在运行")
        print("3. 数据库用户是否有创建表的权限")
        sys.exit(1)
