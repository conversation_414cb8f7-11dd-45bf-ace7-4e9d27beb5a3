from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl
from schemas.base import BaseSchema
from schemas.response import StandardResponse


class ProjectListRequest(BaseSchema):
    """项目列表请求"""
    pageNum: int = Field(1, description="页码，从1开始")
    pageSize: int = Field(8, description="每页数量")


class Project(BaseSchema):
    """项目模型"""
    id: int = Field(..., description="项目ID")
    name: str = Field(..., description="项目名称")
    image: HttpUrl = Field(..., description="项目图片URL")
    fbxUrl: HttpUrl = Field(..., description="FBX模型文件URL")
    videoUrl: HttpUrl = Field(..., description="项目视频URL")
    createdAt: str = Field(..., description="创建时间")
    updatedAt: str = Field(..., description="更新时间")
    status: str = Field(..., description="项目状态")


class ProjectListResponse(BaseSchema):
    """项目列表响应数据"""
    data: List[Project] = Field(..., description="项目列表")
    total: int = Field(..., description="总数量")
    pageNum: int = Field(..., description="当前页码")
    pageSize: int = Field(..., description="每页数量")


class ProjectListResponseModel(StandardResponse):
    """项目列表响应模型"""
    data: Optional[ProjectListResponse] = None
