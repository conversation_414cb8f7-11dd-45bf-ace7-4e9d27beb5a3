export interface UserInfo {
	id: string;
	username: string;
	email: string;
	role: string;
	avatar?: string;
	createdAt: string;
	updatedAt: string;
	balance: number;
	vipLevel: number;
	vipDeadline: string;
}

// 修改密码请求参数
export interface ChangePasswordParams {
	currentPassword: string;
	newPassword: string;
}

// 通用用户操作响应数据
export interface UserOperationResponse {
	code: number;
	message: string;
	data?: {
		status: boolean;
	};
}

// 修改密码响应数据（使用通用响应格式）
export interface ChangePasswordResponse extends UserOperationResponse {}

// 用户订单记录项
export interface RecordItem {
	id: string;
	type: string;
	time: string;
	amount: string;
	method: string;
	toAccount: string;
}

// 获取订单记录请求参数
export interface OrderRecordsParams {
	pageNum: number;
	pageSize: number;
}

// 订单记录列表数据
export interface OrderRecordsData {
	data: RecordItem[];
	total: number;
	pageNum: number;
	pageSize: number;
}

// 获取订单记录响应
export interface OrderRecordsResponse {
	code: number;
	message: string;
	data: OrderRecordsData;
}
