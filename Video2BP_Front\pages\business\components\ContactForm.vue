<template>
  <div class="contact-form">
    <div class="contact-content">
      <div class="contact-left">
        <div class="title">进一步了解产品及解决方案</div>
        <div class="subtitle">联系我们，匹配需求</div>
        <div class="contact-info">
          <div class="info-item">
            <span class="label">电话：</span>
            <span class="value">19113545780</span>
          </div>
          <div class="info-item">
            <span class="label">邮箱：</span>
            <span class="value"><EMAIL></span>
          </div>
        </div>
      </div>
      <div class="contact-right">
        <div class="form-group">
          <input type="text" class="form-input" placeholder="请输入手机号码" v-model="formData.phone" :disabled="loading" />
        </div>
        <div class="form-group">
          <input type="text" class="form-input" placeholder="请输入公司名称" v-model="formData.companyName"
            :disabled="loading" />
        </div>
        <div class="form-group">
          <input type="text" class="form-input" placeholder="请输入咨询项目" v-model="formData.project" :disabled="loading" />
        </div>
        <div class="form-group">
          <button class="submit-btn" @click="submitForm" :disabled="loading">
            {{ loading ? '提交中...' : '免费体验' }}
          </button>
        </div>
        <div v-if="error" class="error-message">{{ error }}</div>
        <div v-if="success" class="success-message">{{ success }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { ContactFormParams } from '~/types/business'
import { useApi } from '~/composables/useApi'

defineOptions({
  name: 'ContactForm',
})

const { business } = useApi()

// 表单数据
const formData = reactive<ContactFormParams>({
  phone: '',
  companyName: '',
  project: ''
})

// 状态管理
const loading = ref(false)
const error = ref<string>('')
const success = ref<string>('')

// 提交表单
const submitForm = async () => {
  try {
    // 简单验证
    if (!formData.phone.trim()) {
      message.warning('请输入手机号码')
      return
    }
    if (!formData.companyName.trim()) {
      message.warning('请输入公司名称')
      return
    }
    if (!formData.project.trim()) {
      message.warning('请输入咨询项目')
      return
    }

    loading.value = true

    // 调用API
    const result = await business?.submitContactForm({
      phone: formData.phone.trim(),
      companyName: formData.companyName.trim(),
      project: formData.project.trim()
    })

    if (!result) {
      throw new Error('提交失败')
    }

    message.success('提交成功！我们会尽快联系您。')

    // 清空表单
    formData.phone = ''
    formData.companyName = ''
    formData.project = ''

  } catch (err) {
    console.error('联系表单提交失败:', err)
    error.value = err as string;
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.contact-form {
  width: 100%;
  background-image: url('~/assets/images/business-contract.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: rem(80) 0;
  position: relative;
  overflow: hidden;

  .contact-content {
    width: 83.85%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .contact-left {
      flex: 1;
      padding-right: rem(60);

      .title {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: bold;
        font-size: rem(60);
        color: #ffffff;
        line-height: rem(104);
        text-align: left;
        font-style: normal;
      }

      .subtitle {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: bold;
        font-size: rem(60);
        color: #ffffff;
        line-height: rem(86);
        text-align: left;
        font-style: normal;
        position: relative;

        &::before {
          content: '';
          width: rem(560);
          height: rem(4);
          position: absolute;
          bottom: rem(-22);
          left: 0;
          background: linear-gradient(270deg,
              rgba(255, 255, 255, 0) 0%,
              #ffffff 50%,
              rgba(255, 255, 255, 0) 100%);
          //   filter: blur(rem(3));
        }
      }

      .contact-info {
        margin-top: rem(60);
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(40);
        color: rgba(255, 255, 255, 0.8);
        line-height: rem(72);
        text-align: left;
        font-style: normal;
      }
    }

    .contact-right {
      width: rem(440);
      padding-top: rem(30);

      .form-group {
        margin-bottom: rem(16);

        .form-input {
          width: 100%;
          height: rem(72);
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: rem(4);
          padding: 0 rem(24);
          color: #ffffff;
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: 400;
          font-size: rem(24);
          color: #ffffff;
          line-height: rem(32);
          text-align: left;
          font-style: normal;

          &::placeholder {
            color: rgba(255, 255, 255, 0.6);
          }

          &:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.1);
          }
        }

        .submit-btn {
          width: 100%;
          height: rem(72);
          background: #0099ff;
          border-radius: rem(4);
          border: none;
          cursor: pointer;
          transition: background 0.3s;
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: bold;
          font-size: rem(24);
          color: #ffffff;
          line-height: rem(32);
          text-align: center;
          font-style: normal;

          &:hover:not(:disabled) {
            background: #00aaff;
          }

          &:disabled {
            background: #666;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}
</style>
