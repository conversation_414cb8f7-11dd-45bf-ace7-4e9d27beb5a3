from flask import Blueprint, request

from service import home_manage_service
from urls import URLs
from utils.R import R

blueprint = Blueprint("home_manage", __name__)


@blueprint.route(URLs.compony_info.url, methods=URLs.compony_info.methods)
def compony_info():
    res = home_manage_service.get_compony_info()
    return R.success(data=res)


@blueprint.route(URLs.combo_info.url, methods=URLs.combo_info.methods)
def combo_info():
    res = home_manage_service.get_combo_info()
    return R.success(data=res)


@blueprint.route(URLs.news_list.url, methods=URLs.news_list.methods)
def news_list():
    j_data = request.get_json()
    page = j_data.get("page", 1)
    size = j_data.get("size", 9)
    news_type = j_data.get("type")

    res = home_manage_service.get_news_list(page=page, size=size, news_type=news_type)
    return R.success(data=res)


@blueprint.route(URLs.news_detail.url, methods=URLs.news_detail.methods)
def news_detail():
    a_data = request.args
    news_id = a_data.get("id")

    res = home_manage_service.get_news_detail(news_id=news_id)
    return R.success(data=res)