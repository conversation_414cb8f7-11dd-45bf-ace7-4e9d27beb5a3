<template>
  <div class="auth-container">
    <!-- <OptimizedImage src="/login-bg.webp" :is-background="true" :is-critical="true" alt="login-bg" class="login-bg"> -->
    <div class="auth-banner">
      <div class="hb-title">AI视频动捕</div>
      <div class="hb-intro">AI视频动捕，捕捉每一次精彩镜头</div>
    </div>
    <div class="auth-main">
      <LoginView v-if="isLoginView" @switchToRegister="showRegister" />
      <RegisterView v-else @switchToLogin="showLogin" @registrationComplete="handleRegistrationComplete" />
    </div>
    <!-- </OptimizedImage> -->
  </div>
</template>

<script setup lang="ts">
const LoginView = defineAsyncComponent(() => import('./LoginView.vue'))
const RegisterView = defineAsyncComponent(() => import('./RegisterView.vue'))

definePageMeta({
  layout: 'default',
  layoutconfig: {
    headerVisible: false,
    footerVisible: false,
  },
})

const isLoginView = ref(true)

const showRegister = () => {
  isLoginView.value = false
}

const showLogin = () => {
  isLoginView.value = true
}

const handleRegistrationComplete = () => {
  isLoginView.value = true
}


</script>

<style scoped lang="scss">
.auth-container {
  display: flex;
  align-items: center;
  height: 100vh;
  overflow: hidden;
  background-image: url('~/assets/images/login-bg.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

// .login-bg {
//   position: absolute;
//   left: 0;
//   top: 0;
//   right: 0;
//   bottom: 0;
//   z-index: 0
// }

.auth-main {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: rem(100);
}

.auth-banner {
  position: absolute;
  left: rem(180);
  top: 50%;
  transform: translateY(-50%);
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: bold;
  font-style: normal;

  .hb-title {
    font-size: rem(72);
    color: #eaa1ff;
    line-height: rem(104);
    margin-bottom: rem(25);
  }

  .hb-intro {
    font-size: rem(40);
    color: #ffffff;
    line-height: rem(72);
  }
}
</style>
