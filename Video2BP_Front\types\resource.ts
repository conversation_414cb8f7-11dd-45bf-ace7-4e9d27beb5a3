export interface ResourceListParams {
	pageNum: number;
	pageSize: number;
	keyword?: string; // 搜索关键词，可选
}

export interface ResourceItem {
	id: number;
	name: string;
	image: string;
	downloadUrl?: string;
	fileSize?: string;
	fileType?: string;
	createdAt?: string;
	updatedAt?: string;
	downloadCount?: number;
	description?: string;
}

export interface ResourceListData {
	data: ResourceItem[];
	total: number;
	pageNum: number;
	pageSize: number;
}

export interface ResourceListResponse {
	code: number;
	message: string;
	data: ResourceListData;
}
