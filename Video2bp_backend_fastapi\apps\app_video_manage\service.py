import os
import shutil
import uuid
import zipfile
import asyncio
from io import Bytes<PERSON>
from typing import Dict, Any, List
import redis
from fastapi import HTTP<PERSON>x<PERSON>, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc, func
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND

from apps.app_video_manage.model import UserVideos
from db.models import VideoProcessRecords
from lib.upload_manager import upload_manager
from apps.app_video_manage.schema import (
    MultiVideoUploadResponse, VideoUploadProgress,
    UploadProgressResponse
)
from core.config import settings
from core.e import ErrorCode, ErrorMessage


class VideoManageService:
    """视频管理服务"""
    
    def __init__(self):
        # 初始化Redis连接
        self.redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
        
        # 视频文件扩展名
        self.video_extensions = {"mp4", "avi", "mov"}
        
        # 上传目录配置
        self.video_upload_folder = "static/videos"
        self.bp_upload_folder = "static/bp"
        
        # 确保目录存在
        os.makedirs(self.video_upload_folder, exist_ok=True)
        os.makedirs(self.bp_upload_folder, exist_ok=True)
    
    def allowed_file(self, filename: str) -> bool:
        """检查文件扩展名是否允许"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.video_extensions
    
    def generate_video_filename(self, original_filename: str) -> str:
        """生成唯一的视频文件名"""
        ext = original_filename.rsplit('.', 1)[1].lower()
        unique_id = str(uuid.uuid4()).replace('-', '')
        return f"{unique_id}.{ext}"
    
    async def upload_video(self, db: AsyncSession, user_id: int, file: UploadFile) -> Dict[str, Any]:
        """上传视频文件"""
        if not self.allowed_file(file.filename):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INVALID_PARAMS}_{ErrorMessage.get(ErrorCode.INVALID_PARAMS)}"
            )
        
        # 生成唯一文件名
        filename = self.generate_video_filename(file.filename)
        save_path = os.path.join(self.video_upload_folder, filename)
        
        # 保存文件
        with open(save_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 保存到数据库
        async with db.begin():
            new_user_video = UserVideos(user_id=user_id, filename=filename)
            db.add(new_user_video)
            await db.commit()
        
        return {"filename": filename}
    
    async def get_video_list(self, db: AsyncSession, user_id: int, start_page: int, page_size: int) -> Dict[str, Any]:
        """获取用户视频列表"""
        # 计算总数
        total_result = await db.execute(
            select(func.count()).select_from(UserVideos).filter(UserVideos.user_id == user_id)
        )
        total = total_result.scalar()
        
        # 获取分页数据
        offset = (start_page - 1) * page_size
        result = await db.execute(
            select(UserVideos)
            .filter(UserVideos.user_id == user_id)
            .order_by(desc(UserVideos.created_at))
            .offset(offset)
            .limit(page_size)
        )
        videos = result.scalars().all()
        
        video_list = []
        for video in videos:
            video_list.append({
                "videoId": video.id,
                "filename": video.filename,
                "bpFilename": video.bp_filename,
                "createdAt": video.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updatedAt": video.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
                "status": video.status,
            })
        
        return {
            "total": total,
            "videoList": video_list
        }
    
    async def get_video_path(self, db: AsyncSession, user_id: int, video_id: int) -> str:
        """获取视频文件路径"""
        result = await db.execute(
            select(UserVideos).filter(
                UserVideos.id == video_id,
                UserVideos.user_id == user_id
            )
        )
        video = result.scalars().first()
        
        if not video:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_{ErrorMessage.get(ErrorCode.NOT_FOUND)}"
            )
        
        file_path = os.path.join(self.video_upload_folder, video.filename)
        
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_Video file not found"
            )
        
        return file_path
    
    async def get_bp_path(self, db: AsyncSession, user_id: int, video_id: int) -> str:
        """获取骨骼点文件路径"""
        result = await db.execute(
            select(UserVideos).filter(
                UserVideos.id == video_id,
                UserVideos.user_id == user_id
            )
        )
        video = result.scalars().first()
        
        if not video:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_{ErrorMessage.get(ErrorCode.NOT_FOUND)}"
            )
        
        # 生成骨骼点文件名
        bp_filename = f"{os.path.splitext(video.filename)[0]}_bp.json"
        bp_path = os.path.join(self.bp_upload_folder, bp_filename)
        
        if not os.path.exists(bp_path):
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_BP file not found"
            )
        
        return bp_path
    
    async def get_video_all(self, db: AsyncSession, user_id: int, video_id: int) -> BytesIO:
        """打包下载视频和骨骼点文件"""
        # 获取视频信息
        result = await db.execute(
            select(UserVideos).filter(
                UserVideos.id == video_id,
                UserVideos.user_id == user_id
            )
        )
        video = result.scalars().first()
        
        if not video:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.NOT_FOUND}_{ErrorMessage.get(ErrorCode.NOT_FOUND)}"
            )
        
        # 创建内存中的ZIP文件
        memory_file = BytesIO()
        
        with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            # 添加视频文件
            video_path = os.path.join(self.video_upload_folder, video.filename)
            if os.path.exists(video_path):
                zf.write(video_path, video.filename)
            
            # 添加骨骼点文件
            bp_filename = f"{os.path.splitext(video.filename)[0]}_bp.json"
            bp_path = os.path.join(self.bp_upload_folder, bp_filename)
            if os.path.exists(bp_path):
                zf.write(bp_path, bp_filename)
        
        memory_file.seek(0)
        return memory_file
    
    async def save_chunk(self, user_id: int, file_id: str, chunk: UploadFile, 
                        chunk_number: int, total_chunks: int) -> Dict[str, Any]:
        """保存分片文件"""
        # 创建临时目录
        temp_dir = os.path.join(self.video_upload_folder, "temp", file_id)
        os.makedirs(temp_dir, exist_ok=True)
        
        # 保存分块
        chunk_path = os.path.join(temp_dir, f"chunk_{chunk_number}")
        with open(chunk_path, "wb") as buffer:
            content = await chunk.read()
            buffer.write(content)
        
        # 更新Redis中的上传进度
        redis_key = f"upload:{user_id}:{file_id}"
        self.redis_client.hset(redis_key, str(chunk_number), "1")
        
        # 计算已完成分块数
        uploaded_chunks = len(self.redis_client.hkeys(redis_key))
        progress = int((uploaded_chunks / total_chunks) * 100)
        
        return {
            "progress": progress,
            "uploadedChunks": uploaded_chunks,
            "totalChunks": total_chunks
        }
    
    async def merge_chunks(self, db: AsyncSession, user_id: int, file_id: str, filename: str) -> Dict[str, Any]:
        """合并分片文件"""
        if not self.allowed_file(filename):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INVALID_PARAMS}_{ErrorMessage.get(ErrorCode.INVALID_PARAMS)}"
            )
        
        temp_dir = os.path.join(self.video_upload_folder, "temp", file_id)
        final_filename = self.generate_video_filename(filename)
        final_path = os.path.join(self.video_upload_folder, final_filename)
        
        # 合并所有分块
        with open(final_path, "wb") as outfile:
            chunk_files = sorted([f for f in os.listdir(temp_dir) if f.startswith("chunk_")],
                               key=lambda x: int(x.split("_")[1]))
            for chunk_file in chunk_files:
                chunk_path = os.path.join(temp_dir, chunk_file)
                with open(chunk_path, "rb") as infile:
                    outfile.write(infile.read())
        
        # 清理临时文件
        shutil.rmtree(temp_dir)
        
        # 清理Redis中的上传进度
        redis_key = f"upload:{user_id}:{file_id}"
        self.redis_client.delete(redis_key)
        
        # 保存到数据库
        async with db.begin():
            new_user_video = UserVideos(user_id=user_id, filename=final_filename)
            db.add(new_user_video)
            await db.commit()
        
        return {"filename": final_filename}
    
    async def get_upload_status(self, user_id: int, file_id: str) -> Dict[str, Any]:
        """获取上传状态"""
        redis_key = f"upload:{user_id}:{file_id}"
        uploaded_chunks = len(self.redis_client.hkeys(redis_key))

        # 这里需要从某个地方获取总分片数，可能需要在开始上传时存储
        # 暂时返回基本信息
        return {
            "uploadedChunks": uploaded_chunks,
            "status": "uploading" if uploaded_chunks > 0 else "not_started"
        }

    # 新增的方法，根据前端API文档要求

    async def get_video_upload_status(self, db: AsyncSession, user_id: int, file_id: str) -> Dict[str, Any]:
        """获取视频上传状态"""
        try:
            # 查询视频记录
            result = await db.execute(
                select(UserVideos).filter(
                    UserVideos.user_id == user_id,
                    UserVideos.filename.like(f"%{file_id}%")
                ).order_by(desc(UserVideos.created_at))
            )
            video = result.scalars().first()

            if not video:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail=f"{ErrorCode.VIDEO_NOT_FOUND}_{ErrorMessage.get(ErrorCode.VIDEO_NOT_FOUND)}"
                )

            # 构建响应数据
            video_web_path = f"/static/videos/{video.filename}"
            video_file_path = os.path.join(self.video_upload_folder, video.filename)

            # 假设骨骼点文件名为视频文件名去掉扩展名加上.json
            bp_filename = video.filename.rsplit('.', 1)[0] + '.json'
            json_web_path = f"/static/bp/{bp_filename}"
            json_file_path = os.path.join(self.bp_upload_folder, bp_filename)

            return {
                "video_id": str(video.id),
                "status": "completed" if video.status == 1 else "processing",
                "video_web_path": video_web_path,
                "video_file_path": video_file_path,
                "json_web_path": json_web_path,
                "json_file_path": json_file_path
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )

    async def process_video(self, db: AsyncSession, user_id: int, video_id: str) -> None:
        """处理视频"""
        try:
            # 查询视频记录
            result = await db.execute(
                select(UserVideos).filter(
                    UserVideos.user_id == user_id,
                    UserVideos.id == int(video_id)
                )
            )
            video = result.scalars().first()

            if not video:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail=f"{ErrorCode.VIDEO_NOT_FOUND}_{ErrorMessage.get(ErrorCode.VIDEO_NOT_FOUND)}"
                )

            # 这里应该启动视频处理任务
            # 暂时只更新状态为处理中
            video.status = 2  # 假设2表示处理中
            await db.commit()

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )

    async def get_process_status(self, db: AsyncSession, user_id: int) -> Dict[str, Any]:
        """获取处理状态"""
        try:
            # 查询用户最新的处理中视频
            result = await db.execute(
                select(UserVideos).filter(
                    UserVideos.user_id == user_id,
                    UserVideos.status == 2  # 处理中状态
                ).order_by(desc(UserVideos.created_at))
            )
            video = result.scalars().first()

            if not video:
                return {"status": "no_processing", "progress": 0}

            # 查询视频处理记录
            process_result = await db.execute(
                select(VideoProcessRecords).filter(
                    VideoProcessRecords.user_id == user_id,
                    VideoProcessRecords.video_id == video.id
                ).order_by(desc(VideoProcessRecords.created_at))
            )
            process_record = process_result.scalars().first()

            if process_record:
                status_map = {
                    0: "pending",
                    1: "processing",
                    2: "completed",
                    3: "failed"
                }
                return {
                    "status": status_map.get(process_record.process_status, "unknown"),
                    "progress": process_record.progress,
                    "video_id": video.id,
                    "error_message": process_record.error_message
                }
            else:
                # 如果没有处理记录，返回默认状态
                return {
                    "status": "processing",
                    "progress": 0,
                    "video_id": video.id
                }

        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )

    async def simple_upload_video(self, db: AsyncSession, user_id: int, video: UploadFile, filename: str) -> Dict[str, Any]:
        """简单视频上传"""
        try:
            if not self.allowed_file(filename):
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail=f"{ErrorCode.INVALID_FILE_TYPE}_{ErrorMessage.get(ErrorCode.INVALID_FILE_TYPE)}"
                )

            # 生成唯一文件名
            unique_filename = self.generate_video_filename(filename)
            file_path = os.path.join(self.video_upload_folder, unique_filename)

            # 保存文件
            with open(file_path, "wb") as buffer:
                content = await video.read()
                buffer.write(content)

            # 保存到数据库
            new_video = UserVideos(
                user_id=user_id,
                filename=unique_filename,
                status=1  # 上传完成
            )
            db.add(new_video)
            await db.commit()
            await db.refresh(new_video)

            # 生成访问URL
            video_url = f"/static/videos/{unique_filename}"

            return {
                "id": f"video_{new_video.id}",
                "url": video_url
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )

    async def create_file_record(self, db: AsyncSession, user_id: int, request) -> Dict[str, Any]:
        """创建文件记录"""
        try:
            # 验证视频是否存在
            video_id = request.id.replace("video_", "")
            result = await db.execute(
                select(UserVideos).filter(
                    UserVideos.user_id == user_id,
                    UserVideos.id == int(video_id)
                )
            )
            video = result.scalars().first()

            if not video:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail=f"{ErrorCode.VIDEO_NOT_FOUND}_{ErrorMessage.get(ErrorCode.VIDEO_NOT_FOUND)}"
                )

            # 验证参数
            if request.endTime <= request.startTime:
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_时间范围错误，endTime必须大于startTime"
                )

            if not (1 <= request.outputFormat <= 4):
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_请求参数错误，outputFormat必须为1-4之间的数字"
                )

            # 生成文件记录ID
            file_record_id = f"file_{uuid.uuid4().hex[:10]}"
            project_id = f"project_{uuid.uuid4().hex[:8]}"

            # 保存文件记录到数据库
            video_process_record = VideoProcessRecords(
                user_id=user_id,
                video_id=request.videoId,
                file_record_id=file_record_id,
                project_id=project_id,
                start_time=request.startTime,
                end_time=request.endTime,
                duration=request.endTime - request.startTime,
                was_trimmed=request.endTime != request.startTime,
                output_format=request.outputFormat,
                original_selection=request.dict(),
                form_data=request.dict(),
                process_status=0,  # 0-待处理
                progress=0
            )

            db.add(video_process_record)
            await db.commit()
            await db.refresh(video_process_record)

            return {
                "id": file_record_id,
                "fileId": project_id,
                "status": "created"
            }

        except Exception as e:
            raise HTTPException(
                status_code=HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"500_创建文件记录失败: {str(e)}"
            )

    async def start_multi_upload(self, user_id: int, filenames: List[str]) -> MultiVideoUploadResponse:
        """开始多文件上传任务"""
        try:
            # 检查文件数量限制
            if len(filenames) > 5:
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_最多只能同时上传5个文件"
                )

            if len(filenames) == 0:
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_至少需要上传1个文件"
                )

            # 检查文件名格式
            allowed_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
            for filename in filenames:
                file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
                if f'.{file_ext}' not in allowed_extensions:
                    raise HTTPException(
                        status_code=HTTP_400_BAD_REQUEST,
                        detail=f"400_文件 {filename} 格式不支持，仅支持mp4, avi, mov, wmv, flv, mkv格式"
                    )

            # 创建批量上传任务
            upload_id = upload_manager.create_batch_upload(user_id, filenames)

            # 构建响应
            progress_list = []
            for filename in filenames:
                progress_list.append(VideoUploadProgress(
                    filename=filename,
                    progress=0,
                    status="pending"
                ))

            return MultiVideoUploadResponse(
                uploadId=upload_id,
                totalFiles=len(filenames),
                progress=progress_list
            )

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"500_创建上传任务失败: {str(e)}"
            )

    async def upload_file_in_batch(self, db: AsyncSession, upload_id: str, filename: str, video_file, user_id: int):
        """在批量上传任务中上传单个文件"""
        try:
            # 更新进度为开始上传
            upload_manager.update_file_progress(upload_id, filename, 0, "uploading")

            # 模拟上传进度更新
            for progress in [10, 30, 50, 70, 90]:
                upload_manager.update_file_progress(upload_id, filename, progress, "uploading")
                await asyncio.sleep(0.1)  # 模拟上传时间

            # 调用原有的上传逻辑
            result = await self.simple_upload_video(db, user_id, video_file, filename)

            # 更新为完成状态
            upload_manager.set_file_result(
                upload_id,
                filename,
                video_id=result["id"],
                video_url=result["url"]
            )

        except Exception as e:
            # 更新为失败状态
            upload_manager.set_file_result(upload_id, filename, error=str(e))
            raise

    async def get_upload_progress(self, upload_id: str, user_id: int) -> UploadProgressResponse:
        """获取上传进度"""
        try:
            task = upload_manager.get_task(upload_id)
            if not task:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail="404_上传任务不存在"
                )

            # 检查用户权限
            if task.user_id != user_id:
                raise HTTPException(
                    status_code=HTTP_403_FORBIDDEN,
                    detail="403_无权访问此上传任务"
                )

            # 构建进度响应
            progress_list = []
            for filename, file_progress in task.files.items():
                progress_list.append(VideoUploadProgress(
                    filename=file_progress.filename,
                    progress=file_progress.progress,
                    status=file_progress.status,
                    id=file_progress.video_id,
                    url=file_progress.video_url,
                    error=file_progress.error
                ))

            return UploadProgressResponse(
                uploadId=upload_id,
                totalFiles=task.total_files,
                completedFiles=task.completed_files,
                overallProgress=task.overall_progress,
                progress=progress_list
            )

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"500_获取上传进度失败: {str(e)}"
            )

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )


# 创建服务实例
video_manage_service = VideoManageService()
