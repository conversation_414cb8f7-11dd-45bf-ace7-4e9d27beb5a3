<template>
    <div ref="imageContainer" :class="[
        'optimized-image-container',
        {
            'is-background': isBackground,
            'is-loading': isLoading,
            'is-loaded': isLoaded,
            'is-error': hasError,
            'is-critical': isCritical
        }
    ]" :style="containerStyle">
        <!-- 背景图片模式 -->
        <template v-if="isBackground">
            <!-- 低质量占位图 -->
            <NuxtImg v-if="showPlaceholder && placeholder" :src="getImageUrl(placeholder)" :alt="alt"
                class="image-placeholder" loading="eager" :quality="20" format="webp" @load="handlePlaceholderLoad"
                @error="handleError" />

            <!-- 主图片 -->
            <NuxtImg :src="getImageUrl(src)" :alt="alt" class="image-main" :class="{ 'image-loaded': isLoaded }"
                :loading="isCritical ? 'eager' : 'lazy'" :fetchpriority="isCritical ? 'high' : 'auto'" :format="format"
                :quality="quality" :sizes="sizes" :width="width" :height="height" v-bind="$attrs" @load="handleMainLoad"
                @error="handleError" />
        </template>

        <!-- 常规图片模式 -->
        <template v-else>
            <NuxtPicture :src="getImageUrl(src)" :alt="alt" class="image-picture"
                :loading="isCritical ? 'eager' : 'lazy'" :fetchpriority="isCritical ? 'high' : 'auto'" :format="format"
                :quality="quality" :sizes="sizes" :width="width" :height="height" :img-attrs="{
                    onLoad: handleMainLoad,
                    onError: handleError,
                    ...imgAttrs
                }" v-bind="$attrs" />
        </template>

        <!-- 加载状态 -->
        <div v-if="showLoadingState && isLoading" class="loading-overlay">
            <div class="loading-spinner">
                <slot name="loading">
                    <div class="spinner"></div>
                </slot>
            </div>
        </div>

        <!-- 错误状态 -->
        <div v-if="hasError" class="error-overlay">
            <slot name="error">
                <div class="error-message">图片加载失败</div>
            </slot>
        </div>

        <!-- 内容插槽 -->
        <div v-if="$slots.default" class="image-content">
            <slot />
        </div>
    </div>
</template>

<script setup lang="ts">
interface Props {
    // 图片路径（相对于服务器 assets/images/ 目录）
    src: string
    // 替代文本
    alt?: string
    // 是否作为背景图片
    isBackground?: boolean
    // 是否是关键图片（首屏重要图片）
    isCritical?: boolean
    // 图片格式
    format?: 'webp' | 'avif' | 'png' | 'jpg' | 'jpeg'
    // 图片质量
    quality?: number
    // 响应式尺寸
    sizes?: string
    // 宽度
    width?: number
    // 高度
    height?: number
    // 低质量占位图
    placeholder?: string
    // 是否显示加载状态
    showLoadingState?: boolean
    // 容器样式
    containerClass?: string
    // 额外的 img 属性
    imgAttrs?: Record<string, any>
    // 对象适配方式（背景图片模式）
    objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none'
    // 对象位置（背景图片模式）
    objectPosition?: string
}

const props = withDefaults(defineProps<Props>(), {
    alt: '',
    isBackground: false,
    isCritical: false,
    format: 'webp',
    quality: 85,
    sizes: '100vw',
    showLoadingState: true,
    containerClass: '',
    imgAttrs: () => ({}),
    objectFit: 'cover',
    objectPosition: 'center'
})

// 获取运行时配置
const config = useRuntimeConfig()

// 服务器图片基础路径（从环境变量获取）
const IMAGE_BASE_URL = config.public.imageBaseUrl

// 响应式状态
const isLoading = ref(true)
const isLoaded = ref(false)
const hasError = ref(false)
const showPlaceholder = ref(true)
const imageContainer = ref<HTMLElement>()

// 计算属性
const containerStyle = computed(() => {
    const styles: Record<string, string> = {}

    if (props.isBackground) {
        if (props.width) styles.width = `${props.width}px`
        if (props.height) styles.height = `${props.height}px`
    }

    return styles
})

// 获取完整图片URL
const getImageUrl = (imagePath: string): string => {
    if (!imagePath) return ''

    // 如果已经是完整URL，直接返回
    if (imagePath.startsWith('http') || imagePath.startsWith('//')) {
        return imagePath
    }

    const cleanPath = imagePath.replace(/^\/+/, '')

    return `${IMAGE_BASE_URL}/${cleanPath}`
}

// 事件处理
const handlePlaceholderLoad = () => {
    // 占位图加载完成，可以开始加载主图
}

const handleMainLoad = () => {
    isLoading.value = false
    isLoaded.value = true
    hasError.value = false

    // 延迟隐藏占位图，创建平滑过渡
    setTimeout(() => {
        showPlaceholder.value = false
    }, 300)

    // 触发自定义事件
    emit('load')
}

const handleError = (error: string | Event) => {
    isLoading.value = false
    hasError.value = true
    emit('error', error)
}

// 暴露的事件
const emit = defineEmits<{
    load: []
    error: [error: string | Event]
}>()

// 性能监控
const trackImagePerformance = () => {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            entries.forEach((entry) => {
                if (entry.name.includes(props.src)) {
                    console.log(`Image ${props.src} loaded in ${entry.duration}ms`)
                }
            })
        })

        observer.observe({ entryTypes: ['resource'] })
    }
}

// 预加载关键图片
const preloadCriticalImage = () => {
    if (props.isCritical && typeof window !== 'undefined') {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.as = 'image'
        link.href = getImageUrl(props.src)
        link.type = `image/${props.format}`
        document.head.appendChild(link)
    }
}

onMounted(() => {
    trackImagePerformance()
    preloadCriticalImage()
})

// 组件属性继承
defineOptions({
    inheritAttrs: false
})
</script>

<style scoped>
.optimized-image-container {
    position: relative;
    display: inline-block;
    overflow: hidden;
}

.optimized-image-container.is-background {
    width: 100%;
    height: 100%;
}

/* 背景图片样式 */
.image-placeholder,
.image-main {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: v-bind(objectFit);
    object-position: v-bind(objectPosition);
    transition: opacity 0.3s ease;
}

.image-placeholder {
    filter: blur(5px);
    transform: scale(1.05);
    z-index: 1;
}

.image-main {
    opacity: 0;
    z-index: 2;
}

.image-main.image-loaded {
    opacity: 1;
}

/* 常规图片样式 */
.image-picture :deep(img) {
    width: 100%;
    height: auto;
    transition: opacity 0.3s ease;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 错误状态 */
.error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 0, 0, 0.1);
    z-index: 10;
}

.error-message {
    color: #e74c3c;
    font-size: 14px;
    text-align: center;
    padding: 20px;
}

/* 内容层 */
.image-content {
    position: relative;
    z-index: 5;
    width: 100%;
    height: 100%;
}
</style>
