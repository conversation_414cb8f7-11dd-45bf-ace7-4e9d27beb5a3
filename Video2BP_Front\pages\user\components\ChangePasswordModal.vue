<template>
  <a-modal v-model:open="visible" title="" :width="500" :closable="true" :ok-text="submitText" cancel-text="取消"
    :mask-closable="false" :confirm-loading="isLoading" class="change-password-modal" @cancel="handleCancel"
    @ok="handleSubmit">
    <div class="modal-content">
      <div class="modal-title">修改密码</div>

      <div class="form-container">
        <!-- 当前密码 -->
        <div class="form-item" :class="{ 'has-error': errors.currentPassword }">
          <label class="form-label">当前密码</label>
          <div class="input-wrapper">
            <a-input v-model:value="formData.currentPassword" :type="currentPasswordVisible ? 'text' : 'password'"
              placeholder="请输入当前密码" class="form-input" @blur="validateCurrentPassword" />
            <span class="password-toggle" @click="currentPasswordVisible = !currentPasswordVisible">
              <EyeOutlined v-if="currentPasswordVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="errors.currentPassword" class="error-message">{{ errors.currentPassword }}</div>
        </div>

        <!-- 新密码 -->
        <div class="form-item" :class="{ 'has-error': errors.newPassword }">
          <label class="form-label">新密码</label>
          <div class="input-wrapper">
            <a-input v-model:value="formData.newPassword" :type="newPasswordVisible ? 'text' : 'password'"
              placeholder="请输入新密码（6-20位）" class="form-input" @blur="validateNewPassword" />
            <span class="password-toggle" @click="newPasswordVisible = !newPasswordVisible">
              <EyeOutlined v-if="newPasswordVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="errors.newPassword" class="error-message">{{ errors.newPassword }}</div>
        </div>

        <!-- 确认新密码 -->
        <div class="form-item" :class="{ 'has-error': errors.confirmPassword }">
          <label class="form-label">确认新密码</label>
          <div class="input-wrapper">
            <a-input v-model:value="formData.confirmPassword" :type="confirmPasswordVisible ? 'text' : 'password'"
              placeholder="请再次输入新密码" class="form-input" @blur="validateConfirmPassword" />
            <span class="password-toggle" @click="confirmPasswordVisible = !confirmPasswordVisible">
              <EyeOutlined v-if="confirmPasswordVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'
import { useApi } from '~/composables/useApi'

interface Props {
  open: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:open': [value: boolean]
  'success': []

}>()

const { user } = useApi()

// 响应式数据
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

// 表单数据
const formData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 验证错误信息
const errors = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码可见性控制
const currentPasswordVisible = ref(false)
const newPasswordVisible = ref(false)
const confirmPasswordVisible = ref(false)

// 加载状态
const isLoading = ref(false)

// 提交按钮文本
const submitText = computed(() => {
  return isLoading.value ? '修改中...' : '确认修改'
})

// 验证函数
const validateCurrentPassword = () => {
  if (!formData.currentPassword) {
    errors.currentPassword = '请输入当前密码'
    return false
  }
  errors.currentPassword = ''
  return true
}

const validateNewPassword = () => {
  if (!formData.newPassword) {
    errors.newPassword = '请输入新密码'
    return false
  }
  if (formData.newPassword.length < 6 || formData.newPassword.length > 20) {
    errors.newPassword = '密码长度应为6-20位'
    return false
  }
  if (formData.newPassword === formData.currentPassword) {
    errors.newPassword = '新密码不能与当前密码相同'
    return false
  }
  errors.newPassword = ''
  return true
}

const validateConfirmPassword = () => {
  if (!formData.confirmPassword) {
    errors.confirmPassword = '请确认新密码'
    return false
  }
  if (formData.confirmPassword !== formData.newPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
    return false
  }
  errors.confirmPassword = ''
  return true
}

// 验证整个表单
const validateForm = () => {
  const currentValid = validateCurrentPassword()
  const newValid = validateNewPassword()
  const confirmValid = validateConfirmPassword()
  return currentValid && newValid && confirmValid
}

// 重置表单
const resetForm = () => {
  formData.currentPassword = ''
  formData.newPassword = ''
  formData.confirmPassword = ''
  errors.currentPassword = ''
  errors.newPassword = ''
  errors.confirmPassword = ''
  currentPasswordVisible.value = false
  newPasswordVisible.value = false
  confirmPasswordVisible.value = false
}

// 处理取消
const handleCancel = () => {
  resetForm()
  visible.value = false
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  try {
    isLoading.value = true;

    const result = await user?.changePassword({
      currentPassword: formData.currentPassword,
      newPassword: formData.newPassword
    })

    if (!result) {
      throw new Error('修改密码失败')
    }

    resetForm()
    visible.value = false
    emit('success')

  } catch (error) {
    message.error(error as string)
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="scss">
.change-password-modal {
  .ant-modal-content {
    background: rgba(42, 40, 68, 0.95);
    border-radius: rem(12);
    padding: 0;
  }

  .ant-modal-header {
    display: none;
  }

  .ant-modal-body {
    padding: rem(32) rem(24);
  }

  .ant-modal-footer {
    padding: rem(20) rem(24);
    margin-top: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    button {
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: 400;
      font-size: rem(16);
      line-height: rem(24);
      border-radius: rem(6);
      border: none;
      cursor: pointer;
      transition: all 0.2s;
      height: rem(40);
      padding: 0 rem(20);
    }

    .ant-btn-primary {
      background: #0091ff;

      &:hover {
        background: #0091ff;
        opacity: 0.8;
      }
    }

    .ant-btn-default {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.2);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .ant-modal-close {
    color: #ffffff;
    font-size: rem(18);

    &:hover {
      color: #0091ff;
    }
  }
}
</style>

<style lang="scss" scoped>
.modal-content {
  .modal-title {
    font-size: rem(24);
    font-weight: 600;
    color: #ffffff;
    margin-bottom: rem(32);
    text-align: center;
    font-family: SourceHanSerifSC, SourceHanSerifSC;
  }

  .form-container {
    .form-item {
      margin-bottom: rem(24);

      &:last-child {
        margin-bottom: 0;
      }

      .form-label {
        display: block;
        color: #ffffff;
        font-size: rem(14);
        font-weight: 500;
        margin-bottom: rem(8);
        font-family: SourceHanSerifSC, SourceHanSerifSC;
      }

      .input-wrapper {
        position: relative;

        .form-input {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: rem(6);
          color: #ffffff;
          padding-right: rem(40);

          &:focus {
            border-color: #0091ff;
            box-shadow: 0 0 0 2px rgba(0, 145, 255, 0.2);
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .password-toggle {
          position: absolute;
          right: rem(12);
          top: 50%;
          transform: translateY(-50%);
          color: rgba(255, 255, 255, 0.6);
          cursor: pointer;
          font-size: rem(16);
          transition: color 0.2s;

          &:hover {
            color: #0091ff;
          }
        }
      }

      .error-message {
        color: #ff4d4f;
        font-size: rem(12);
        margin-top: rem(4);
        font-family: SourceHanSerifSC, SourceHanSerifSC;
      }

      &.has-error {
        .form-input {
          border-color: #ff4d4f;

          &:focus {
            border-color: #ff4d4f;
            box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
          }
        }
      }
    }
  }
}
</style>
