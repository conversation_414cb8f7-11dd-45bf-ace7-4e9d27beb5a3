<template>
    <div class="loading-wrapper" :class="{ 'loading-wrapper--overlay': overlay }">
        <div v-if="overlay" class="loading-overlay">
            <div class="loading-content">
                <div :class="['loading-spinner', sizeClass]"></div>
                <p v-if="text" class="loading-text">{{ text }}</p>
            </div>
        </div>
        <div v-else :class="['loading-spinner', sizeClass]"></div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
    size?: 'small' | 'default' | 'large'
    overlay?: boolean
    text?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 'default',
    overlay: false,
    text: ''
})

const sizeClass = computed(() => `loading-spinner--${props.size}`)
</script>

<style scoped lang="scss">
.loading-wrapper--overlay {
    position: relative;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
    z-index: 1000;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: rem(12);
}

.loading-text {
    margin: 0;
    color: #fff;
    font-size: rem(14);
    width: rem(100);
    text-align: center;
}

/* Spinner 动画 */
.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 尺寸变体 */
.loading-spinner--small {
    width: rem(20);
    height: rem(20);
    border-width: rem(2);

    &+p {
        font-size: rem(16);
    }
}

.loading-spinner--default {
    width: rem(40);
    height: rem(40);
    border-width: rem(3);

    &+p {
        font-size: rem(14);
    }
}

.loading-spinner--large {
    width: rem(60);
    height: rem(60);
    border-width: rem(4);

    &+p {
        font-size: rem(18);
    }
}
</style>
