from typing import Optional
from decimal import Decimal
from pydantic import BaseModel, EmailStr, Field
from schemas.base import BaseSchema
from schemas.response import StandardResponse


class SendVerificationCodeRequest(BaseSchema):
    """发送验证码请求"""
    email: EmailStr = Field(..., description="邮箱地址")


class SendVerificationCodeResponse(BaseSchema):
    """发送验证码响应"""
    pass


class SendVerificationCodeResponseModel(StandardResponse):
    """发送验证码响应模型"""
    data: Optional[SendVerificationCodeResponse] = None


class UserRegisterRequest(BaseSchema):
    """用户注册请求"""
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=6, max_length=32, description="密码")
    username: str = Field(None, min_length=3, max_length=20, description="用户名（可选）")


class UserRegisterResponse(BaseSchema):
    """用户注册响应"""
    id: int = Field(..., description="用户ID")
    email: EmailStr = Field(..., description="邮箱地址")
    username: str = Field(None, description="用户名")
    created_at: str = Field(..., description="注册时间")


class UserRegisterResponseModel(StandardResponse):
    """用户注册响应模型"""
    data: Optional[UserRegisterResponse] = None


class UserLoginRequest(BaseSchema):
    """用户登录请求"""
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., description="密码")


class UserLoginResponse(BaseSchema):
    """用户登录响应"""
    pass


class UserLoginResponseModel(StandardResponse):
    """用户登录响应模型"""
    data: Optional[UserLoginResponse] = None


class UserLogoutResponse(BaseSchema):
    """用户登出响应"""
    pass


class UserLogoutResponseModel(StandardResponse):
    """用户登出响应模型"""
    data: Optional[UserLogoutResponse] = None


class UserInfoRequest(BaseSchema):
    """获取用户信息请求"""
    userId: int = Field(..., description="用户ID")


class UserInfoResponse(BaseSchema):
    """用户信息响应"""
    id: int = Field(..., description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    email: str = Field(..., description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    balance: Decimal = Field(..., description="账户余额", ge=0)
    status: int = Field(..., description="状态")
    vipLevel: int = Field(..., description="VIP等级", alias="vip_level")


class UserInfoResponseModel(StandardResponse):
    """用户信息响应模型"""
    data: Optional[UserInfoResponse] = None


# 新增的schema定义，根据前端API文档要求

class GetUserInfoResponse(BaseSchema):
    """GET获取用户信息响应"""
    id: str = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    role: str = Field(..., description="角色")
    avatar: Optional[str] = Field(None, description="头像URL")
    createdAt: str = Field(..., description="创建时间")
    updatedAt: str = Field(..., description="更新时间")
    balance: float = Field(..., description="账户余额")
    vipLevel: int = Field(..., description="VIP等级")
    vipDeadline: Optional[str] = Field(None, description="VIP到期时间")


class GetUserInfoResponseModel(StandardResponse):
    """GET获取用户信息响应模型"""
    data: Optional[GetUserInfoResponse] = None


class ChangePasswordRequest(BaseSchema):
    """修改密码请求"""
    currentPassword: str = Field(..., description="当前密码")
    newPassword: str = Field(..., min_length=6, max_length=20, description="新密码")


class ChangePasswordResponse(BaseSchema):
    """修改密码响应"""
    pass


class ChangePasswordResponseModel(StandardResponse):
    """修改密码响应模型"""
    data: Optional[ChangePasswordResponse] = None


class UpdateUserRequest(BaseSchema):
    """更新用户信息请求"""
    username: Optional[str] = Field(None, description="用户名")
    avatar: Optional[str] = Field(None, description="头像URL")
    balance: Optional[float] = Field(None, description="账户余额")
    vipLevel: Optional[int] = Field(None, description="VIP等级")
    vipDeadline: Optional[str] = Field(None, description="VIP到期时间")


class UpdateUserResponse(BaseSchema):
    """更新用户信息响应"""
    pass


class UpdateUserResponseModel(StandardResponse):
    """更新用户信息响应模型"""
    data: Optional[UpdateUserResponse] = None


class OrderRecordsRequest(BaseSchema):
    """获取订单记录请求"""
    pageNum: int = Field(..., ge=1, description="当前页码")
    pageSize: int = Field(..., ge=5, le=50, description="每页数量")


class OrderRecord(BaseSchema):
    """订单记录"""
    id: str = Field(..., description="订单ID")
    type: str = Field(..., description="订单类型")
    time: str = Field(..., description="时间")
    amount: str = Field(..., description="金额")
    method: str = Field(..., description="支付方式")
    toAccount: str = Field(..., description="到账金额")


class OrderRecordsResponse(BaseSchema):
    """获取订单记录响应"""
    data: list[OrderRecord] = Field(..., description="订单记录列表")
    total: int = Field(..., description="总记录数")
    pageNum: int = Field(..., description="当前页码")
    pageSize: int = Field(..., description="每页数量")


class OrderRecordsResponseModel(StandardResponse):
    """获取订单记录响应模型"""
    data: Optional[OrderRecordsResponse] = None


# 错误响应模型
class ErrorResponseModel(BaseSchema):
    """错误响应模型"""
    code: int = Field(..., description="错误状态码")
    message: str = Field(..., description="错误信息")
    data: Optional[dict] = Field(None, description="错误时通常为null")


# 余额管理相关Schema
class BalanceUpdateRequest(BaseSchema):
    """余额更新请求"""
    amount: Decimal = Field(..., description="金额", gt=0)
    operation: str = Field(..., description="操作类型：recharge-充值，deduct-扣除")


class BalanceUpdateResponse(BaseSchema):
    """余额更新响应"""
    newBalance: Decimal = Field(..., description="更新后的余额")
    operation: str = Field(..., description="操作类型")
    amount: Decimal = Field(..., description="操作金额")


class BalanceUpdateResponseModel(StandardResponse):
    """余额更新响应模型"""
    data: Optional[BalanceUpdateResponse] = None


class BalanceQueryResponse(BaseSchema):
    """余额查询响应"""
    balance: Decimal = Field(..., description="当前余额")


class BalanceQueryResponseModel(StandardResponse):
    """余额查询响应模型"""
    data: Optional[BalanceQueryResponse] = None
