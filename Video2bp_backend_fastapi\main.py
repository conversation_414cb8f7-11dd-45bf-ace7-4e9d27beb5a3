from fastapi import FastAPI, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException
from starlette.middleware.cors import CORSMiddleware
from starlette.status import HTTP_400_BAD_REQUEST

from api.errors.http_error import http_error_handler
from api.errors.validation_error import http422_error_handler
from api.routes.api import router as api_router

from core.config import settings
from core.lifespan import lifespan

from utils.docs import get_custom_openapi

from middleware.logger import RequestLoggerMiddleware


# 自定义422错误处理器，将其转换为400错误
async def custom_422_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """将422验证错误转换为400错误，符合前端API文档要求"""
    return JSONResponse(
        status_code=HTTP_400_BAD_REQUEST,
        content={
            "code": 400,
            "message": "请求参数错误",
            "data": None
        }
    )


def get_application() -> FastAPI:
    # 项目配置
    application = FastAPI(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        debug=settings.DEBUG,
        lifespan=lifespan
    )

    # 跨域中间件
    application.add_middleware(
        CORSMiddleware,
        allow_credentials=True,
        allow_origins=["*"],
        allow_methods=["*"],
        allow_headers=["*"],
    )
    # 请求日志中间件
    application.add_middleware(RequestLoggerMiddleware)

    # 生成 OpenAPI 模式
    application.openapi = get_custom_openapi(application)

    # 异常处理句柄
    application.add_exception_handler(HTTPException, http_error_handler)
    application.add_exception_handler(ValueError, http422_error_handler)
    application.add_exception_handler(RequestValidationError, custom_422_handler)

    # 路由导入
    application.include_router(api_router, prefix=settings.API_PREFIX)

    return application


app = get_application()


if __name__ == "__main__":
    # 检查 mysql 相关内容
    import sys

    if "-c" in sys.argv:
        from utils.dbmanager import DBManager

        DBManager.check_and_autocreate()

    # 启动项目
    import uvicorn

    uvicorn.run(app="main:app", host="127.0.0.1", port=8000, reload=True)
