// 关键点数据结构
export interface Keypoint {
	x: number;
	y: number;
}

// 单个实例（人物）在一帧中
export interface SkeletonInstance {
	keypoints: [number, number][];
	bbox?: [number, number, number, number][];
	bbox_score?: number;
}

// 单帧数据结构
export interface SkeletonFrame {
	image_shape: [number, number]; // [高度, 宽度]
	frame_idx: number;
	instance_info: SkeletonInstance[];
}

// 骨架连接数据结构（关键点之间的连接）
export type SkeletonLink = [number, number];

// 骨架可视化的渲染选项
export interface RenderOptions {
	pointColor?: string;
	lineColor?: string;
	pointSize?: number;
	lineWidth?: number;
	offsetX?: number;
	offsetY?: number;
	bboxColor?: string;
	bboxLineWidth?: number;
	showBbox?: boolean;
}

// 播放器状态
export interface PlayerState {
	currentFrame: number;
	isPlaying: boolean;
	playbackSpeed: number;
	totalFrames: number;
}

// 边界框
export interface BoundingBox {
	x: number;
	y: number;
	width: number;
	height: number;
}
