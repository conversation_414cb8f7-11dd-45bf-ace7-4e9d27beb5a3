#!/usr/bin/env python3
"""
测试用户注册API接口
使用HTTP请求测试注册功能
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# API基础URL
BASE_URL = "http://localhost:8000/api"


def test_register_api():
    """测试注册API"""
    print("=== 用户注册API测试 ===\n")
    
    # 测试数据
    test_cases = [
        {
            "name": "基本注册测试",
            "data": {
                "email": "<EMAIL>",
                "password": "test123456",
                "username": "testuser1"
            }
        },
        {
            "name": "无用户名注册测试",
            "data": {
                "email": "<EMAIL>", 
                "password": "test123456"
            }
        },
        {
            "name": "重复邮箱测试",
            "data": {
                "email": "<EMAIL>",  # 重复邮箱
                "password": "test123456",
                "username": "testuser2"
            }
        },
        {
            "name": "密码过短测试",
            "data": {
                "email": "<EMAIL>",
                "password": "123",  # 密码过短
                "username": "testuser3"
            }
        },
        {
            "name": "邮箱格式错误测试",
            "data": {
                "email": "invalid-email",  # 邮箱格式错误
                "password": "test123456",
                "username": "testuser4"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"=== 测试 {i}: {test_case['name']} ===")
        
        try:
            # 发送注册请求
            response = requests.post(
                f"{BASE_URL}/user/register",
                json=test_case['data'],
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            print(f"请求数据: {json.dumps(test_case['data'], ensure_ascii=False)}")
            print(f"响应状态码: {response.status_code}")
            
            try:
                response_data = response.json()
                print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                
                # 分析响应
                if response.status_code == 200:
                    if response_data.get('code') == 0:
                        print("✅ 注册成功!")
                        if 'data' in response_data and response_data['data']:
                            user_data = response_data['data']
                            print(f"   用户ID: {user_data.get('id')}")
                            print(f"   邮箱: {user_data.get('email')}")
                            print(f"   用户名: {user_data.get('username')}")
                            print(f"   创建时间: {user_data.get('created_at')}")
                    else:
                        print(f"❌ 注册失败: {response_data.get('message', '未知错误')}")
                elif response.status_code == 401:
                    print(f"❌ 认证失败: {response_data.get('message', '未知错误')}")
                elif response.status_code == 422:
                    print(f"❌ 参数验证失败: {response_data}")
                else:
                    print(f"❌ 请求失败: HTTP {response.status_code}")
                    
            except json.JSONDecodeError:
                print(f"❌ 响应不是有效的JSON: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败: 请确保FastAPI服务器正在运行 (python main.py)")
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        print()


def test_login_api():
    """测试登录API"""
    print("=== 用户登录API测试 ===\n")
    
    # 使用注册成功的账号测试登录
    login_data = {
        "email": "<EMAIL>",
        "password": "test123456"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/user/login",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"登录请求数据: {json.dumps(login_data, ensure_ascii=False)}")
        print(f"响应状态码: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            if response.status_code == 200 and response_data.get('code') == 0:
                print("✅ 登录成功!")
            else:
                print(f"❌ 登录失败: {response_data.get('message', '未知错误')}")
                
        except json.JSONDecodeError:
            print(f"❌ 响应不是有效的JSON: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 请确保FastAPI服务器正在运行")
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def check_server_status():
    """检查服务器状态"""
    try:
        response = requests.get(f"{BASE_URL.replace('/api', '')}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI服务器正在运行")
            return True
        else:
            print(f"⚠️ 服务器响应异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到FastAPI服务器")
        print("   请运行: python main.py")
        return False
    except Exception as e:
        print(f"❌ 检查服务器状态时出错: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 用户注册API测试开始\n")
    
    # 检查服务器状态
    print("=== 服务器状态检查 ===")
    if not check_server_status():
        print("\n💡 启动服务器的步骤:")
        print("1. 打开新的终端窗口")
        print("2. 进入项目目录: cd Video2bp_backend_fastapi")
        print("3. 激活虚拟环境: fastapi_env\\Scripts\\activate")
        print("4. 启动服务器: python main.py")
        print("5. 等待服务器启动完成后重新运行此测试")
        return
    
    print()
    
    try:
        # 测试注册功能
        test_register_api()
        
        # 测试登录功能
        test_login_api()
        
        print("\n🎉 API测试完成！")
        
    except Exception as e:
        print(f"💥 测试过程中发生错误: {e}")


if __name__ == "__main__":
    main()
