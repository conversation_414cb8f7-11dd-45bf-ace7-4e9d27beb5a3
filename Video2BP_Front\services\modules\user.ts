import type {
	ChangePasswordParams,
	ChangePasswordResponse,
	UserOperationResponse,
	UserInfo,
	OrderRecordsParams,
	OrderRecordsResponse,
} from '~/types/user';
import type { ApiService } from '../api';

const API_PREFIX = '/user';

export function createUserService(apiService: ApiService) {
	return {
		// 获取用户信息
		getUserInfo(): Promise<UserInfo> {
			return apiService.get<UserInfo>(`${API_PREFIX}/info`);
		},

		// 修改密码
		changePassword(
			data: ChangePasswordParams
		): Promise<ChangePasswordResponse> {
			return apiService.post<ChangePasswordResponse>(
				`${API_PREFIX}/change-password`,
				data
			);
		},

		// 更新用户信息
		updateUserInfo(
			data: Partial<UserInfo>
		): Promise<UserOperationResponse> {
			return apiService.put<UserOperationResponse>(
				`${API_PREFIX}/update`,
				data
			);
		},

		// 获取订单记录
		getOrderRecords(
			data: OrderRecordsParams
		): Promise<OrderRecordsResponse> {
			return apiService.post<OrderRecordsResponse>(
				`${API_PREFIX}/order-records`,
				data
			);
		},
	};
}
