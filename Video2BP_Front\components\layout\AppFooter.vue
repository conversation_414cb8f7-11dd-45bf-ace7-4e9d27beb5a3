<template>
    <footer class="app-footer">
        <div class="footer-container container">
            <div class="footer-left">
                <div class="website-title">
                    <!-- <OptimizedImage src="/lingyuimage&logo.webp" alt="lingyulogo" :width="30" :height="30" /> -->
                    <img src="~/assets/images/lingyuimage&logo.webp" alt="lingyulogo">
                    <span>网站名称</span>
                </div>
                <div class="footer-links">
                    <div class="footer-link">用户协议</div>
                    <div class="footer-link">隐私协议</div>
                </div>
                <div class="copyright-info">
                    联系地址：请按实际情况填写联系地址，联系邮箱：<EMAIL>
                </div>
            </div>
            <div class="footer-right">
                <div class="social-icons">
                    <div class="social-icon">
                        <img src="~/assets/images/f1.webp" alt="">
                    </div>
                    <div class="social-icon">
                        <img src="~/assets/images/f2.webp" alt="">
                    </div>
                    <div class="social-icon">
                        <img src="~/assets/images/f3.webp" alt="">
                    </div>
                    <div class="social-icon">
                        <img src="~/assets/images/f4.webp" alt="">
                    </div>
                </div>
                <div class="icp-info">备案号ICP123456789123</div>
            </div>
        </div>
    </footer>
</template>

<script setup lang="ts">
defineOptions({
    name: 'AppFooter',
})
</script>

<style scoped lang="scss">
.app-footer {
    width: 100%;
    background-color: #000;
    color: #fff;
    padding: rem(64) rem(120) rem(64);
    user-select: none;

    .footer-container {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .footer-left {
        display: flex;
        flex-direction: column;
    }

    .website-title {
        display: flex;
        align-items: center;
        margin-bottom: rem(60);
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: bold;
        font-size: rem(36);
        color: #FFFFFF;
        line-height: rem(40);
        text-align: left;
        font-style: normal;

        img {
            width: rem(80) !important;
            height: rem(110) !important;
            margin-top: rem(5) !important;
            margin-right: rem(10) !important;
        }
    }

    .footer-links {
        display: flex;

        .footer-link {
            font-family: SourceHanSerifSC, SourceHanSerifSC;
            font-weight: bold;
            font-size: rem(18);
            color: #FFFFFF;
            line-height: rem(26);
            text-align: left;
            font-style: normal;
            cursor: pointer;
            margin-bottom: rem(48);

            &:nth-child(1) {
                margin-right: rem(48);
            }

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .copyright-info {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(18);
        color: rgba(255, 255, 255, 0.5);
        line-height: rem(26);
        text-align: left;
        font-style: normal;
    }

    .footer-right {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: rem(145);
    }

    .social-icons {
        display: flex;
        gap: rem(29);
        margin-bottom: rem(45);

        .social-icon {
            color: #fff;
            font-size: rem(20);

            img {
                width: rem(24);
                height: rem(24);
            }

            &:hover {
                opacity: 0.8;
            }
        }
    }

    .icp-info {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(18);
        color: rgba(255, 255, 255, 0.5);
        line-height: rem(26);
        text-align: center;
        font-style: normal;
    }
}
</style>
