from fastapi import APIRouter, Body, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_400_BAD_REQUEST

from apps.app_resources.schema import ResourceListResponseModel
from apps.app_resources.service import resource_service
from core.e import ErrorCode
from db.database import get_async_db


router = APIRouter()


@router.post(
    "/list",
    name="获取资源列表",
    response_model=ResourceListResponseModel,
)
async def get_resource_list(
    request: dict = Body({}),
    db: AsyncSession = Depends(get_async_db),
):
    """获取所有资源数据，无需参数"""
    try:
        resource_data = await resource_service.get_resource_list(db)
        return ResourceListResponseModel(
            code=0,
            message="success",
            data=resource_data
        )
    except HTTPException as e:
        return ResourceListResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return ResourceListResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_400_BAD_REQUEST)
