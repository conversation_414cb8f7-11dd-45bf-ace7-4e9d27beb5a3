<template>
  <div class="auth-content">
    <TabsHeader v-model="activeTab" :tabs="tabsList" @tab-change="handleTabChange" />

    <div class="auth-form-container">
      <form v-if="activeTab === 'personal'" class="custom-form" @submit.prevent="onPersonalLoginFinish">
        <div class="form-item" :class="{ 'has-error': personalErrors.account }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <PhoneOutlined />
            </span>
            <input type="text" v-model="personalLoginForm.account" placeholder="请输入手机号或邮箱" class="form-input"
              @blur="validatePersonalAccount" />
          </div>
          <div v-if="personalErrors.account" class="error-message">{{ personalErrors.account }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': personalErrors.password }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <LockOutlined />
            </span>
            <input :type="passwordVisible ? 'text' : 'password'" v-model="personalLoginForm.password"
              placeholder="请输入密码" class="form-input" @blur="validatePersonalPassword" />
            <span class="input-suffix" @click="passwordVisible = !passwordVisible">
              <EyeOutlined v-if="passwordVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="personalErrors.password" class="error-message">{{ personalErrors.password }}</div>
        </div>

        <div class="form-item">
          <button type="submit" class="submit-button">进入AI捕捉</button>
        </div>
      </form>

      <form v-else class="custom-form" @submit.prevent="onApiLoginFinish">
        <div class="form-item" :class="{ 'has-error': apiErrors.apiKey }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <PhoneOutlined />
            </span>
            <input type="text" v-model="apiLoginForm.apiKey" placeholder="API Key" class="form-input"
              @blur="validateApiKey" />
          </div>
          <div v-if="apiErrors.apiKey" class="error-message">{{ apiErrors.apiKey }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': apiErrors.apiSecret }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <LockOutlined />
            </span>
            <input :type="apiSecretVisible ? 'text' : 'password'" v-model="apiLoginForm.apiSecret"
              placeholder="API Secret" class="form-input" @blur="validateApiSecret" />
            <span class="input-suffix" @click="apiSecretVisible = !apiSecretVisible">
              <EyeOutlined v-if="apiSecretVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="apiErrors.apiSecret" class="error-message">{{ apiErrors.apiSecret }}</div>
        </div>

        <div class="form-item">
          <button type="submit" class="submit-button">进入AI捕捉</button>
        </div>
      </form>

      <div class="auth-form-footer">
        <div class="switch-auth-link">
          还没有账号?<a href="#" @click.prevent="handleSwitchToRegister">立即创建</a>
        </div>
        <div class="switch-auth-link">忘记密码</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { LockOutlined, PhoneOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'
import TabsHeader from './components/Header.vue'
import type { Tab } from './components/Header.vue'
import { useAuthStore } from '~/stores/auth'

const emit = defineEmits(['switchToRegister'])

// 密码可见性控制
const passwordVisible = ref(false)
const apiSecretVisible = ref(false)

//tab相关
const activeTab = ref('personal')
const tabsList = ref<Tab[]>([
  { key: 'personal', label: '账号登录' },
  // { key: 'api', label: 'API登录' },
])

// 处理标签页切换事件
const handleTabChange = (key: string) => {
  console.log('Tab changed to:', key)
}

//登录相关
const authStore = useAuthStore()

const personalLoginForm = reactive({
  account: '',
  password: '',
  rememberMe: false,
})

const apiLoginForm = reactive({
  apiKey: '',
  apiSecret: '',
  rememberMe: false,
})

// 表单验证错误信息
const personalErrors = reactive({
  account: '',
  password: '',
})

const apiErrors = reactive({
  apiKey: '',
  apiSecret: '',
})

// 验证函数
const validatePersonalAccount = () => {
  const phoneRegex = /^1[3-9]\d{9}$/
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

  if (!personalLoginForm.account) {
    personalErrors.account = '请输入手机号或邮箱!'
    return false
  } else if (!phoneRegex.test(personalLoginForm.account) && !emailRegex.test(personalLoginForm.account)) {
    personalErrors.account = '请输入有效的手机号或邮箱!'
    return false
  }
  personalErrors.account = ''
  return true
}

const validatePersonalPassword = () => {
  if (!personalLoginForm.password) {
    personalErrors.password = '请输入密码!'
    return false
  }
  personalErrors.password = ''
  return true
}

const validateApiKey = () => {
  if (!apiLoginForm.apiKey) {
    apiErrors.apiKey = '请输入API Key!'
    return false
  }
  apiErrors.apiKey = ''
  return true
}

const validateApiSecret = () => {
  if (!apiLoginForm.apiSecret) {
    apiErrors.apiSecret = '请输入API Secret!'
    return false
  }
  apiErrors.apiSecret = ''
  return true
}

const validatePersonalForm = () => {
  const accountValid = validatePersonalAccount()
  const passwordValid = validatePersonalPassword()
  return accountValid && passwordValid
}

const onPersonalLoginFinish = async () => {
  if (validatePersonalForm()) {
    const result = await authStore.login(personalLoginForm.account, personalLoginForm.password);
    if (result) {
      return navigateTo('/home')
    }
    message.error(authStore.authError ?? '登录失败，请检查用户名和密码')
  }
}

const onApiLoginFinish = () => {

}

const handleSwitchToRegister = () => {
  emit('switchToRegister')
}
</script>

<style lang="scss" scoped>
.auth-content {
  width: rem(500);
  padding: rem(60) rem(40);
  background-color: transparent;
  color: #e0e0e0;
  box-shadow: 0 rem(2) rem(8) 0 #d9e7ff;
  border-radius: rem(16);
  border: 1px solid #ffffff;
}

.custom-form {
  width: 100%;
}

.form-item {
  margin-bottom: rem(20);
  position: relative;

  &.has-error .form-input {
    border-color: #ff4d4f;
  }

  .error-message {
    color: #ff4d4f;
    font-size: rem(16);
    margin-top: rem(4);
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: 1px solid #555;
  border-radius: rem(4);
  height: rem(64);

  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 rem(2) rgba(24, 144, 255, 0.2);
  }
}

.input-prefix {
  display: flex;
  align-items: center;
  justify-content: center;
  height: rem(50);
  width: rem(40);
  color: #888;
  margin-left: rem(10);
  font-size: rem(20);
}

.input-suffix {
  display: flex;
  align-items: center;
  justify-content: center;
  height: rem(50);
  width: rem(40);
  color: #888;
  cursor: pointer;
  margin-right: rem(10);
}

.form-input {
  flex: 1;
  height: rem(50);
  background-color: transparent;
  border: none;
  outline: none;
  color: #e0e0e0;
  font-size: rem(22);
  padding: 0 rem(10);

  &::placeholder {
    color: #888;
  }

  &:focus {
    outline: none;
  }
}

.submit-button {
  width: 100%;
  height: rem(64);
  background-color: #0052cc;
  border: none;
  border-radius: rem(4);
  color: #ffffff;
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: bold;
  font-size: rem(24);
  line-height: rem(32);
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #0069d9;
  }

  &:active {
    background-color: #0062cc;
  }
}

.auth-form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: rem(16);
  margin-top: rem(10);

  .switch-auth-link {
    text-align: center;
    color: #a0a0a0;
  }

  .switch-auth-link a {
    color: #1890ff;
    font-weight: bold;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.input-group {
  display: flex;
  align-items: center;

  .code-input {
    flex: 1;
    border: 1px solid #555;
    border-radius: rem(4);
    height: rem(64);
    background-color: transparent;
    color: #e0e0e0;
    font-size: rem(16);
    padding: 0 rem(15);

    &::placeholder {
      color: #888;
    }

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 rem(2) rgba(24, 144, 255, 0.2);
    }
  }

  .code-button {
    margin-left: rem(10);
    height: rem(64);
    min-width: rem(120);
    background-color: #1890ff;
    border: none;
    border-radius: rem(4);
    color: white;
    font-size: rem(14);
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #40a9ff;
    }

    &:disabled {
      background-color: #d9d9d9;
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;
    }
  }
}

.custom-checkbox {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: rem(30);
  cursor: pointer;
  user-select: none;

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;

    &:checked~.checkmark {
      background-color: #1890ff;
      border-color: #1890ff;

      &:after {
        display: block;
      }
    }
  }

  .checkmark {
    position: absolute;
    left: 0;
    height: rem(18);
    width: rem(18);
    background-color: transparent;
    border: 1px solid #555;
    border-radius: rem(2);

    &:after {
      content: "";
      position: absolute;
      display: none;
      left: rem(6);
      top: rem(2);
      width: rem(5);
      height: rem(10);
      border: solid white;
      border-width: 0 rem(2) rem(2) 0;
      transform: rotate(45deg);
    }
  }

  .checkbox-text {
    color: #a0a0a0;
    font-size: rem(14);
  }
}

.checkbox-item {
  margin-top: rem(15);
}
</style>
