<template>
  <div class="support-page" :style="style">
    <div class="tp-title">支持多个主流软件及合适</div>
    <div class="tp-content">
      我们深知动画制作的核心在于流畅的创作体验。因此，我们支持3DMax、Motionbuilder、Maya等主流动画软件，确保您能够在熟悉的工作流程中，释放无限的创意潜力。现在，无论是BIP还是FBX或是其他您需要的格式，这些文件格式都能从千面完美输出。这意味着您可以自由地在不同的创作环境间穿梭，无需担心格式的壁垒。
    </div>
    <div class="tp-compony">
      <div class="pc-img">
        <template v-for="i in imgLength" :key="i">
          <img :src="i" alt="" />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import c1png from '~/assets/images/c1.webp'
import c2png from '~/assets/images/c2.webp'
import c3png from '~/assets/images/c3.webp'
import c4png from '~/assets/images/c4.webp'
import c5png from '~/assets/images/c5.webp'
import c6png from '~/assets/images/c6.webp'
import c7png from '~/assets/images/c7.webp'
defineProps({
  style: {
    type: Object,
    default: () => ({}),
  },
})

const imgLength = ref([
  c1png,
  c2png,
  c3png,
  c4png,
  c5png,
  c6png,
  c7png,
])
</script>

<style lang="scss" scoped>
.support-page {
  width: 100%;
  padding: rem(100) rem(120) 0;
  // background: linear-gradient(to bottom, #111016 0%, #111016 100%);
  background-image: url('~/assets/images/icon-bg.webp');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;

  .tp-title {
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: bold;
    font-size: rem(72);
    color: #eaa1ff;
    line-height: rem(104);
    text-align: center;
    font-style: normal;
    position: relative;
    margin-bottom: rem(95);

    &::after {
      content: '';
      width: rem(172);
      height: rem(10);
      background: #eaa1ff;
      border-radius: rem(1);
      position: absolute;
      left: 50%;
      bottom: rem(-20);
      transform: translateX(-50%);
    }
  }

  .tp-content {
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: 400;
    font-size: rem(40);
    color: #ffffff;
    line-height: rem(72);
    text-align: center;
    font-style: normal;
    margin-bottom: rem(63);
  }

  .tp-compony {
    padding-bottom: rem(170);

    .pc-img {
      height: rem(260);
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: rem(50);
      backdrop-filter: blur(rem(10));
      box-shadow: 0 rem(8) rem(32) rgba(0, 0, 0, 0.3);
      padding: rem(80) rem(160);
      display: flex;
      justify-content: space-between;

      img {
        width: rem(80);
        height: rem(80);
      }
    }
  }
}
</style>
