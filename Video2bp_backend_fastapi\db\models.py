"""
数据库模型定义
包含项目所需的所有数据表模型
"""

from sqlalchemy import Column, String, Integer, BigInteger, SmallInteger, Boolean, Text, DECIMAL, DateTime, JSON
from sqlalchemy.dialects.mysql import LONGTEXT
import datetime

from models.base import Base
from models.mixins import DateTimeModelMixin, SoftDeleteModelMixin


class UserOrders(Base["UserOrders"], DateTimeModelMixin):
    """用户订单表"""
    __tablename__ = "user_orders"

    user_id = Column(BigInteger, nullable=False, index=True, comment="用户ID")
    order_no = Column(String(64), unique=True, nullable=False, comment="订单号")
    order_type = Column(String(50), nullable=False, comment="订单类型")
    amount = Column(DECIMAL(10, 2), nullable=False, comment="订单金额")
    payment_method = Column(String(50), nullable=True, comment="支付方式")
    payment_status = Column(SmallInteger, default=0, comment="支付状态：0-未支付，1-已支付，2-已退款")
    combo_plan_id = Column(Integer, nullable=True, comment="套餐ID")
    description = Column(Text, nullable=True, comment="订单描述")
    paid_at = Column(DateTime, nullable=True, comment="支付时间")


class UserVipRecords(Base["UserVipRecords"], DateTimeModelMixin):
    """用户VIP记录表"""
    __tablename__ = "user_vip_records"

    user_id = Column(BigInteger, nullable=False, index=True, comment="用户ID")
    vip_level = Column(String(20), nullable=False, comment="VIP等级")
    start_date = Column(DateTime, nullable=False, comment="开始时间")
    end_date = Column(DateTime, nullable=False, comment="结束时间")
    order_id = Column(BigInteger, nullable=True, comment="关联订单ID")
    status = Column(SmallInteger, default=1, comment="状态：0-已过期，1-生效中")


class BusinessItems(Base["BusinessItems"], DateTimeModelMixin):
    """业务项目表"""
    __tablename__ = "business_items"

    title = Column(String(255), nullable=False, comment="业务标题")
    image_url = Column(String(500), nullable=False, comment="业务图片URL")
    details = Column(JSON, nullable=False, comment="业务详情列表")
    sort_order = Column(Integer, default=0, comment="排序")
    is_active = Column(Boolean, default=True, comment="是否启用")


class Products(Base["Products"], DateTimeModelMixin):
    """产品表"""
    __tablename__ = "products"

    title = Column(String(255), nullable=False, comment="产品标题")
    description = Column(Text, nullable=False, comment="产品描述")
    video_url = Column(String(500), nullable=False, comment="产品演示视频URL")
    thumbnail_url = Column(String(500), nullable=True, comment="缩略图URL")
    sort_order = Column(Integer, default=0, comment="排序")
    is_active = Column(Boolean, default=True, comment="是否启用")


class UserCases(Base["UserCases"], DateTimeModelMixin):
    """用户案例表"""
    __tablename__ = "user_cases"

    user_name = Column(String(100), nullable=False, comment="用户姓名")
    avatar_url = Column(String(500), nullable=False, comment="用户头像URL")
    time_label = Column(String(20), nullable=False, comment="时间标识")
    content = Column(Text, nullable=False, comment="案例内容描述")
    preview_url = Column(String(500), nullable=False, comment="案例预览视频URL")
    sort_order = Column(Integer, default=0, comment="排序")
    is_active = Column(Boolean, default=True, comment="是否启用")


class Projects(Base["Projects"], DateTimeModelMixin):
    """项目展示表"""
    __tablename__ = "projects"

    title = Column(String(255), nullable=False, comment="项目标题")
    description = Column(Text, nullable=False, comment="项目描述")
    image_url = Column(String(500), nullable=False, comment="项目图片URL")
    category = Column(String(100), nullable=False, comment="项目分类")
    tags = Column(JSON, nullable=False, comment="项目标签")
    sort_order = Column(Integer, default=0, comment="排序")
    is_active = Column(Boolean, default=True, comment="是否启用")


class Resources(Base["Resources"], DateTimeModelMixin):
    """资源下载表"""
    __tablename__ = "resources"

    title = Column(String(255), nullable=False, comment="资源标题")
    description = Column(Text, nullable=False, comment="资源描述")
    resource_type = Column(String(50), nullable=False, comment="资源类型")
    file_url = Column(String(500), nullable=False, comment="资源链接")
    thumbnail_url = Column(String(500), nullable=True, comment="缩略图URL")
    file_size = Column(String(20), nullable=True, comment="文件大小")
    file_format = Column(String(20), nullable=True, comment="文件格式")
    download_count = Column(Integer, default=0, comment="下载次数")
    sort_order = Column(Integer, default=0, comment="排序")
    is_active = Column(Boolean, default=True, comment="是否启用")


class CompanyInfo(Base["CompanyInfo"], DateTimeModelMixin):
    """公司信息表"""
    __tablename__ = "company_info"

    name = Column(String(255), nullable=False, comment="公司名称")
    description = Column(Text, nullable=True, comment="公司描述")
    address = Column(String(500), nullable=True, comment="公司地址")
    phone = Column(String(50), nullable=True, comment="联系电话")
    email = Column(String(100), nullable=True, comment="联系邮箱")
    website = Column(String(255), nullable=True, comment="官方网站")
    logo_url = Column(String(500), nullable=True, comment="公司Logo URL")
    is_active = Column(Boolean, default=True, comment="是否启用")


class ComboPlans(Base["ComboPlans"], DateTimeModelMixin):
    """套餐计划表"""
    __tablename__ = "combo_plans"

    name = Column(String(100), nullable=False, comment="套餐名称")
    description = Column(Text, nullable=True, comment="套餐描述")
    price = Column(DECIMAL(10, 2), nullable=False, comment="套餐价格")
    duration_days = Column(Integer, nullable=False, comment="有效期天数")
    features = Column(JSON, nullable=False, comment="套餐功能列表")
    sort_order = Column(Integer, default=0, comment="排序")
    is_active = Column(Boolean, default=True, comment="是否启用")


class ContactSubmissions(Base["ContactSubmissions"], DateTimeModelMixin):
    """联系表单提交记录表"""
    __tablename__ = "contact_submissions"

    phone = Column(String(20), nullable=False, comment="手机号")
    company_name = Column(String(255), nullable=False, comment="公司名称")
    project = Column(String(255), nullable=False, comment="咨询项目")
    status = Column(SmallInteger, default=0, comment="处理状态：0-未处理，1-已处理")
    remark = Column(Text, nullable=True, comment="备注")


class VideoProcessRecords(Base["VideoProcessRecords"], DateTimeModelMixin):
    """视频处理记录表"""
    __tablename__ = "video_process_records"

    user_id = Column(BigInteger, nullable=False, index=True, comment="用户ID")
    video_id = Column(BigInteger, nullable=False, comment="视频ID")
    file_record_id = Column(String(100), nullable=True, comment="文件记录ID")
    project_id = Column(String(100), nullable=True, comment="项目ID")
    start_time = Column(DECIMAL(10, 3), nullable=True, comment="开始时间")
    end_time = Column(DECIMAL(10, 3), nullable=True, comment="结束时间")
    duration = Column(DECIMAL(10, 3), nullable=True, comment="时长")
    was_trimmed = Column(Boolean, default=False, comment="是否被裁剪")
    output_format = Column(SmallInteger, nullable=False, comment="输出格式：1-4")
    original_selection = Column(JSON, nullable=True, comment="原始选择")
    form_data = Column(JSON, nullable=True, comment="表单数据")
    process_status = Column(SmallInteger, default=0, comment="处理状态：0-待处理，1-处理中，2-已完成，3-失败")
    progress = Column(SmallInteger, default=0, comment="处理进度：0-100")
    error_message = Column(Text, nullable=True, comment="错误信息")
    result_file_path = Column(String(500), nullable=True, comment="结果文件路径")
