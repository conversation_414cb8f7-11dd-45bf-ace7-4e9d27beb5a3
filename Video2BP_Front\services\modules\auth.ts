import type { LoginParams, LoginResponse, RegisterParams } from '~/types/auth';
import type { UserInfo } from '~/types/user';
import type { ApiService } from '../api';

const API_PREFIX = '/user'; // 该模块的 API 前缀

// 使用工厂函数模式
export function createAuthService(apiService: ApiService) {
	return {
		login(data: LoginParams): Promise<LoginResponse> {
			return apiService.post<LoginResponse>(`${API_PREFIX}/login`, data);
		},

		logout(): Promise<LoginResponse> {
			return apiService.post<LoginResponse>(`${API_PREFIX}/logout`);
		},

		register(data: RegisterParams): Promise<LoginResponse> {
			return apiService.post<LoginResponse>(
				`${API_PREFIX}/register`,
				data
			);
		},

		sendEmailVerificationCode(
			email: string
		): Promise<{ success: boolean; data: Object; message: string }> {
			return apiService.post<{
				success: boolean;
				data: Object;
				message: string;
			}>(`${API_PREFIX}/send_verification_code`, { email });
		},
	};
}
