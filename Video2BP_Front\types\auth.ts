// <!---- 接口相关 !---->
export interface LoginParams {
	email: string;
	password: string;
}

export interface LoginResponse {
	code: number;
	message: string;
	data: {
		token: string;
		user?: {
			id: string;
			username: string;
			role: string;
		};
	};
}
export interface RegisterParams {
	email: string;
	code: string;
	password: string;
}

// <!---- 页面相关 !---->
export interface PersonalRegisterFormData {
	email: string;
	verificationCode: string;
	password: string;
	confirmPassword: string;
	invitationCode: string;
	agreeToTerms: boolean;
	subscribeToUpdates: boolean;
}

export interface ApiRegisterFormData {
	email: string;
	verificationCode: string;
	password: string;
	confirmPassword: string;
	companyName: string;
	industry: string;
	commonSoftware: string;
	phoneNumber: string;
	agreeToTerms: boolean;
	subscribeToUpdates: boolean;
}
