@use '~/assets/styles/utils.scss' as *;
@use '~/assets/styles/variables.scss' as *;
//封装一个样式 width: 100%; max-width: 1680px; margin: 0 auto;
@mixin container {
//   width: 100%;
//   max-width: 1680px;
//   margin: 0 auto;
}

//按钮样式
@mixin action-btn {
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: 400;
    font-size: rem(18);
    color: #FFFFFF;
    line-height: rem(26);
    text-align: right;
    font-style: normal;
    border-radius: rem(6);
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    height: rem(48);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 rem(20);

    .anticon-save {
        margin-right: rem(8);
    }

    &.primary {
        background: #1890ff;
        color: white;

        &:hover {
            background: #40a9ff;
        }
    }

    &.secondary {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: rem(1) solid rgba(255, 255, 255, 0.3);

        &:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    }
    &.cut {
        background: linear-gradient(135deg, #ff6b35, #ff8e53);
        border: 1px solid #ff6b35;

        &:hover {
            background: linear-gradient(135deg, #ff8e53, #ff6b35);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }   
    }
}

// ============================================
// 页面公共样式混合器
// ============================================

// 加载状态样式
@mixin loading-container {
    width: 100%;
    min-height: rem(400);
    display: flex;
    align-items: center;
    justify-content: center;
}


// 错误状态容器样式
@mixin error-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: rem(400);
}

// 错误状态内容样式
@mixin error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: rem(20);

    .error-icon {
        font-size: rem(48);
    }

    .error-text {
        color: $text-primary-dark;
        font-family: $font-family-primary;
        font-size: rem(18);
        font-weight: 400;
        text-align: center;
    }

    .retry-btn {
        padding: rem(12) rem(24);
        background: $gradient-primary;
        border: none;
        border-radius: rem(4);
        color: $text-primary-dark;
        font-family: $font-family-primary;
        font-size: rem(16);
        font-weight: 400;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(rem(-2));
            box-shadow: 0 rem(4) rem(12) rgba(0, 145, 255, 0.3);
        }
    }
}

// 分页容器样式
@mixin pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: rem(50);
}

// 分页组件样式
@mixin pagination-styles {
    :deep(.ant-pagination) {
        .ant-pagination-item {
            background: $pagination-bg;
            border: 1px solid $pagination-border;
            border-radius: rem(4);

            a {
                color: $text-primary-dark;
                font-family: $font-family-primary;
                font-weight: 400;
            }

            &:hover {
                background: $pagination-hover-bg;
                border-color: $pagination-hover-border;
            }

            &.ant-pagination-item-active {
                background: $gradient-primary;
                border-color: transparent;

                a {
                    color: $text-primary-dark;
                    font-weight: bold;
                }
            }
        }

        .ant-pagination-prev,
        .ant-pagination-next,
        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
            background: $pagination-bg;
            border: 1px solid $pagination-border;
            border-radius: rem(4);

            .anticon {
                color: $text-primary-dark;
            }

            &:hover {
                background: $pagination-hover-bg;
                border-color: $pagination-hover-border;
            }

            &.ant-pagination-disabled {
                background: $pagination-disabled-bg;
                border-color: $pagination-disabled-border;

                .anticon {
                    color: $pagination-disabled-text;
                }
            }
        }

        .ant-pagination-total-text {
            color: $text-primary-dark;
            font-family: $font-family-primary;
            font-weight: 400;
        }
    }
}