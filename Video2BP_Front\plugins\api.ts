import { createApiService } from '~/services/api';
import * as userService from '~/services/modules/user';
import * as authService from '~/services/modules/auth';
import * as videoService from '~/services/modules/capture';

export default defineNuxtPlugin((nuxtApp) => {
	const config = useRuntimeConfig();

	// 创建API服务实例
	const apiService = createApiService(config.public.apiBase);

	// 提供API服务给应用
	return {
		provide: {
			api: apiService,
			userService,
			authService,
			videoService,
		},
	};
});
