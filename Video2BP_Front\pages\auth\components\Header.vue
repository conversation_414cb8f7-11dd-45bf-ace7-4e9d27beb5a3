<template>
  <div class="tabs-header">
    <div v-for="tab in tabs" :key="tab.key" :class="['tab-item', { active: modelValue === tab.key }]"
      @click="updateActiveTab(tab.key)">
      {{ tab.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'TabsHeader',
})

export interface Tab {
  key: string
  label: string
}

// 定义组件的属性
defineProps<{
  tabs: Tab[]
  // 当前激活的标签页，使用 v-model
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'tabChange', value: string): void
}>()

const updateActiveTab = (key: string) => {
  emit('update:modelValue', key)
  emit('tabChange', key)
}
</script>

<style scoped lang="scss">
.tabs-header {
  width: 80%;
  display: flex;
  margin: 0 auto;
  margin-bottom: rem(55);
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: rem(20);
  line-height: rem(40);
  color: #999;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: bold;
  font-size: rem(36);
  line-height: rem(38);
  font-style: normal;
}

.tab-item:hover {
  opacity: 0.9;
}

.tab-item.active {
  color: #ffffff;

  &:after {
    content: '';
    position: absolute;
    bottom: rem(-15);
    left: 50%;
    width: 90%;
    transform: translateX(-50%);
    background-color: #0a84ff;
    height: rem(4);
    border-radius: rem(1);
  }
}
</style>
