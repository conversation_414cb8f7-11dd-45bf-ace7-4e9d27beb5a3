import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';
import { ref, readonly } from 'vue';

interface VideoProcessorOptions {
	onProgress?: (progress: number) => void;
	onLog?: (message: string) => void;
}

interface TrimOptions {
	startTime: number;
	endTime: number;
	outputFormat?: string;
}

export const useVideoProcessor = () => {
	const ffmpeg = ref<FFmpeg | null>(null);
	const isLoaded = ref(false);
	const isLoading = ref(false);

	// 初始化 FFmpeg
	const loadFFmpeg = async () => {
		if (isLoaded.value || isLoading.value) return;

		try {
			isLoading.value = true;

			const ffmpegInstance = new FFmpeg();

			// 设置日志回调
			ffmpegInstance.on('log', ({ message }) => {
				console.log('FFmpeg:', message);
			});

			// 加载 FFmpeg 核心文件
			const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/esm';
			await ffmpegInstance.load({
				coreURL: await toBlobURL(
					`${baseURL}/ffmpeg-core.js`,
					'text/javascript'
				),
				wasmURL: await toBlobURL(
					`${baseURL}/ffmpeg-core.wasm`,
					'application/wasm'
				),
			});

			ffmpeg.value = ffmpegInstance;
			isLoaded.value = true;
			console.log('FFmpeg 加载完成');
		} catch (error) {
			console.error('FFmpeg 加载失败:', error);
			throw new Error('FFmpeg 加载失败');
		} finally {
			isLoading.value = false;
		}
	};

	// 截取视频片段
	const trimVideo = async (
		file: File,
		options: TrimOptions,
		callbacks?: VideoProcessorOptions
	): Promise<File> => {
		if (!ffmpeg.value) {
			await loadFFmpeg();
		}

		if (!ffmpeg.value) {
			throw new Error('FFmpeg 未初始化');
		}

		try {
			const { startTime, endTime, outputFormat = 'mp4' } = options;
			const duration = endTime - startTime;

			if (duration <= 0) {
				throw new Error('截取时间无效');
			}

			callbacks?.onLog?.('开始处理视频文件...');

			// 设置进度回调
			// if (callbacks?.onProgress) {
			//   ffmpeg.value.on('progress', ({ progress }) => {
			//     callbacks.onProgress?.(Math.round(progress * 100));
			//   });
			// }

			// 写入输入文件
			const inputFileName = `input.${file.name.split('.').pop() || 'mp4'}`;
			const outputFileName = `output.${outputFormat}`;

			await ffmpeg.value.writeFile(inputFileName, await fetchFile(file));
			callbacks?.onLog?.('文件写入完成，开始截取...');
			callbacks?.onProgress?.(30);

			// 执行截取命令
			await ffmpeg.value.exec([
				'-i',
				inputFileName,
				'-ss',
				startTime.toString(),
				'-t',
				duration.toString(),
				'-c',
				'copy', // 使用流复制，避免重新编码
				'-avoid_negative_ts',
				'make_zero',
				outputFileName,
			]);

			callbacks?.onLog?.('视频截取完成，正在生成文件...');
			callbacks?.onProgress?.(80);

			// 读取输出文件
			const outputData = await ffmpeg.value.readFile(outputFileName);

			// 创建新的 File 对象
			const outputBlob = new Blob([outputData], {
				type: `video/${outputFormat}`,
			});
			const trimmedFile = new File([outputBlob], `trimmed_${file.name}`, {
				type: `video/${outputFormat}`,
			});

			// 清理临时文件
			try {
				await ffmpeg.value.deleteFile(inputFileName);
				await ffmpeg.value.deleteFile(outputFileName);
			} catch (cleanupError) {
				console.warn('清理临时文件失败:', cleanupError);
			}

			callbacks?.onLog?.('视频处理完成');
			callbacks?.onProgress?.(100);
			return trimmedFile;
		} catch (error) {
			console.error('视频截取失败:', error);
			throw new Error(`视频截取失败: ${error}`);
		}
	};

	// 检查是否需要截取
	const needsTrimming = (
		startTime: number,
		endTime: number,
		totalDuration: number
	): boolean => {
		const tolerance = 0.1; // 0.1秒的容差

		// 如果开始和结束时间都是0，说明用户没有设置选择范围
		if (startTime === 0 && endTime === 0) {
			return false;
		}

		// 如果截取时长无效（小于等于0），不需要截取
		if (endTime - startTime <= 0) {
			return false;
		}

		// 检查是否接近完整视频（开始接近0，结束接近总时长）
		return !(
			Math.abs(startTime) < tolerance &&
			Math.abs(endTime - totalDuration) < tolerance
		);
	};

	// 获取视频信息
	const getVideoInfo = async (file: File) => {
		return new Promise<{ duration: number; width: number; height: number }>(
			(resolve, reject) => {
				const video = document.createElement('video');
				video.preload = 'metadata';

				video.onloadedmetadata = () => {
					resolve({
						duration: video.duration,
						width: video.videoWidth,
						height: video.videoHeight,
					});
					URL.revokeObjectURL(video.src);
				};

				video.onerror = () => {
					reject(new Error('无法读取视频信息'));
					URL.revokeObjectURL(video.src);
				};

				video.src = URL.createObjectURL(file);
			}
		);
	};

	return {
		ffmpeg: readonly(ffmpeg),
		isLoaded: readonly(isLoaded),
		isLoading: readonly(isLoading),
		loadFFmpeg,
		trimVideo,
		needsTrimming,
		getVideoInfo,
	};
};
