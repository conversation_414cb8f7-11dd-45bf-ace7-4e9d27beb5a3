<template>
  <div class="error-wrapper">
    <div class="error-content">
      <div class="error-icon">{{ icon }}</div>
      <div class="error-text">{{ message }}</div>
      <button v-if="showRetry" class="retry-btn" @click="handleRetry">
        {{ retryText }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  message: string
  icon?: string
  showRetry?: boolean
  retryText?: string
}

interface Emits {
  (e: 'retry'): void
}

const props = withDefaults(defineProps<Props>(), {
  icon: '⚠️',
  showRetry: true,
  retryText: '重试',
})

const emit = defineEmits<Emits>()

// 处理重试点击
const handleRetry = () => {
  emit('retry')
}
</script>

<style lang="scss" scoped>
.error-wrapper {
  @include error-wrapper;
}

.error-content {
  @include error-content;
}
</style>
