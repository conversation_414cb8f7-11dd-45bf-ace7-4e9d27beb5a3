from service.mysql_client import DBSessionContext
from models.user_model import User
from flask_mail import Message
from flask import current_app
import random
import string
from utils.exceptions import UserError, LoginError
from utils.publicMethod import global_str_md5
from service.dbRedis.redisClient import get_client
import uuid
from settings import WEB_TOKEN_EXP_TIME, MAIL_USERNAME
from loguru import logger


# 模拟邮箱验证码发送
def send_email_verification_code(email):
    code = "".join(random.choices(string.digits, k=6))
    
    msg = Message("您的验证码",
                    sender=MAIL_USERNAME,
                    recipients=[email])
    msg.body = f"您的验证码是: {code}，5分钟内有效"
    with current_app.app_context():
        mail = current_app.extensions["mail"]
    mail.send(msg)
    
    logger.info(f"验证码 {code} 已发送到邮箱 {email}")
    # 将验证码存入 Redis，设置过期时间为 5 分钟
    redis_client = get_client()
    redis_client.set(f"email_verification:{email}", code, ex=300)
    return code


# 验证邮箱验证码
def verify_email_verification_code(email, code):
    redis_client = get_client()
    stored_code = redis_client.get(f"email_verification:{email}")
    return stored_code == code if stored_code else False


# 检查邮箱是否已注册
def check_email_registered(email):
    with DBSessionContext() as db_session:
        user = db_session.query(User).filter_by(email=email).first()
        return bool(user)


# 用户注册
def user_register(email, password):
    if check_email_registered(email):
        raise UserError(UserError.USER_EXISTS)
    with DBSessionContext() as db_session:
        new_user = User(email=email, password=global_str_md5(password))
        db_session.add(new_user)


# 用户登录
def user_login(email, password):
    with DBSessionContext() as db_session:
        user = db_session.query(User).filter_by(email=email, password=global_str_md5(password)).first()
        if not user:
            raise LoginError(LoginError.LOGIN_FAILED)
        token = generate_token(user.id)
        return user.id, token, user.email


# 用户信息
def user_info(user_id):
    with DBSessionContext() as db_session:
        user = db_session.query(User).filter_by(id=user_id).first()
        if not user:
            raise UserError(UserError.USER_NOT_FOUND)
        res = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "phone": user.phone,
            "status": user.status,
            "vipLevel": user.vip_level,
        }
        return res


# 生成 token
def generate_token(user_id):
    token = str(uuid.uuid4())
    redis_client = get_client()
    redis_client.setex(f"user_token:{user_id}", WEB_TOKEN_EXP_TIME, token)
    return token


# 验证 token
def verify_token(user_id, token):
    redis_client = get_client()
    stored_token = redis_client.get(f"user_token:{user_id}")
    return stored_token and stored_token.decode("utf-8") == token

