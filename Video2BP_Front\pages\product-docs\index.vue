<template>
  <div class="product-page">
    <div class="product-content">
      <div class="features-container">
        <div class="feature-card large">
          <div class="feature-image">
            <img :src="features[0].image" alt="AI" />
          </div>
          <div class="feature-title">{{ features[0].title }}</div>
        </div>

        <div class="middle-column">
          <div class="feature-card medium" v-for="feature in features.slice(1, 3)" :key="feature.id">
            <div class="feature-image">
              <img :src="feature.image" alt="feature" />
            </div>
            <div class="feature-title">{{ feature.title }}</div>
          </div>
        </div>

        <div class="right-column">
          <div class="feature-card small" v-for="(link, index) in productLinks" :key="link.id">
            <div class="link-content">
              <div class="link-icon" v-if="index !== 0">
                <img :src="link.icon" alt="icon" />
              </div>
              <div class="link-title">{{ link.title }}</div>
            </div>
            <div class="link-arrow" v-if="index !== 0">
              <img :src="link.link" alt="icon" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ProductFeature, ProductLink } from '~/types/product'
import documentLeftBg from '~/assets/images/document-left-bg.webp'
import documentCenterTopBg from '~/assets/images/document-center-top-bg.webp'
import documentCenterBottomBg from '~/assets/images/document-center-top-bottom.webp'
import documentRightTopBg from '~/assets/images/document-right-top-bg.webp'
import documentRightIconTop from '~/assets/images/document-right-icon-top.webp'
import documentRightIconBot from '~/assets/images/document-right-icon-bot.webp'
import documentRightIconArrow from '~/assets/images/document-document-right-icon-arrow.webp'

defineOptions({
  name: 'ProductDocsView',
})

const features = ref<ProductFeature[]>([
  {
    id: 1,
    title: '自动化',
    image: documentLeftBg,
  },
  {
    id: 2,
    title: '更效率',
    image: documentCenterTopBg,
  },
  {
    id: 3,
    title: '更简单',
    image: documentCenterBottomBg,
  },
])

const productLinks = ref<ProductLink[]>([
  {
    id: 1,
    title: '售后保障',
    icon: documentRightTopBg,
    link: documentRightIconArrow,
  },
  {
    id: 2,
    title: '产品简介',
    icon: documentRightIconTop,
    link: documentRightIconArrow,
  },
  {
    id: 3,
    title: '使用帮助',
    icon: documentRightIconBot,
    link: documentRightIconArrow,
  },
])
</script>

<style scoped lang="scss">
.product-page {
  width: 100%;
  // min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('~/assets/images/upgrade-bg.webp');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;

  .product-content {
    // flex: 1;
    min-height: calc(100vh - rem(403));
    padding: rem(188) rem(120) rem(120);
    max-width: rem(1920);
    margin: 0 auto;

    .features-container {
      display: grid;
      grid-template-columns: 1fr 1fr rem(432);
      gap: rem(24);
      height: rem(772);

      .feature-card {
        position: relative;

        overflow: hidden;

        &.large {
          height: 100%;

          .feature-image {
            height: 100%;
          }
        }

        .feature-image {
          position: relative;
          height: 100%;
          width: 100%;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .feature-title {
          position: absolute;
          bottom: rem(40);
          left: rem(40);
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: bold;
          font-size: rem(48);
          color: #ffffff;
          line-height: rem(48);
          text-shadow: 0px rem(2) rem(4) rgba(0, 0, 0, 0.5);
        }
      }

      .middle-column {
        display: grid;
        grid-template-rows: 60.62% 36.27%;
        gap: rem(24);

        .feature-card {
          &:first-child {
            height: 100%;
            /* 468px (60.62% of 772px) */
          }

          &:last-child {
            height: 100%;
            /* 280px (36.27% of 772px) */
          }
        }
      }

      .right-column {
        display: flex;
        flex-direction: column;
        gap: rem(24);

        .feature-card {
          height: calc(33.33% - 16px);
          padding: 0 rem(40);
          display: flex;
          align-items: center;
          justify-content: space-between;
          position: relative;

          &:nth-child(1) {
            background-image: url('~/assets/images/document-right-top-bg.webp');
            background-size: 100% 100%;
            background-repeat: no-repeat;

            .link-content {
              position: absolute;
              left: rem(40);
              bottom: rem(35);
            }
          }

          &:nth-child(2),
          &:nth-child(3) {
            border: rem(2) solid transparent;
            border-image: linear-gradient(to bottom,
                rgba(255, 255, 255, 0.5) 0%,
                /* 顶部可见 */
                rgba(255, 255, 255, 0.3) 30%,
                /* 逐渐变淡 */
                rgba(255, 255, 255, 0.2) 60%,
                /* 更淡 */
                transparent 100%
                /* 底部完全透明 */
              ) 1;

            .link-content {
              position: absolute;
              left: rem(32);
              top: rem(29);
            }
          }

          .link-content {
            display: flex;
            align-items: center;
            gap: rem(16);

            .link-icon {
              width: rem(40);
              height: rem(40);

              img {
                width: 100%;
                height: 100%;
              }
            }

            .link-title {
              font-family: SourceHanSerifSC, SourceHanSerifSC;
              font-weight: bold;
              font-size: rem(40);
              color: #ffffff;
              line-height: rem(57);
              text-align: left;
              font-style: normal;
            }
          }

          .link-arrow {
            position: absolute;
            right: rem(24);
            bottom: rem(25);

            img {
              width: rem(100);
              height: rem(20);
            }
          }
        }
      }
    }
  }
}
</style>
