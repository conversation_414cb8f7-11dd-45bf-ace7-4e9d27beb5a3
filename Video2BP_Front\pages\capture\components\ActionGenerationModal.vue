<template>
  <a-modal v-model:open="visible" title="" :width="800" :closable="!props.isUploading" :ok-text="processingText"
    cancel-text="取消" :mask-closable="false" :confirm-loading="props.isUploading" class="action-generation-modal"
    @cancel="handleCancel" @ok="handleStartGeneration">
    <div class="modal-content">
      <!-- 左侧3D展示区域 -->
      <div class="left-section">
        <div class="model-display">
          <div class="model-placeholder">
            <div class="placeholder-text">3D数字人物展示</div>
          </div>
        </div>
      </div>

      <!-- 右侧配置面板 -->
      <div class="right-section">
        <div class="config-panel">
          <!-- 标题 -->
          <div class="panel-title">动作生成</div>

          <!-- 文件名称 -->
          <!-- <div class="form-item">
            <label class="form-label">文件名称</label>
            <a-input v-model:value="formData.fileName" placeholder="请输入文件名称" class="form-input" />
          </div> -->

          <!-- 功能选择 -->
          <div class="form-item">
            <label class="form-label">功能选择</label>
            <div class="button-group">
              <a-button v-for="option in functionOptions" :key="option.value"
                :type="formData.selectedFunction === option.value ? 'primary' : 'default'" class="function-btn"
                @click="selectFunction(option.value)">
                {{ option.label }}
              </a-button>
            </div>
          </div>

          <!-- 第一帧Pose -->
          <div class="form-item">
            <label class="form-label">第一帧Pose</label>
            <div class="pose-options">
              <a-button v-for="pose in poseOptions" :key="pose.value"
                :type="formData.selectedPose === pose.value ? 'primary' : 'default'" class="pose-btn"
                @click="selectPose(pose.value)">
                {{ pose.label }}
              </a-button>
            </div>
          </div>

          <!-- 防穿模介入 -->
          <div class="form-item">
            <label class="form-label">防穿模介入</label>
            <div class="switch-container">
              <a-switch v-model:checked="formData.antiClipping" />
              <span class="switch-label">{{ formData.antiClipping ? '开启' : '关闭' }}</span>
            </div>
          </div>

          <!-- 输出格式 -->
          <div class="form-item">
            <label class="form-label">输出格式</label>
            <a-select v-model:value="formData.outputFormat" class="form-select" placeholder="请选择输出格式">
              <a-select-option value="fbx">Boy FBX</a-select-option>
              <a-select-option value="gif">GIF</a-select-option>
              <a-select-option value="mp4">MP4</a-select-option>
            </a-select>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  open: boolean
  isUploading?: boolean
  uploadProgress?: number
}

const props = defineProps<Props>()

// 计算处理状态文本
const processingText = computed(() => {
  if (!props.isUploading) return '开始制作'

  const progress = props.uploadProgress || 0
  if (progress <= 70) {
    return `视频截取中 ${progress}%`
  } else {
    return `上传中 ${progress}%`
  }
})

const emit = defineEmits<{
  'update:open': [value: boolean]
  'start-generation': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

// 表单数据
const formData = ref({
  fileName: '2970308360-1-16',
  selectedFunction: 'body',
  selectedPose: 'default',
  antiClipping: false,
  outputFormat: 'fbx'
})

// 功能选项
const functionOptions = [
  { label: '身体姿态', value: 'body' },
  { label: '手部姿态', value: 'hand' },
  { label: '自定义姿态', value: 'custom' },
  { label: '自动姿态', value: 'auto' }
]

// Pose选项
const poseOptions = [
  { label: '默认Pose', value: 'default' },
  { label: 'T-Pose', value: 't-pose' },
  { label: 'A-Pose', value: 'a-pose' }
]

// 方法
const selectFunction = (value: string) => {
  formData.value.selectedFunction = value
}

const selectPose = (value: string) => {
  formData.value.selectedPose = value
}

const handleCancel = () => {
  visible.value = false
}

const handleStartGeneration = () => {
  // 如果正在上传，不允许重复提交
  if (props.isUploading) {
    return
  }
  console.log('开始制作:', formData.value)
  emit('start-generation', formData.value)

}
</script>
<style lang="scss">
.action-generation-modal {
  .ant-modal-content {
    background: rgba(42, 40, 68, 0.95);
    border-radius: rem(12);
    padding: 0;
  }

  .ant-modal-header {
    display: none;
  }

  .ant-modal-body {
    padding: 0;
  }

  .ant-modal-footer {
    padding: rem(20);
    margin-top: 0;

    button {
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: 400;
      font-size: rem(18);
      line-height: rem(26);
      border-radius: rem(6);
      border: none;
      cursor: pointer;
      transition: all 0.2s;
      height: rem(48);
      padding: 0 rem(20);
    }

    .ant-btn-primary {
      background: $primary-color-dark;

      &:hover {
        background: $primary-color-dark;
        opacity: 0.8;
      }
    }
  }

  .ant-modal-close {
    color: #ffffff;
    font-size: rem(18);

    &:hover {
      color: $primary-color-dark;
    }
  }
}
</style>
<style lang="scss" scoped>
.modal-content {
  display: flex;
  border-radius: rem(12);
  overflow: hidden;

  .left-section {
    width: 40%;
    background: rgba(30, 30, 40, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;

    .model-display {
      width: 100%;
      height: 100%;
      position: relative;

      .model-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, rgba(0, 145, 255, 0.1), rgba(0, 212, 255, 0.1));
        border: rem(2) dashed rgba(255, 255, 255, 0.2);

        .placeholder-text {
          color: rgba(255, 255, 255, 0.6);
          font-size: rem(18);
          font-weight: 500;
        }
      }
    }
  }

  .right-section {
    flex: 1;
    background: rgba(42, 40, 68, 0.9);
    padding: rem(32) rem(24);
    border-left: 1px solid rgba(255, 255, 255, 0.1);

    .config-panel {
      height: 100%;
      display: flex;
      flex-direction: column;

      .panel-title {
        font-size: rem(24);
        font-weight: 600;
        color: #ffffff;
        margin-bottom: rem(32);
        text-align: center;
      }

      .form-item {
        margin-bottom: rem(24);

        .form-label {
          display: block;
          color: #ffffff;
          font-size: rem(14);
          font-weight: 500;
          margin-bottom: rem(8);
        }

        .form-input {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: rem(6);
          color: #ffffff;

          &:focus {
            border-color: #0091ff;
            box-shadow: 0 0 0 2px rgba(0, 145, 255, 0.2);
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .button-group {
          display: flex;
          gap: rem(8);
          flex-wrap: wrap;

          .function-btn {
            flex: 1;
            min-width: rem(80);
            height: rem(36);
            font-size: rem(12);
            border-radius: rem(6);

            &.ant-btn-default {
              background: rgba(255, 255, 255, 0.1);
              border-color: rgba(255, 255, 255, 0.2);
              color: #ffffff;

              &:hover {
                background: rgba(0, 145, 255, 0.2);
                border-color: #0091ff;
                color: #0091ff;
              }
            }

            &.ant-btn-primary {
              background: linear-gradient(135deg, #0091ff, #00d4ff);
              border-color: #0091ff;
            }
          }
        }

        .pose-options {
          display: flex;
          gap: rem(8);

          .pose-btn {
            flex: 1;
            height: rem(36);
            font-size: rem(12);
            border-radius: rem(6);

            &.ant-btn-default {
              background: rgba(255, 255, 255, 0.1);
              border-color: rgba(255, 255, 255, 0.2);
              color: #ffffff;

              &:hover {
                background: rgba(0, 145, 255, 0.2);
                border-color: #0091ff;
                color: #0091ff;
              }
            }

            &.ant-btn-primary {
              background: linear-gradient(135deg, #0091ff, #00d4ff);
              border-color: #0091ff;
            }
          }
        }

        .switch-container {
          display: flex;
          align-items: center;
          gap: rem(12);

          .switch-label {
            color: #ffffff;
            font-size: rem(14);
          }
        }

        .form-select {
          width: 100%;

          :deep(.ant-select-selector) {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: rem(6);
            color: #ffffff;
          }

          :deep(.ant-select-selection-item) {
            color: #ffffff !important;
          }

          :deep(.ant-select-arrow) {
            color: #ffffff;
          }
        }

        .version-info {
          color: #0091ff;
          font-size: rem(14);
          font-weight: 500;
          padding: rem(8) rem(12);
          background: rgba(0, 145, 255, 0.1);
          border: 1px solid rgba(0, 145, 255, 0.3);
          border-radius: rem(6);
          text-align: center;
        }
      }

      .action-buttons {
        margin-top: auto;
        padding-top: rem(24);

        .start-btn {
          width: 100%;
          height: rem(48);
          font-size: rem(16);
          font-weight: 600;
          border-radius: rem(8);
          background: linear-gradient(135deg, #0091ff, #00d4ff);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #00d4ff, #0091ff);
            transform: translateY(rem(-1));
            box-shadow: 0 rem(4) rem(12) rgba(0, 145, 255, 0.3);
          }
        }
      }
    }
  }
}

// 全局样式覆盖
:deep(.ant-switch-checked) {
  background-color: #0091ff !important;
}

:deep(.ant-select-dropdown) {
  background: rgba(42, 40, 68, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;

  .ant-select-item {
    color: #ffffff !important;

    &:hover {
      background: rgba(0, 145, 255, 0.2) !important;
    }

    &.ant-select-item-option-selected {
      background: rgba(0, 145, 255, 0.3) !important;
    }
  }
}
</style>
