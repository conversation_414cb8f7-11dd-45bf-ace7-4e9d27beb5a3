import type { SkeletonFrame } from '../hooks/useSkeletonAnimation';

interface NewDataFormat {
	video_path: string;
	video_frames: number[];
	bboxs: number[][];
	keypoints: Record<string, number[][]>;
	mta_skeleton: Record<string, string>;
}

// 可互换的节点组对
export const SWAPPABLE_PAIRS_NEW = [
	['leftArm', 'rightArm'],
	['leftLeg', 'rightLeg'],
	['leftFoot', 'rightFoot'],
];

/**
 * 根据传入的单次数据，动态生成所有需要的映射和配置。
 * @param newData 从服务器获取的 JSON 数据。
 */
export function generateDynamicMappings(newData: NewDataFormat) {
	// 关节名称的有序列表
	const ORDERED_JOINT_NAMES = Object.keys(newData.keypoints);

	// 动态生成关节名称到索引的映射
	const JOINT_NAME_TO_INDEX: Record<string, number> = Object.fromEntries(
		ORDERED_JOINT_NAMES.map((name, index) => [name, index])
	);

	// 动态获取关节总数
	const TOTAL_JOINTS = ORDERED_JOINT_NAMES.length;

	// 用可读的名称定义分组
	const NODE_GROUPS_BY_NAME = {
		head: ['head'],
		spine: [
			'neck_01',
			'neck_02',
			'spine_01',
			'spine_02',
			'spine_03',
			'spine_04',
			'spine_05',
			'pelvis',
		],
		leftArm: [
			'clavicle_l',
			'upperarm_l',
			'lowerarm_l',
			'lowerarm_twist_01_l',
			'lowerarm_twist_02_l',
			'hand_l',
			'thumb_01_l',
			'thumb_02_l',
			'thumb_03_l',
			'index_metacarpal_l',
			'index_01_l',
			'index_02_l',
			'index_03_l',
			'middle_metacarpal_l',
			'middle_01_l',
			'middle_02_l',
			'middle_03_l',
			'ring_metacarpal_l',
			'ring_01_l',
			'ring_02_l',
			'ring_03_l',
			'pinky_metacarpal_l',
			'pinky_01_l',
			'pinky_02_l',
			'pinky_03_l',
		],
		rightArm: [
			'clavicle_r',
			'upperarm_r',
			'lowerarm_r',
			'lowerarm_twist_01_r',
			'lowerarm_twist_02_r',
			'hand_r',
			'thumb_01_r',
			'thumb_02_r',
			'thumb_03_r',
			'index_metacarpal_r',
			'index_01_r',
			'index_02_r',
			'index_03_r',
			'middle_metacarpal_r',
			'middle_01_r',
			'middle_02_r',
			'middle_03_r',
			'ring_metacarpal_r',
			'ring_01_r',
			'ring_02_r',
			'ring_03_r',
			'pinky_metacarpal_r',
			'pinky_01_r',
			'pinky_02_r',
			'pinky_03_r',
		],
		leftLeg: ['thigh_l', 'calf_l'],
		rightLeg: ['thigh_r', 'calf_r'],
		leftFoot: ['foot_l', 'ball_l'],
		rightFoot: ['foot_r', 'ball_r'],
	};

	// 动态将基于名称的分组转换为基于索引的分组
	const NODE_GROUPS_NEW: Record<string, number[]> = Object.fromEntries(
		Object.entries(NODE_GROUPS_BY_NAME).map(([groupName, jointNames]) => {
			const indices = jointNames
				.map((name) => JOINT_NAME_TO_INDEX[name])
				.filter((index) => index !== undefined); // 过滤掉数据中可能不存在的关节名
			return [groupName, indices];
		})
	);

	// 动态生成骨骼连线
	const SKELETON_LINKS: [number, number][] = Object.entries(
		newData.mta_skeleton
	)
		.map(([childJoint, parentJoint]) => {
			const childIndex = JOINT_NAME_TO_INDEX[childJoint];
			const parentIndex = JOINT_NAME_TO_INDEX[parentJoint];
			return childIndex !== undefined && parentIndex !== undefined
				? [parentIndex, childIndex]
				: undefined;
		})
		.filter((link): link is [number, number] => link !== undefined);

	return {
		JOINT_NAME_TO_INDEX,
		TOTAL_JOINTS,
		NODE_GROUPS_NEW,
		SKELETON_LINKS,
	};
}

// 类型别名，方便使用
export type DynamicMappings = ReturnType<typeof generateDynamicMappings>;

/**
 * 数据格式转换为 SkeletonFrame 数组
 * @param newData - 新的骨骼数据
 * @param mappings - 由 generateDynamicMappings 生成的动态配置
 * @param videoTotalFrames - (可选) 视频的总帧数
 * @returns 转换后的 SkeletonFrame 数组
 */
export function adaptNewData(
	newData: NewDataFormat,
	mappings: DynamicMappings,
	videoTotalFrames?: number
): SkeletonFrame[] {
	const { video_frames, bboxs, keypoints } = newData;
	const { JOINT_NAME_TO_INDEX, TOTAL_JOINTS } = mappings;
	const frames: SkeletonFrame[] = [];

	const frameDataIndexMap = new Map<number, number>();
	video_frames.forEach((frameNumber, index) => {
		frameDataIndexMap.set(frameNumber, index);
	});

	const jointNames = Object.keys(keypoints);
	const maxFrameNumber = videoTotalFrames
		? videoTotalFrames - 1
		: Math.max(...video_frames);

	for (let currentFrame = 0; currentFrame <= maxFrameNumber; currentFrame++) {
		const frameDataIndex = frameDataIndexMap.get(currentFrame);
		const hasData = frameDataIndex !== undefined;

		const keypointsArray: [number, number][] = new Array(TOTAL_JOINTS).fill(
			[0, 0]
		);

		if (hasData && frameDataIndex !== undefined) {
			jointNames.forEach((jointName) => {
				const jointIndex = JOINT_NAME_TO_INDEX[jointName];
				const keypointData = keypoints[jointName]?.[frameDataIndex];

				if (jointIndex !== undefined && keypointData) {
					keypointsArray[jointIndex] = [
						keypointData[0],
						keypointData[1],
					];
				}
			});
		}

		let bboxFormatted: [number, number, number, number][] | undefined;
		if (
			hasData &&
			frameDataIndex !== undefined &&
			bboxs[frameDataIndex]?.length === 4
		) {
			bboxFormatted = [
				bboxs[frameDataIndex] as [number, number, number, number],
			];
		}

		frames.push({
			image_shape: [1080, 1920],
			frame_idx: currentFrame,
			instance_info: [
				{
					keypoints: keypointsArray,
					bbox: bboxFormatted,
					bbox_score: hasData ? 1.0 : 0.0,
				},
			],
		});
	}

	return frames;
}
