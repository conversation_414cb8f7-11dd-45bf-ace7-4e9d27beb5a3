/**
 * VitePress 自定义样式
 */

/* 侧边栏链接激活状态颜色 */
.VPSidebarItem.is-active > .item > .link {
  color: #10b981 !important;
}

/* 侧边栏链接悬停状态颜色 */
.VPSidebarItem .item .link:hover {
  color: #10b981 !important;
}

/* 侧边栏当前页面链接颜色 */
.VPSidebarItem.is-active > .item > .link .text {
  color: #10b981 !important;
}

/* 侧边栏链接左侧指示器颜色 */
.VPSidebarItem.is-active > .item::before {
  background-color: #10b981 !important;
}

/* 侧边栏分组标题悬停颜色 */
.VPSidebarItem .text:hover {
  color: #10b981 !important;
}

/* 确保链接文本在激活时使用自定义颜色 */
.VPSidebarItem.is-active .text {
  color: #10b981 !important;
  font-weight: 600;
}

/* 侧边栏链接在焦点状态下的颜色 */
.VPSidebarItem .item .link:focus {
  color: #10b981 !important;
}

/* 侧边栏子项目激活状态 */
.VPSidebarItem .items .VPSidebarItem.is-active > .item > .link {
  color: #10b981 !important;
}

/* 右侧目录导航（outline）样式 - 多种选择器确保覆盖 */
.VPDocAsideOutline .outline-link,
.VPDocAside .outline-link,
.outline-link,
.VPDocOutlineItem .outline-link {
  color: var(--vp-c-text-2);
}

/* 右侧目录导航悬浮状态 */
.VPDocAsideOutline .outline-link:hover,
.VPDocAside .outline-link:hover,
.outline-link:hover,
.VPDocOutlineItem .outline-link:hover,
.VPDocOutlineItem a:hover {
  color: #10b981 !important;
}

/* 右侧目录导航激活状态 */
.VPDocAsideOutline .outline-link.active,
.VPDocAside .outline-link.active,
.outline-link.active,
.VPDocOutlineItem .outline-link.active,
.VPDocOutlineItem.active a {
  color: #10b981 !important;
  font-weight: 600;
}

/* 右侧目录导航焦点状态 */
.VPDocAsideOutline .outline-link:focus,
.VPDocAside .outline-link:focus,
.outline-link:focus,
.VPDocOutlineItem .outline-link:focus {
  color: #10b981 !important;
}

/* 页面内标题链接悬浮 */
.vp-doc h1:hover .header-anchor,
.vp-doc h2:hover .header-anchor,
.vp-doc h3:hover .header-anchor,
.vp-doc h4:hover .header-anchor,
.vp-doc h5:hover .header-anchor,
.vp-doc h6:hover .header-anchor {
  color: #10b981 !important;
}

/* 页面内容区域的链接悬浮 */
.vp-doc a:hover {
  color: #10b981 !important;
}

/* 通用右侧导航 */
.VPAside a:hover,
.VPDocAside a:hover,
[class*="outline"] a:hover,
[class*="Outline"] a:hover {
  color: #10b981 !important;
}

/* 激活状态的通用样式 */
.VPAside a.active,
.VPDocAside a.active,
[class*="outline"] a.active,
[class*="Outline"] a.active {
  color: #10b981 !important;
  font-weight: 600;
}

/* 通用悬浮样式 */
.VPContent .VPDoc .aside a:hover {
  color: #10b981 !important;
}
