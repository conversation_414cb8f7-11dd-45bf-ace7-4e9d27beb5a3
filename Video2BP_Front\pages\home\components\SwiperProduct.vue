<template>
  <div class="swiper-container">
    <!-- 添加自定义导航按钮 -->
    <div class="swiper-button-prev custom-prev-btn"></div>
    <div class="swiper-button-next custom-next-btn"></div>
    <swiper :modules="modules" :slides-per-view="1" :space-between="0" :pagination="false" class="custom-swiper"
      :navigation="{
        prevEl: '.swiper-button-prev',
        nextEl: '.swiper-button-next'
      }" :autoplay="false" @swiper="onSwiper" @slideChange="onSlideChange">
      <swiper-slide v-for="(slide, index) in slides" :key="index">
        <div class="slide-content">
          <div class="ec-left">
            <video :ref="el => { videoRefs[index] = el as HTMLVideoElement | null }" :src="slide.videoUrl" playsinline
              preload="metadata" @ended="onVideoEnded(index)"></video>
            <VideoPlayControl :videoRef="videoRefs[index]" :initialPlaying="videoPlayingStates[index]"
              @toggle="toggleVideo(index)" />
          </div>
          <div class="ec-right">
            <div class="cr-title">{{ slide.title }}</div>
            <div class="cr-detail">{{ slide.description }}</div>
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination, Navigation, Autoplay } from 'swiper/modules'
import type { Swiper as SwiperType } from 'swiper'
import type { ProductItem } from '~/types/business'
import { useApi } from '~/composables/useApi'
import VideoPlayControl from './VideoPlayControl.vue'

// 导入 Swiper 样式
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'

defineOptions({
  name: 'SwiperDemo',
})

const { business } = useApi()

// 使用数组存储每个轮播项的视频引用
const videoRefs = reactive<(HTMLVideoElement | null)[]>([])
// 使用数组存储每个轮播项的视频播放状态
const videoPlayingStates = reactive<boolean[]>([])

// 当前活动的轮播项索引
const activeIndex = ref(0)

// 定义 Swiper 模块
const modules = [Pagination, Navigation, Autoplay]

// 响应式数据管理
const slides = ref<ProductItem[]>([])

const fetchProductList = async () => {
  try {
    const result = await business?.getProductList({})

    if (result) {
      slides.value = result
      console.log('获取产品列表成功:', result)
    }
  } catch (err) {
    console.error('获取产品列表失败:', err)
  }
}

// 初始化视频引用和播放状态数组
const initializeVideoArrays = () => {
  // 确保数组长度与轮播项数量匹配
  videoRefs.length = slides.value.length
  videoPlayingStates.length = slides.value.length

  // 初始化所有视频为暂停状态
  for (let i = 0; i < videoPlayingStates.length; i++) {
    videoPlayingStates[i] = false
  }
}
// 监听slides变化，重新初始化视频数组
watch(slides, () => {
  initializeVideoArrays()
}, { deep: true })

// 监听视频引用变化
watch(videoRefs, (newRefs) => {
  console.log('视频引用已更新:', newRefs)
}, { deep: true })

// Swiper 实例
let swiperInstance: SwiperType | null = null

// Swiper 事件处理
const onSwiper = (swiper: SwiperType) => {
  console.log('Swiper 实例:', swiper)
  swiperInstance = swiper
  activeIndex.value = swiper.activeIndex
}

const onSlideChange = () => {
  if (swiperInstance) {
    const newIndex = swiperInstance.activeIndex
    console.log('幻灯片已更改，当前索引:', newIndex)

    // 暂停所有视频
    pauseAllVideos()

    activeIndex.value = newIndex
  }
}

// 暂停所有视频
const pauseAllVideos = () => {
  videoRefs.forEach((video, index) => {
    if (video && !video.paused) {
      video.pause()
      videoPlayingStates[index] = false
    }
  })
}

// 处理视频播放结束事件
const onVideoEnded = (index: number) => {
  videoPlayingStates[index] = false

  // 确保视频回到开始位置但不自动播放
  const video = videoRefs[index]
  if (video) {
    video.currentTime = 0
  }
}

// 切换指定索引的视频播放/暂停
const toggleVideo = (index: number) => {
  const video = videoRefs[index]

  if (video) {
    if (video.paused) {
      // 播放前先暂停其他视频
      pauseAllVideos()

      // 播放当前视频
      const playPromise = video.play()
      if (playPromise !== undefined) {
        playPromise.then(() => {
          videoPlayingStates[index] = true
        }).catch(error => {
          videoPlayingStates[index] = false
        })
      }
    } else {
      video.pause()
      videoPlayingStates[index] = false
      console.log('视频已暂停')
    }
  } else {
    console.error('视频元素不存在')
  }
}

// 页面初始化
onMounted(async () => {
  await fetchProductList()
  initializeVideoArrays()
})


</script>

<style lang="scss" scoped>
.swiper-container {
  width: 100%;
  position: relative;

  // 自定义导航按钮样式
  .swiper-button-prev,
  .swiper-button-next {
    position: absolute;
    top: 50%;
    width: rem(50);
    height: rem(50);
    margin-top: rem(-25);
    z-index: 10;
    cursor: pointer;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;

    // 移除默认的Swiper箭头
    &::after {
      display: none;
    }
  }

  .custom-prev-btn {
    width: rem(100);
    height: rem(100);
    position: absolute;
    left: rem(20);
    top: 50%;
    transform: translateY(-50%);
    background-image: url('~/assets/images/swiper-left.webp');
  }

  .custom-next-btn {
    width: rem(100);
    height: rem(100);
    position: absolute;
    right: rem(20);
    top: 50%;
    transform: translateY(-50%);
    background-image: url('~/assets/images/swiper-right.webp');
  }

  .slide-content {
    width: 100%;
    height: 100%;
    display: flex;
    background: linear-gradient(to bottom, #1d1935 0%, #1d1933 100%);

    .ec-left {
      width: 50%;
      height: rem(1080);
      position: relative;

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .ec-right {
      width: 50%;
      padding: rem(107) 0 0 rem(120);

      .cr-title {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: bold;
        font-size: rem(72);
        color: #FFFFFF;
        line-height: rem(104);
        text-align: left;
        font-style: normal;
        position: relative;
        margin-bottom: rem(88);

        &::after {
          content: '';
          width: rem(172);
          height: rem(10);
          background: #FFFFFF;
          border-radius: rem(1);
          position: absolute;
          left: 0;
          bottom: rem(-15);
        }
      }

      .cr-detail {
        width: 77%;
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(40);
        color: #98DFFB;
        line-height: rem(72);
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>
