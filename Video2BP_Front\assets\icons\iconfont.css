@font-face {
  font-family: "iconfont"; /* Project id 4933728 */
  src: url('iconfont.woff2?t=1750059721239') format('woff2'),
       url('iconfont.woff?t=1750059721239') format('woff'),
       url('iconfont.ttf?t=1750059721239') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-reset_defalut:before {
  content: "\e60a";
}

.icon-reset:before {
  content: "\e739";
}

.icon-jiantou:before {
  content: "\e679";
}

.icon-jiantou1:before {
  content: "\e600";
}

.icon-a-zu13:before {
  content: "\e624";
}

