#!/usr/bin/env python3
"""
测试安全认证系统
验证登录失败锁定、业务码响应等功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.security.auth_security import AuthSecurityService
from core.constants import BusinessCode, BusinessMessage, AuthSecurityConfig
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from core.config import settings


async def test_auth_security():
    """测试认证安全功能"""
    
    print("=== 测试安全认证系统 ===\n")
    
    # 初始化安全服务
    auth_service = AuthSecurityService()
    
    # 测试邮箱
    test_email = "<EMAIL>"
    test_password = "wrong_password"
    
    print(f"测试邮箱: {test_email}")
    print(f"最大登录尝试次数: {AuthSecurityConfig.MAX_LOGIN_ATTEMPTS}")
    print(f"锁定时长: {AuthSecurityConfig.LOCKOUT_DURATION} 秒")
    print(f"登录失败延迟: {AuthSecurityConfig.LOGIN_DELAY_AFTER_FAILED} 秒")
    print(f"锁定状态延迟: {AuthSecurityConfig.LOGIN_DELAY_WHEN_LOCKED} 秒\n")
    
    # 创建数据库连接（如果可用）
    try:
        engine = create_async_engine(settings.DATABASE_URL)
        async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        
        async with async_session() as db:
            print("数据库连接成功，开始测试...\n")
            
            # 测试1: 正常登录失败
            print("=== 测试1: 模拟登录失败 ===")
            for i in range(AuthSecurityConfig.MAX_LOGIN_ATTEMPTS + 2):
                print(f"第 {i+1} 次登录尝试...")
                user, code = await auth_service.authenticate_user(
                    db, test_email, test_password, "127.0.0.1"
                )
                print(f"结果: {BusinessMessage.get(code)} (业务码: {code})")
                
                if code == BusinessCode.AUTH_ACCOUNT_LOCKED:
                    remaining = await auth_service.get_remaining_lockout_time(test_email)
                    print(f"账号已锁定，剩余时间: {remaining} 秒")
                    break
                print()
            
            print("\n=== 测试2: 检查锁定状态 ===")
            locked_time = await auth_service.get_remaining_lockout_time(test_email)
            if locked_time:
                print(f"账号当前锁定状态，剩余时间: {locked_time} 秒")
            else:
                print("账号未锁定")
            
            print("\n=== 测试3: 手动解锁账号 ===")
            await auth_service.unlock_account(test_email)
            print("账号已手动解锁")
            
            locked_time = await auth_service.get_remaining_lockout_time(test_email)
            if locked_time:
                print(f"解锁后状态: 仍锁定，剩余时间: {locked_time} 秒")
            else:
                print("解锁后状态: 已解锁")
                
    except Exception as e:
        print(f"数据库连接失败: {e}")
        print("跳过数据库相关测试，仅测试业务逻辑...\n")
        
        # 测试业务码和消息
        print("=== 测试业务码和消息 ===")
        test_codes = [
            BusinessCode.SUCCESS,
            BusinessCode.AUTH_LOGIN_FAILED,
            BusinessCode.AUTH_ACCOUNT_LOCKED,
            BusinessCode.SYSTEM_ERROR,
        ]
        
        for code in test_codes:
            message = BusinessMessage.get(code)
            print(f"业务码 {code}: {message}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_auth_security())
