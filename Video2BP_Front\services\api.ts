import axios, {
	type AxiosInstance,
	type AxiosRequestConfig,
	type AxiosResponse,
	type InternalAxiosRequestConfig,
} from 'axios';
import { useAppStore } from '~/stores/app';

// 创建API服务工厂函数
export function createApiService(baseURL: string) {
	// 创建 axios 实例
	const service: AxiosInstance = axios.create({
		baseURL,
		timeout: 10000, // 请求超时时间
		withCredentials: true, // 跨域请求时发送Cookie
	});

	// 请求拦截器
	service.interceptors.request.use(
		(config: InternalAxiosRequestConfig) => {
			const appStore = useAppStore();
			appStore.setLoading(true);

			// 不再从localStorage获取token
			// HttpOnly Cookie会自动随请求发送
			return config;
		},
		(error) => {
			const appStore = useAppStore();
			appStore.setLoading(false);
			console.error('Request Error:', error);
			return Promise.reject(error);
		}
	);

	// 响应拦截器
	service.interceptors.response.use(
		(response: AxiosResponse) => {
			const appStore = useAppStore();
			appStore.setLoading(false);

			const res = response.data;

			if (res.code !== 200 && res.code !== 0 && res.code !== 20000) {
				if (
					res.code === 401 ||
					res.code === 50008 ||
					res.code === 50012 ||
					res.code === 50014
				) {
					navigateTo('/auth');
				}
				return Promise.reject(res.message ?? res.msg ?? 'Error');
			} else {
				return res.data;
			}
		},
		(error) => {
			const appStore = useAppStore();
			appStore.setLoading(false);
			console.error('Response Error:', error.message);

			if (error.response && error.response.status === 401) {
				navigateTo('/auth');
			}
			return Promise.reject(error);
		}
	);

	// 封装通用的请求方法，并导出
	return {
		get<T = unknown>(
			url: string,
			params?: object,
			config?: AxiosRequestConfig
		): Promise<T> {
			return service.get(url, { params, ...config });
		},
		post<T = unknown>(
			url: string,
			data?: object,
			config?: AxiosRequestConfig
		): Promise<T> {
			return service.post(url, data, config);
		},
		put<T = unknown>(
			url: string,
			data?: object,
			config?: AxiosRequestConfig
		): Promise<T> {
			return service.put(url, data, config);
		},
		delete<T = unknown>(
			url: string,
			params?: object,
			config?: AxiosRequestConfig
		): Promise<T> {
			return service.delete(url, { params, ...config });
		},
		// 可以添加其他方法如 patch, head 等
		request<T = unknown>(config: AxiosRequestConfig): Promise<T> {
			return service.request(config);
		},
	};
}

// 导出类型定义，方便使用
export type ApiService = ReturnType<typeof createApiService>;
