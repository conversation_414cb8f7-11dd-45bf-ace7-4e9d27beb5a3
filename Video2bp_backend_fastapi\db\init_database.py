"""
数据库初始化脚本
创建所有必要的表结构
"""

import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from core.config import settings

# 导入所有模型以确保表被创建
from models.base import Base
from apps.app_user.model import User
from apps.app_video_manage.model import UserVideos
from apps.app_home_manage.model import News

# 导入新增的模型
from db.models import (
    UserOrders,
    UserVipRecords,
    BusinessItems,
    Products,
    UserCases,
    Projects,
    Resources,
    CompanyInfo,
    ComboPlans,
    ContactSubmissions,
    VideoProcessRecords,
)


async def create_database():
    """创建数据库（如果不存在）"""
    # 从URL中提取数据库名
    db_url = str(settings.ASYNC_DATABASE_URL)
    if "mysql" in db_url:
        # MySQL
        db_name = db_url.split("/")[-1].split("?")[0]
        base_url = db_url.replace(f"/{db_name}", "/mysql")
        
        engine = create_async_engine(base_url)
        async with engine.begin() as conn:
            await conn.execute(text(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
        await engine.dispose()
        print(f"✅ 数据库 {db_name} 创建成功")
    
    elif "sqlite" in db_url:
        # SQLite 会自动创建文件
        print("✅ SQLite 数据库将自动创建")
    
    else:
        print("⚠️ 未识别的数据库类型，请手动创建数据库")


async def create_tables():
    """创建所有表"""
    engine = create_async_engine(str(settings.ASYNC_DATABASE_URL), echo=True)
    
    async with engine.begin() as conn:
        # 创建所有表
        await conn.run_sync(Base.metadata.create_all)
    
    await engine.dispose()
    print("✅ 所有表创建成功")


async def insert_initial_data():
    """插入初始数据"""
    engine = create_async_engine(str(settings.ASYNC_DATABASE_URL))
    
    from sqlalchemy.ext.asyncio import AsyncSession
    from sqlalchemy.orm import sessionmaker
    
    AsyncSessionLocal = sessionmaker(
        bind=engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False,
    )
    
    async with AsyncSessionLocal() as session:
        # 插入公司信息
        company_info = CompanyInfo(
            name="灵宇AI动捕",
            description="专业的AI动作捕捉解决方案提供商",
            address="山东省",
            phone="************",
            email="<EMAIL>",
            website="https://www.lingyuai.com",
            logo_url="https://example.com/logo.png"
        )
        session.add(company_info)
        
        # 插入套餐信息
        combo_plans = [
            ComboPlans(
                name="基础版",
                description="适合个人用户的基础动捕功能",
                price=99.00,
                duration_days=30,
                features=["单人动捕", "基础格式导出", "标准技术支持"],
                is_active=True
            ),
            ComboPlans(
                name="专业版",
                description="适合专业用户的高级动捕功能",
                price=299.00,
                duration_days=30,
                features=["多人动捕", "高级格式导出", "面部表情捕捉", "优先技术支持"],
                is_active=True
            ),
            ComboPlans(
                name="企业版",
                description="适合企业用户的完整解决方案",
                price=999.00,
                duration_days=30,
                features=["无限制动捕", "所有格式导出", "API接入", "专属技术支持", "定制化服务"],
                is_active=True
            )
        ]
        for plan in combo_plans:
            session.add(plan)
        
        # 插入业务项目
        business_items = [
            BusinessItems(
                title="AI视频处理解决方案",
                image_url="https://example.com/business1.jpg",
                details=["解决方案技术支持", "应用支持服务", "产品功能演示讲解", "产品部署与接入调试指南"],
                sort_order=1,
                is_active=True
            ),
            BusinessItems(
                title="动作捕捉技术服务",
                image_url="https://example.com/business2.jpg",
                details=["专业动作捕捉设备", "实时动作识别", "高精度骨骼追踪", "多人同时捕捉"],
                sort_order=2,
                is_active=True
            )
        ]
        for item in business_items:
            session.add(item)
        
        # 插入产品信息
        products = [
            Products(
                title="单/多人捕捉",
                description="在动作捕捉的世界中，首帧姿势是一切的开始。自定义首帧姿势，赋予您无限的创造自由。",
                video_url="https://example.com/videos/demo1.mp4",
                thumbnail_url="https://example.com/thumbnails/product1.jpg",
                sort_order=1,
                is_active=True
            ),
            Products(
                title="面部表情捕捉",
                description="精准捕捉面部表情变化，为您的角色注入生动的情感表达。",
                video_url="https://example.com/videos/demo2.mp4",
                thumbnail_url="https://example.com/thumbnails/product2.jpg",
                sort_order=2,
                is_active=True
            ),
            Products(
                title="手部动作捕捉",
                description="细致入微的手部动作捕捉，让每一个手势都栩栩如生。",
                video_url="https://example.com/videos/demo3.mp4",
                thumbnail_url="https://example.com/thumbnails/product3.jpg",
                sort_order=3,
                is_active=True
            )
        ]
        for product in products:
            session.add(product)

        # 插入测试用户（带有初始余额）
        from apps.app_user.model import User
        test_user = User(
            username="testuser",
            email="<EMAIL>",
            phone="13800138000",
            password="$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.gSUadG",  # 密码: 123456
            balance=100.00,  # 初始余额100元
            vip_level="1",
            status=1
        )
        session.add(test_user)

        await session.commit()
        print("✅ 初始数据插入成功")
    
    await engine.dispose()


async def main():
    """主函数"""
    print("🚀 开始初始化数据库...")
    
    try:
        # 1. 创建数据库
        await create_database()
        
        # 2. 创建表
        await create_tables()
        
        # 3. 插入初始数据
        await insert_initial_data()
        
        print("🎉 数据库初始化完成！")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
