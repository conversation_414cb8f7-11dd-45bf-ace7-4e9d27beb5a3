import random
import string
import hashlib
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from decimal import Decimal
from fastapi import HTTPException, Request
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND, HTTP_401_UNAUTHORIZED, HTTP_500_INTERNAL_SERVER_ERROR

from apps.app_user.model import User
from db.models import UserOrders
from core.config import settings
from core.e import ErrorCode, ErrorMessage
from core.constants import BusinessCode, BusinessMessage
from core.security import AuthSecurityService
from core.redis import get_redis


class UserAuthService:
    """用户认证服务"""

    def __init__(self):
        # 初始化Redis连接
        self.redis_client = get_redis()
        # 初始化安全认证服务
        self.auth_security = AuthSecurityService()
    
    def generate_verification_code(self) -> str:
        """生成6位数字验证码"""
        return "".join(random.choices(string.digits, k=6))
    
    def hash_password(self, password: str) -> str:
        """使用MD5加密密码（保持与Flask版本一致）"""
        return hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    
    async def send_email_verification_code(self, email: str) -> None:
        """发送邮箱验证码"""
        code = self.generate_verification_code()
        
        # TODO: 实际发送邮件的逻辑
        # 这里需要集成邮件发送服务
        print(f"验证码 {code} 已发送到邮箱 {email}")
        
        # 将验证码存入Redis，设置过期时间为5分钟
        self.redis_client.setex(f"email_verification:{email}", 300, code)
    
    def verify_email_verification_code(self, email: str, code: str) -> bool:
        """验证邮箱验证码"""
        stored_code = self.redis_client.get(f"email_verification:{email}")
        if stored_code and stored_code == code:
            # 验证成功后删除验证码
            self.redis_client.delete(f"email_verification:{email}")
            return True
        return False
    
    async def check_email_registered(self, db: AsyncSession, email: str) -> bool:
        """检查邮箱是否已注册"""
        result = await db.execute(select(User).filter(User.email == email))
        user = result.scalars().first()
        return user is not None
    
    async def register_user(self, db: AsyncSession, email: str, password: str, code: str) -> None:
        """用户注册"""
        # 验证验证码
        if not self.verify_email_verification_code(email, code):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.VERIFICATION_CODE_ERROR}_{ErrorMessage.get(ErrorCode.VERIFICATION_CODE_ERROR)}"
            )
        
        # 检查邮箱是否已注册
        if await self.check_email_registered(db, email):
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.USER_EXIST}_{ErrorMessage.get(ErrorCode.USER_EXIST)}"
            )
        
        # 创建新用户
        new_user = User(email=email)
        new_user.set_password(password)
        db.add(new_user)
        await db.commit()
    
    async def login_user(self, db: AsyncSession, email: str, password: str, ip_address: str = None) -> tuple[int, str]:
        """
        安全的用户登录

        Args:
            db: 数据库会话
            email: 邮箱
            password: 密码
            ip_address: 客户端IP地址

        Returns:
            tuple[int, str]: 用户ID和邮箱

        Raises:
            HTTPException: 登录失败时抛出异常
        """
        # 使用安全认证服务进行认证
        user, business_code = await self.auth_security.authenticate_user(
            db, email, password, ip_address
        )

        if business_code == BusinessCode.SUCCESS and user:
            return user.id, user.email
        else:
            # 根据业务码返回相应的HTTP异常
            if business_code == BusinessCode.AUTH_ACCOUNT_LOCKED:
                raise HTTPException(
                    status_code=HTTP_401_UNAUTHORIZED,
                    detail=f"{business_code}_{BusinessMessage.get(business_code)}"
                )
            elif business_code == BusinessCode.AUTH_LOGIN_FAILED:
                raise HTTPException(
                    status_code=HTTP_401_UNAUTHORIZED,
                    detail=f"{business_code}_{BusinessMessage.get(business_code)}"
                )
            else:  # SYSTEM_ERROR
                raise HTTPException(
                    status_code=HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"{business_code}_{BusinessMessage.get(business_code)}"
                )
    
    async def get_user_info(self, db: AsyncSession, user_id: int) -> dict:
        """获取用户信息"""
        result = await db.execute(select(User).filter(User.id == user_id))
        user = result.scalars().first()

        if not user:
            raise HTTPException(
                status_code=HTTP_404_NOT_FOUND,
                detail=f"{ErrorCode.USER_NOT_FOUND}_{ErrorMessage.get(ErrorCode.USER_NOT_FOUND)}"
            )

        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "phone": user.phone,
            "balance": float(user.balance),  # 转换为float以便JSON序列化
            "status": user.status,
            "vipLevel": user.vip_level,
        }

    # 新增的方法，根据前端API文档要求

    async def get_current_user_info(self, db: AsyncSession, user_id: int) -> dict:
        """获取当前用户详细信息"""
        try:
            result = await db.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()

            if not user:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail=f"{ErrorCode.USER_NOT_FOUND}_{ErrorMessage.get(ErrorCode.USER_NOT_FOUND)}"
                )

            return {
                "id": str(user.id),
                "username": user.username or "用户名",
                "email": user.email,
                "role": "user",
                "avatar": None,  # 暂时没有头像字段
                "createdAt": user.created_at.isoformat() if user.created_at else "",
                "updatedAt": user.updated_at.isoformat() if user.updated_at else "",
                "balance": 100.5,  # 暂时返回固定值
                "vipLevel": user.vip_level,
                "vipDeadline": None  # 暂时没有VIP到期时间字段
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )

    async def change_password(self, db: AsyncSession, user_id: int, current_password: str, new_password: str) -> None:
        """修改用户密码"""
        try:
            result = await db.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()

            if not user:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail=f"{ErrorCode.USER_NOT_FOUND}_{ErrorMessage.get(ErrorCode.USER_NOT_FOUND)}"
                )

            # 验证当前密码
            if user.password != self.hash_password(current_password):
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_当前密码错误"
                )

            # 验证新密码长度
            if not (6 <= len(new_password) <= 20):
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_新密码长度必须在6-20位之间"
                )

            # 更新密码
            user.password = self.hash_password(new_password)
            await db.commit()

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )

    async def update_user_info(self, db: AsyncSession, user_id: int, request) -> None:
        """更新用户信息"""
        try:
            result = await db.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()

            if not user:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail=f"{ErrorCode.USER_NOT_FOUND}_{ErrorMessage.get(ErrorCode.USER_NOT_FOUND)}"
                )

            # 更新用户名
            if request.username is not None:
                # 检查用户名是否已存在
                existing_user = await db.execute(
                    select(User).filter(User.username == request.username, User.id != user_id)
                )
                if existing_user.scalars().first():
                    raise HTTPException(
                        status_code=HTTP_400_BAD_REQUEST,
                        detail="400_用户名已存在"
                    )
                user.username = request.username

            # 更新其他字段（这些字段可能需要管理员权限）
            if request.balance is not None:
                # 这里应该检查管理员权限
                pass  # 暂时不实现

            if request.vipLevel is not None:
                # 这里应该检查管理员权限
                pass  # 暂时不实现

            await db.commit()

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )

    async def get_order_records(self, db: AsyncSession, user_id: int, page_num: int, page_size: int) -> dict:
        """获取用户订单记录"""
        try:
            # 验证分页参数
            if page_num < 1:
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_pageNum必须大于0"
                )

            if not (5 <= page_size <= 50):
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_pageSize必须在5-50之间"
                )

            # 查询用户订单记录
            from sqlalchemy import desc

            # 查询总数
            count_stmt = select(func.count(UserOrders.id)).where(UserOrders.user_id == user_id)
            count_result = await db.execute(count_stmt)
            total = count_result.scalar()

            # 查询分页数据
            offset = (page_num - 1) * page_size
            stmt = select(UserOrders).where(
                UserOrders.user_id == user_id
            ).order_by(desc(UserOrders.created_at)).offset(offset).limit(page_size)

            result = await db.execute(stmt)
            orders = result.scalars().all()

            # 转换为响应格式
            page_data = []
            for order in orders:
                page_data.append({
                    "id": order.order_no,
                    "type": order.order_type,
                    "time": order.created_at.strftime("%Y-%m-%d") if order.created_at else "",
                    "amount": f"{order.amount:.2f}",
                    "method": order.payment_method or "未知",
                    "toAccount": f"{order.amount:.2f}"  # 这里可以根据业务逻辑计算账户余额
                })

            return {
                "data": page_data,
                "total": total,
                "pageNum": page_num,
                "pageSize": page_size
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"{ErrorCode.INTERNAL_SERVER_ERROR}_{str(e)}"
            )

    async def get_user_balance(self, db: AsyncSession, user_id: int) -> Decimal:
        """获取用户余额"""
        try:
            result = await db.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()

            if not user:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail=f"{ErrorCode.USER_NOT_FOUND}_{ErrorMessage.get(ErrorCode.USER_NOT_FOUND)}"
                )

            return user.balance

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"500_获取用户余额失败: {str(e)}"
            )

    async def update_user_balance(self, db: AsyncSession, user_id: int, amount: Decimal, operation: str) -> dict:
        """更新用户余额"""
        try:
            # 验证操作类型
            if operation not in ["recharge", "deduct"]:
                raise HTTPException(
                    status_code=HTTP_400_BAD_REQUEST,
                    detail="400_操作类型无效，仅支持recharge（充值）或deduct（扣除）"
                )

            # 获取用户
            result = await db.execute(select(User).filter(User.id == user_id))
            user = result.scalars().first()

            if not user:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail=f"{ErrorCode.USER_NOT_FOUND}_{ErrorMessage.get(ErrorCode.USER_NOT_FOUND)}"
                )

            # 计算新余额
            if operation == "recharge":
                new_balance = user.balance + amount
            else:  # deduct
                new_balance = user.balance - amount
                if new_balance < 0:
                    raise HTTPException(
                        status_code=HTTP_400_BAD_REQUEST,
                        detail="400_余额不足，无法扣除"
                    )

            # 更新余额
            user.balance = new_balance
            await db.commit()
            await db.refresh(user)

            return {
                "newBalance": float(new_balance),
                "operation": operation,
                "amount": float(amount)
            }

        except HTTPException:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"500_更新用户余额失败: {str(e)}"
            )


# 创建服务实例
user_auth_service = UserAuthService()
