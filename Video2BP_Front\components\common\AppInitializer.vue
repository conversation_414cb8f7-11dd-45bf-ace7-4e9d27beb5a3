<template>
  <div class="app-initializer">
    <div class="initializer-content">
      <!-- Logo 区域 -->
      <div class="logo-section">
        <div class="logo-icon">
          <img src="~/assets/images/lingyuimage&logo.webp" alt="灵宇科技" class="logo-image" />
        </div>
        <h1 class="app-title">{{ appTitle }}</h1>
        <p class="app-subtitle">{{ appSubtitle }}</p>
      </div>

      <!-- 加载动画区域 -->
      <div class="loading-section">
        <div class="loading-spinner"></div>
        <p class="loading-text">{{ loadingText }}<span class="loading-dots"></span></p>
      </div>

      <!-- 进度条 -->
      <div class="progress-section">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progress + '%' }"></div>
        </div>
        <p class="progress-text">{{ Math.round(progress) }}%</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  appTitle?: string
  appSubtitle?: string
  loadingText?: string
  progress?: number
}

const props = withDefaults(defineProps<Props>(), {
  appTitle: '',
  appSubtitle: '',
  loadingText: '正在初始化应用',
  progress: 0
})
</script>

<style scoped lang="scss">
.app-initializer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  // background: rgba(42, 40, 68, 0.8);
  background-image: url('~/assets/images/upgrade-bg.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.initializer-content {
  text-align: center;
  color: white;
  max-width: rem(400);
  padding: rem(40);
}

.logo-section {

  .logo-icon {
    animation: pulse 2s ease-in-out infinite;

    .logo-image {
      width: rem(120);
      height: auto;
      object-fit: contain;
    }
  }

  .app-title {
    font-size: rem(32);
    font-weight: 600;
    margin: 0 0 rem(8) 0;
    letter-spacing: rem(1);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .app-subtitle {
    font-size: rem(16);
    font-weight: 300;
    margin: 0;
    opacity: 0.9;
    letter-spacing: rem(0.5);
  }
}

.loading-section {
  margin-bottom: rem(30);

  .loading-spinner {
    width: rem(40);
    height: rem(40);
    border: rem(3) solid rgba(255, 255, 255, 0.3);
    border-top: rem(3) solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto rem(16) auto;
  }

  .loading-text {
    font-size: rem(16);
    font-weight: 300;
    margin: 0;
    letter-spacing: rem(0.5);
    animation: fadeInOut 2s ease-in-out infinite;
  }

  .loading-dots {
    display: inline-block;
    animation: dots 1.5s steps(4, end) infinite;
  }
}

.progress-section {
  .progress-bar {
    width: 100%;
    height: rem(4);
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: rem(2);
    overflow: hidden;
    margin-bottom: rem(8);

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
      border-radius: rem(2);
      transition: width 0.3s ease;
      box-shadow: 0 0 rem(10) rgba(255, 255, 255, 0.3);
    }
  }

  .progress-text {
    font-size: rem(12);
    font-weight: 300;
    margin: 0;
    opacity: 0.8;
  }
}

// 动画定义
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes fadeInOut {

  0%,
  100% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }
}

@keyframes dots {

  0%,
  20% {
    content: '';
  }

  40% {
    content: '.';
  }

  60% {
    content: '..';
  }

  80%,
  100% {
    content: '...';
  }
}
</style>
