import time
import traceback

from flask import g, request
from loguru import logger


def record_request_start_time():
    g.start_time = time.time()
    url_method = request.method.upper()
    url_str = request.url
    if "OPTIONS" == url_method:
        return None
    try:
        request_data = str(request.data, encoding="utf-8")
        logger.error(f"starting:recv>>>url={url_str}, method={url_method}, body={request_data[:5000]}")
        return None
    except TypeError:
        request_data = str(request.args)
        logger.info(f"starting:recv>>>url={url_str}, method={url_method}, params={request_data[:5000]}")
        return None
    except Exception:
        logger.error(f"http body格式不是json: traceback={traceback.format_exc()}")
        return None


def calculate_metrics(response):
    # 统一记录回复数据日志
    url_str = request.url
    url_method = request.method.upper()
    if "OPTIONS" == url_method:
        return response
    if "start_time" not in g:
        return response
    if "image" in request.url:
        return response
    if "download" in request.url:
        return response
    if "export" in request.url:
        return response
    if "import" in request.url:
        return response
    response_data = str(response.data, encoding="utf-8")
    request_duration = round((time.time() - g.start_time) * 1000, 2)
    logger.error(f"ending:reply>>> url={url_str}: {response_data[:5000]} duration={request_duration}ms")
    return response
