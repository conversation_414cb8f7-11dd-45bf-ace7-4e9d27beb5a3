from typing import List, Dict, Any
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from starlette.status import HTTP_400_BAD_REQUEST

from db.models import BusinessItems, Products, UserCases, ContactSubmissions
from db.database import get_async_db
from apps.app_business.schema import Product, Business, UserCase


class BusinessService:
    """业务服务"""

    def __init__(self):
        pass
    
    async def get_business_list(self, db: AsyncSession = None) -> List[Business]:
        """获取业务轮播图列表"""
        try:
            if db is None:
                # 如果没有传入db会话，创建一个新的
                async for session in get_async_db():
                    db = session
                    break

            # 从数据库查询业务项目
            stmt = select(BusinessItems).where(BusinessItems.is_active == True).order_by(BusinessItems.sort_order)
            result = await db.execute(stmt)
            business_items = result.scalars().all()

            business_list = []
            for item in business_items:
                # 创建Business模型实例
                business_instance = Business(
                    id=item.id,
                    title=item.title,
                    image=item.image_url,
                    details=item.details  # JSON字段，直接返回
                )
                business_list.append(business_instance)

            return business_list

        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"500_获取业务列表失败: {str(e)}"
            )
    
    async def get_product_list(self, db: AsyncSession = None) -> List[Product]:
        """获取产品列表"""
        try:
            if db is None:
                # 如果没有传入db会话，创建一个新的
                async for session in get_async_db():
                    db = session
                    break

            # 从数据库查询产品
            stmt = select(Products).where(Products.is_active == True).order_by(Products.sort_order)
            result = await db.execute(stmt)
            products = result.scalars().all()

            product_list = []
            for product in products:
                # 创建Product模型实例，Pydantic会自动处理字段映射
                product_instance = Product(
                    id=product.id,
                    title=product.title,
                    description=product.description,
                    videoUrl=product.video_url  # 数据库字段video_url映射到videoUrl
                )
                product_list.append(product_instance)

            return product_list

        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"500_获取产品列表失败: {str(e)}"
            )
    
    async def get_user_cases(self, db: AsyncSession = None) -> List[UserCase]:
        """获取用户案例列表"""
        try:
            if db is None:
                # 如果没有传入db会话，创建一个新的
                async for session in get_async_db():
                    db = session
                    break

            # 从数据库查询用户案例
            stmt = select(UserCases).where(UserCases.is_active == True).order_by(UserCases.sort_order)
            result = await db.execute(stmt)
            user_cases = result.scalars().all()

            case_list = []
            for case in user_cases:
                # 创建UserCase模型实例
                case_instance = UserCase(
                    id=case.id,
                    name=case.user_name,
                    avatar=case.avatar_url,
                    time=case.time_label,
                    content=case.content,
                    preview=case.preview_url
                )
                case_list.append(case_instance)

            return case_list

        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"500_获取用户案例列表失败: {str(e)}"
            )
    
    async def submit_contact(self, phone: str, company_name: str, project: str, db: AsyncSession = None) -> None:
        """提交联系表单"""
        try:
            if db is None:
                # 如果没有传入db会话，创建一个新的
                async for session in get_async_db():
                    db = session
                    break

            # 保存联系信息到数据库
            contact_submission = ContactSubmissions(
                phone=phone,
                company_name=company_name,
                project=project,
                status=0  # 0-未处理
            )

            db.add(contact_submission)
            await db.commit()
            await db.refresh(contact_submission)

            print(f"联系表单已保存: ID={contact_submission.id}, 手机号={phone}, 公司={company_name}, 项目={project}")

            return None

        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"500_提交联系表单失败: {str(e)}"
            )


# 创建服务实例
business_service = BusinessService()
