import traceback
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base
from settings import DB_URI
from loguru import logger



class SQLAlchemyError(RuntimeError):
    def __init__(self, err_msg):
        self.errMsg = err_msg
        self.err_details = ""

    def data(self, err_details):
        self.err_details = err_details
        return self


ErrSQLAlchemyClient = SQLAlchemyError("操作数据库异常")

# 创建数据库引擎
try:
    engine = create_engine(DB_URI, pool_size=5, max_overflow=10, pool_timeout=30, connect_args={'connect_timeout': 10})
    logger.info(f"MySQL数据库引擎创建成功")
except Exception as e:
    logger.error(f"MySQL数据库连接异常, 错误信息: {str(e)}, 堆栈跟踪: {traceback.format_exc()}")
    raise

SessionFactory = sessionmaker(bind=engine)

# 基类
Base = declarative_base()


def get_db_session():
    """获取数据库会话"""
    return scoped_session(SessionFactory)


def close_db_session(session):
    """关闭数据库会话"""
    try:
        session.close()
    except Exception as error:
        logger.error(f"关闭数据库会话时出错, 错误信息: {str(error)}, 堆栈跟踪: {traceback.format_exc()}")


class DBSessionContext:
    def __enter__(self):
        self.session = get_db_session()
        return self.session

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.session.rollback()
        else:
            self.session.commit()
        close_db_session(self.session)