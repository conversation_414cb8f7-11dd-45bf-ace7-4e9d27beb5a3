export interface UploadResponse {
	code: number;
	message: string;
	data: {
		id: string;
		url: string;
	};
}

// 创建文件请求参数
export interface CreateFileRequest {
	id: string; // 视频上传后返回的ID
	videoUrl: string; // 视频URL
	startTime?: number; // 开始时间
	endTime?: number; // 结束时间
	duration?: number; // 持续时间
	wasTrimmed?: boolean; // 是否进行了裁剪
	originalSelection?: {
		start: number;
		end: number;
		duration: number;
	}; // 原始选择信息
	formData: any; // 表单数据
	outputFormat: number; // 输出格式
}

// 创建文件响应数据
export interface CreateFileResponseData {
	id?: string;
	fileId?: string;
	status: string;
}

// 创建文件原始响应
export interface CreateFileResponse {
	code: number;
	message: string;
	data: CreateFileResponseData;
}
