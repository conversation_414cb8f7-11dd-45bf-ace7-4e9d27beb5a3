// 手动加载.env文件
import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import { existsSync } from 'fs';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 确定当前环境
const nodeEnv = process.env.NODE_ENV || 'development';

// 加载基本.env文件
config({ path: resolve(__dirname, '.env') });

// 加载环境特定的.env文件
const envFile = resolve(__dirname, `.env.${nodeEnv}`);
if (existsSync(envFile)) {
	config({ path: envFile, override: true });
}

// 加载本地覆盖文件（如果存在）
const localEnvFile = resolve(__dirname, '.env.local');
if (existsSync(localEnvFile)) {
	config({ path: localEnvFile, override: true });
}

import { defineNuxtConfig } from 'nuxt/config';
export default defineNuxtConfig({
	// 开发模式配置
	devtools: { enabled: nodeEnv === 'development' },
	// ✅ 添加实验性优化
	experimental: {
		treeshakeClientOnly: true, // 树摇客户端代码
	},

	// 启用 SSG
	nitro: {
		preset: 'static',
		compatibilityDate: '2025-06-06',
		// 添加压缩
		compressPublicAssets: true,
		routeRules: {
			'/': { redirect: '/home' },
			'/api/**': {
				proxy: `${process.env.NUXT_API_PROXY_TARGET || 'http://localhost:3000'}/api/**`,
			},
		},
	},

	// 导入全局样式
	css: ['~/assets/styles/base.scss', '~/assets/icons/iconfont.css'],
	// 配置构建选项
	vite: {
		server: {
			headers: {
				'Cross-Origin-Embedder-Policy': 'require-corp',
				'Cross-Origin-Opener-Policy': 'same-origin',
			},
		},
		optimizeDeps: {
			exclude: ['@ffmpeg/ffmpeg', '@ffmpeg/util'],
		},
		css: {
			preprocessorOptions: {
				scss: {
					additionalData:
						'@use "~/assets/styles/variables.scss" as *;@use "~/assets/styles/themes.scss" as *; @use "~/assets/styles/utils.scss" as *; @use "~/assets/styles/mixins.scss" as *;',
				},
				less: {
					javascriptEnabled: true,
				},
			},
		},
		// 添加自定义 Vite 插件
		plugins: [
			{
				name: 'vite-plugin-fbx',
				transform(_code, id) {
					if (id.endsWith('.fbx')) {
						return `export default new URL(${JSON.stringify(id)}, import.meta.url).href;`;
					}
				},
			},
		],
		// 构建配置
		build: {
			sourcemap: process.env.NUXT_PUBLIC_SOURCEMAP === 'true',
			// ✅ 设置 chunk 大小限制
			chunkSizeWarningLimit: 1000,
		},
		esbuild: {
			drop: nodeEnv === 'production' ? ['console', 'debugger'] : [],
		},
	},
	build: {
		transpile: [
			// 'ant-design-vue',
			// 'three',
		],
	},

	// 配置路径别名
	alias: {
		'@': '.',
		'@services': './services',
		'@store': './store',
		'@apis': './services/api',
		'@hooks': './composables',
		'@interfaces': './types',
		'@views': './pages',
		'@router': './router',
		'@directives': './directives',
		'@locales': './locales',
	},

	// 配置应用
	app: {
		head: {
			title: process.env.NUXT_PUBLIC_APP_TITLE || '灵宇科技',
			meta: [
				{
					name: 'viewport',
					content: 'width=device-width, initial-scale=1',
				},
			],
			link: [],
		},
	},
	// 组件自动导入配置
	components: [
		{
			path: '~/components',
			pathPrefix: false,
			global: false, // 改为 false，只按需加载
		},
		{
			path: '~/components/global',
			global: true, // 只有这个目录下的组件全局可用
		},
	],

	// 运行时配置 - 安全地处理环境变量
	runtimeConfig: {
		// 服务器端私有变量
		apiSecret: process.env.NUXT_API_SECRET,

		// 客户端可访问的公共变量
		public: {
			apiBase: process.env.NUXT_PUBLIC_API_BASE || '/api',
			apiProxyTarget: process.env.NUXT_API_PROXY_TARGET,
			appTitle: process.env.NUXT_PUBLIC_APP_TITLE || '灵宇科技',
			sourcemap: process.env.NUXT_PUBLIC_SOURCEMAP === 'true',
			imageBaseUrl:
				process.env.NUXT_PUBLIC_IMAGE_BASE_URL ||
				'http://localhost:3000/assets/images',
		},
	},

	// 开发服务器配置
	devServer: {
		port: process.env.NUXT_PORT ? parseInt(process.env.NUXT_PORT) : 3000,
		host: '0.0.0.0',
	},

	modules: ['@pinia/nuxt', '@ant-design-vue/nuxt', '@nuxt/image'],
	image: {
		// 图片服务器配置

		// 预设配置
		presets: {
			hero: {
				modifiers: {
					format: 'webp',
					quality: 85,
					width: 1920,
					height: 1080,
				},
			},
			thumbnail: {
				modifiers: {
					format: 'webp',
					quality: 70,
					width: 400,
					height: 300,
				},
			},
			placeholder: {
				modifiers: {
					format: 'webp',
					quality: 20,
					blur: 5,
				},
			},
		},

		// 响应式断点
		screens: {
			xs: 320,
			sm: 640,
			md: 768,
			lg: 1024,
			xl: 1280,
			xxl: 1536,
		},
	},

	// 添加 Ant Design Vue 按需引入配置
	antd: {
		// 按需引入组件
		extractStyle: true,
	},
});
