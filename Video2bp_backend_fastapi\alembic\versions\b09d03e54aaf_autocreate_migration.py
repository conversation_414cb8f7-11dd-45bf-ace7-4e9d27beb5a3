"""autocreate migration

Revision ID: b09d03e54aaf
Revises:
Create Date: 2024-09-02 16:41:48.522753

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "b09d03e54aaf"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user",
        sa.Column("email", sa.String(length=32), nullable=True),
        sa.Column("username", sa.String(length=32), nullable=False),
        sa.Column(
            "avatar_url",
            sa.String(length=256),
            server_default="https://cdn.img.com/avatar.png",
            nullable=True,
        ),
        sa.Column("salt", sa.String(length=32), nullable=True),
        sa.Column("password", sa.String(length=600), nullable=True),
        sa.Column("id", sa.In<PERSON>ger(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.<PERSON>umn("deleted_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_user_email"), "user", ["email"], unique=False)
    op.create_index(op.f("ix_user_id"), "user", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_user_id"), table_name="user")
    op.drop_index(op.f("ix_user_email"), table_name="user")
    op.drop_table("user")
    # ### end Alembic commands ###
