<template>
  <div class="skeleton-player-with-bg">
    <div class="player-box">
      <div class="player-container" :style="{ width: `${width}px`, height: `${height}px` }">
        <div class="rc-box" :style="{
          width: `${contentBox.width}px`,
          height: `${contentBox.height}px`,
          top: `${contentBox.top}px`,
          left: `${contentBox.left}px`,
        }"></div>
        <video ref="videoRef" src="../demo.mp4" :width="width" :height="height" class="background-video"
          @loadedmetadata="onVideoLoaded"></video>

        <!-- 骨骼叠加层 -->
        <canvas ref="skeletonCanvas" :width="width" :height="height" class="skeleton-canvas"
          @mousedown="handleCanvasMouseDown" @mousemove="handleCanvasMouseMove" @mouseup="handleCanvasMouseUp"
          @mouseleave="handleCanvasMouseLeave"></canvas>

        <!-- 边界框叠加层 -->
        <canvas v-if="showBBox" ref="bboxCanvas" :width="width" :height="height" class="bbox-canvas"></canvas>
      </div>
    </div>

    <div class="controls">
      <div class="player-process">
        <div class="frame-info">
          <span class="play-cavans" @click="isPlaying ? pause() : play()">
            {{ isPlaying ? '暂停' : '播放' }}
          </span><span>{{ currentFrame + 1 }}/{{ videoTotalFrames }}</span>
        </div>

        <!-- 自定义进度条 -->
        <div class="custom-progress-bar" ref="progressBarRef" @click="handleProgressClick">
          <div class="progress-track">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
            <div class="progress-thumb" :style="{ left: progressPercentage + '%' }" @mousedown="handleThumbMouseDown">
            </div>
          </div>
        </div>
      </div>

      <div class="player-button">
        <div @click="manualPreviousFrame" class="prev-btn">
          <LeftOutlined />
        </div>
        <div class="play-input">
          <input min="1" :max="totalFrames" v-model="displayFrameNumber" type="number">
        </div>
        <div @click="manualNextFrame" class="next-btn">
          <RightOutlined />
        </div>
      </div>

      <div class="player-controls">
        <div class="point-watch" @click="showSkeleton = !showSkeleton">
          <img src="~/assets/images/2d.webp" alt="">
          <span>2D检测点</span>
        </div>
        <div class="rw-watch" @click="showBBox = !showBBox">
          <img src="~/assets/images/rw.webp" alt="">
          <span>人物检测框</span>
        </div>
      </div>
    </div>
    <div class="skeleton-selection-controls">
      <div class="selection-group">
        <!-- <button class="skeleton-btn" :class="{ active: selectedNodeGroups.has('head') }"
          @click="selectNodeGroup('head')">
          头部
        </button>
        <button class="skeleton-btn" :class="{ active: selectedNodeGroups.has('spine') }"
          @click="selectNodeGroup('spine')">
          脊柱
        </button>
        <button class="skeleton-btn" :class="{ active: selectedNodeGroups.has('leftArm') }"
          @click="selectNodeGroup('leftArm')">
          左臂
        </button>
        <button class="skeleton-btn" :class="{ active: selectedNodeGroups.has('rightArm') }"
          @click="selectNodeGroup('rightArm')">
          右臂
        </button>
        <button class="skeleton-btn" :class="{ active: selectedNodeGroups.has('leftLeg') }"
          @click="selectNodeGroup('leftLeg')">
          左腿
        </button>
        <button class="skeleton-btn" :class="{ active: selectedNodeGroups.has('rightLeg') }"
          @click="selectNodeGroup('rightLeg')">
          右腿
        </button>
        <button class="skeleton-btn" :class="{ active: selectedNodeGroups.has('leftFoot') }"
          @click="selectNodeGroup('leftFoot')">
          左脚
        </button>
        <button class="skeleton-btn" :class="{ active: selectedNodeGroups.has('rightFoot') }"
          @click="selectNodeGroup('rightFoot')">
          右脚
        </button>
        <button class="skeleton-btn swap-btn" :disabled="!canSwapLeftRight" @click="swapLeftRightNodes">
          左右互换
        </button>
        <button class="skeleton-btn clear-btn" @click="clearSelection">
          清除选择
        </button>
        <button class="skeleton-btn reset-btn" @click="resetCurrentFrame">
          <i class="iconfont icon-reset"></i> 重置当前帧
        </button>
        <button class="skeleton-btn reset-btn" @click="resetAllFrames">
          <i class="iconfont icon-reset"></i> 重置所有帧
        </button>
        <button class="skeleton-btn copy-btn" :disabled="canCopy" @click="copySelectedPoints">
          <i class="iconfont icon-copy"></i> 复制选中点
        </button>
        <button class="skeleton-btn paste-btn" :disabled="!hasCopiedData" @click="pasteToCurrentFrame">
          <i class="iconfont icon-paste"></i> 粘贴到当前帧
        </button> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import { useSkeletonAnimation } from '../hooks/useSkeletonAnimation'
import type { SkeletonFrame } from '../hooks/useSkeletonAnimation'
import { SWAPPABLE_PAIRS_NEW } from '../utils/newDataAdapter'
import { deepClone } from '~/utils'

const getCSSVariableValue = (variableName: string): string => {
  if (typeof window !== 'undefined') {
    const value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim()
    return value || '#000000' // 默认返回黑色
  }
  // 服务端渲染时的默认值
  const defaultColors: Record<string, string> = {
    '--color-skeleton-point-default': '#00ff00',
    '--color-skeleton-point-selected': '#ff0000',
    '--color-skeleton-point-red': '#ff0000',
    '--color-skeleton-point-green': '#00ff00',
    '--color-skeleton-line-default': '#ff00ff',
    '--color-skeleton-bbox-default': '#00ffff',
    '--color-white': '#ffffff'
  }
  return defaultColors[variableName] || '#000000'
}

const props = defineProps({
  skeletonData: {
    type: Array as () => SkeletonFrame[],
    required: true,
  },
  mappings: {
    type: Object,
    required: true,
  },
  videoSrc: {
    type: String,
    default: './demo.mp4',
  },
  width: {
    type: Number,
    default: 960,
  },
  height: {
    type: Number,
    default: 540,
  },
  renderOptions: {
    type: Object,
    default: () => ({}),
  },
  frameRate: {
    type: Number,
    default: 30, // 默认帧率为30fps，必须与骨骼数据的采样率和视频帧率一致
  },
})

const { mappings } = toRefs(props);
const emit = defineEmits<{
  videoLoaded: [videoTotalFrames: number]
}>()

// 节点组选择状态（可以多选）
const selectedNodeGroups = ref<Set<string>>(new Set())

// 节点分组定义（基于8个维度）
const NODE_GROUPS = mappings.value.NODE_GROUPS_NEW

// 可互换节点组对
const SWAPPABLE_PAIRS = SWAPPABLE_PAIRS_NEW
const effectiveFrameRate = ref(props.frameRate)

// 监听props中的帧率变化
watch(
  () => props.frameRate,
  (newFrameRate) => {
    effectiveFrameRate.value = newFrameRate
  },
)

// Canvas 引用
const skeletonCanvas = ref<HTMLCanvasElement | null>(null)
const bboxCanvas = ref<HTMLCanvasElement | null>(null) // 边界框画布
const videoRef = ref<HTMLVideoElement | null>(null) // 视频实例
const progressBarRef = ref<HTMLDivElement | null>(null) // 进度条引用

const showBBox = ref(true) // 是否显示边界框
const showSkeleton = ref(true) // 是否显示骨骼关键点和连线

const canvasWidth = ref(props.width) // 画布宽度
const canvasHeight = ref(props.height) // 画布高度

const skeletonDataRef = computed(() => props.skeletonData) // 骨骼数据

// 监听props中的宽高变化
watch(
  () => props.width,
  (newWidth) => {
    canvasWidth.value = newWidth
  },
)

watch(
  () => props.height,
  (newHeight) => {
    canvasHeight.value = newHeight
  },
)

const {
  currentFrame, // 当前帧
  isPlaying, // 是否播放
  playbackSpeed, // 播放速度
  totalFrames, // 总帧数
  formattedKeypoints, // 格式化关键点
  formattedBoundingBoxes, // 格式化边界框
  scaleFactors, // 缩放因子
  play: playAnimation, // 播放
  pause: pauseAnimation, // 暂停
  nextFrame, // 下一帧
  previousFrame, // 上一帧
  goToFrame, // 跳转帧
  cleanup, // 清理
  currentSkeletonData, // 当前骨骼数据
} = useSkeletonAnimation(skeletonDataRef, canvasWidth, canvasHeight, effectiveFrameRate)

// 视频播放事件处理
const handleVideoPlay = () => {
  isPlaying.value = true
  if (!animationFrameId) {
    animationFrameId = requestAnimationFrame(updateFrameFromVideo)
  }
}

// 视频暂停事件处理
const handleVideoPause = () => {
  isPlaying.value = false
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }
}

// 视频结束事件处理
const handleVideoEnded = () => {
  isPlaying.value = false
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }
  // 重置到第一帧
  manualGoToFrame(0)
}

// 视频相关方法
const onVideoLoaded = () => {
  if (videoRef.value) {
    // 设置视频的播放速度
    videoRef.value.playbackRate = playbackSpeed.value

    // 绑定播放、暂停和结束事件监听器
    videoRef.value.addEventListener('play', handleVideoPlay)
    videoRef.value.addEventListener('pause', handleVideoPause)
    videoRef.value.addEventListener('ended', handleVideoEnded)

    // 发出视频加载完成事件，传递视频总帧数
    const videoFrames = Math.floor(videoRef.value.duration * effectiveFrameRate.value)
    emit('videoLoaded', videoFrames)
  }
}

// 播放方法 - 控制视频播放和帧数同步
const play = () => {
  if (videoRef.value) {
    videoRef.value.play()
  }
  // 设置播放状态并启动帧数同步
  isPlaying.value = true
  // 启动 requestAnimationFrame 循环
  if (!animationFrameId) {
    animationFrameId = requestAnimationFrame(updateFrameFromVideo)
  }
}

// 暂停方法 - 同时控制动画和视频
const pause = () => {
  if (videoRef.value) {
    videoRef.value.pause()
  }
  pauseAnimation()

  // 取消 requestAnimationFrame
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }
}

// 监听播放速度变化，同步更新视频播放速度
watch(playbackSpeed, (newSpeed) => {
  if (videoRef.value) {
    videoRef.value.playbackRate = newSpeed
  }
})

// 计算内容框的位置和尺寸，显示实际渲染区域
const contentBox = computed(() => {
  if (!currentSkeletonData.value) return { width: 0, height: 0, top: 0, left: 0 }

  const { xOffset, yOffset } = scaleFactors.value

  // 使用 scaleFactors 计算的偏移值来确定内容框位置
  const renderWidth = canvasWidth.value - (xOffset * 2)
  const renderHeight = canvasHeight.value - (yOffset * 2)

  return {
    width: renderWidth,
    height: renderHeight,
    top: yOffset,
    left: xOffset,
  }
})

// 渲染选项
const renderOptions = computed(() => {
  return {
    ...props.renderOptions,
    pointColor: props.renderOptions.pointColor || getCSSVariableValue('--color-skeleton-point-default'), // 关键点颜色
    lineColor: props.renderOptions.lineColor || getCSSVariableValue('--color-skeleton-line-default'), // 线条颜色
    pointSize: props.renderOptions.pointSize || 5, // 关键点大小
    lineWidth: props.renderOptions.lineWidth || 2, // 线条宽度
    bboxColor: props.renderOptions.bboxColor || getCSSVariableValue('--color-skeleton-bbox-default'), // 边界框颜色
    bboxLineWidth: props.renderOptions.bboxLineWidth || 2, // 边界框宽度
    offsetX: props.renderOptions.offsetX || 0, // 偏移x
    offsetY: props.renderOptions.offsetY || 0, // 偏移y
  }
})

// 计算视频总帧数（基于视频时长和帧率）
const videoTotalFrames = computed(() => {
  if (videoRef.value && videoRef.value.duration) {
    return Math.floor(videoRef.value.duration * effectiveFrameRate.value)
  }
  return totalFrames.value // 如果视频未加载，使用骨骼数据帧数
})

// 显示用的帧数（从1开始，而不是从0开始）
const displayFrameNumber = computed({
  get: () => currentFrame.value + 1,
  set: (value) => {
    // 允许跳转到视频范围内的任意帧
    const frameIndex = Math.max(1, Math.min(videoTotalFrames.value, Number(value))) - 1
    manualGoToFrame(frameIndex)
  },
})

// 进度条百分比（基于视频总长度）
const progressPercentage = computed(() => {
  if (videoTotalFrames.value === 0) return 0
  return Math.min(100, (currentFrame.value / (videoTotalFrames.value - 1)) * 100)
})

// 拖拽状态
const isDragging = ref(false)

// 拖拽相关状态
const isDraggingPoint = ref(false)
const selectedPoint = ref<{ personIndex: number, pointIndex: number } | null>(null)
const dragOffset = ref({ x: 0, y: 0 })

// 跟踪鼠标悬停的点
const hoverPoint = ref<{ personIndex: number, pointIndex: number } | null>(null)

// 区分点击和拖拽的状态
const mouseDownPosition = ref<{ x: number, y: number } | null>(null)
const hasDragged = ref(false)
const dragThreshold = 5 // 拖拽距离阈值（像素）

// 存储被点击的点
const selectedPoints = ref<Array<{ personIndex: number, pointIndex: number }>>([]);
// 选中点的颜色
const selectedPointColor = ref(getCSSVariableValue('--color-skeleton-point-selected')); // 选中颜色

// 原始数据备份（用于重置功能）
const originalSkeletonData = ref<SkeletonFrame[]>([]);

// 复制粘贴功能数据结构
interface CopiedPoint {
  personIndex: number;
  pointIndex: number;
  x: number; // 显示坐标
  y: number; // 显示坐标
  originalX: number; // 原始坐标
  originalY: number; // 原始坐标
}

// 复制的点数据存储
const copiedPoints = ref<CopiedPoint[]>([]);
const hasCopiedData = computed(() => copiedPoints.value.length > 0);

// 检测当前帧是否有骨骼数据
const hasCurrentFrameData = computed(() => {
  return (currentSkeletonData.value?.instance_info[0]?.bbox_score ?? 0) > 0;
});

const canCopy = computed(() => selectedPoints.value.length === 0 || !hasCurrentFrameData.value);

// 检测点击是否命中关键点
const hitTestPoint = (x: number, y: number) => {
  if (!formattedKeypoints.value.length) return null;

  const options = renderOptions.value;
  // console.log(options, 'optionss')
  const pointSize = options.pointSize || 5;
  const threshold = pointSize + 5; // 增加点击容差

  for (let personIndex = 0; personIndex < formattedKeypoints.value.length; personIndex++) {
    const person = formattedKeypoints.value[personIndex];

    for (let pointIndex = 0; pointIndex < person.length; pointIndex++) {
      const point = person[pointIndex];

      const dx = point.x - x;
      const dy = point.y - y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance <= threshold) {
        return { personIndex, pointIndex };
      }
    }
  }

  return null;
}

// 处理鼠标按下事件
const handleCanvasMouseDown = (event: MouseEvent) => {
  if (!showSkeleton.value) return;

  const canvas = skeletonCanvas.value;
  if (!canvas) return;

  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  // 记录鼠标按下位置
  mouseDownPosition.value = { x, y };
  hasDragged.value = false;

  // 检测是否点击到关键点
  const hit = hitTestPoint(x, y);
  console.log('点击到关键点:', hit);
  if (hit) {
    // 启动拖拽操作
    isDraggingPoint.value = true;
    selectedPoint.value = hit;

    // 计算偏移量
    const point = formattedKeypoints.value[hit.personIndex][hit.pointIndex];
    dragOffset.value = {
      x: point.x - x,
      y: point.y - y
    };

    // 阻止默认行为和冒泡
    event.preventDefault();
    event.stopPropagation();
  }
}

// 处理鼠标移动事件
const handleCanvasMouseMove = (event: MouseEvent) => {
  const canvas = skeletonCanvas.value;
  if (!canvas || !showSkeleton.value) return;

  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  // 检测是否发生了拖拽（移动距离超过阈值）
  if (mouseDownPosition.value && !hasDragged.value) {
    const dx = x - mouseDownPosition.value.x;
    const dy = y - mouseDownPosition.value.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    if (distance > dragThreshold) {
      hasDragged.value = true;
    }
  }

  // 如果正在拖拽，处理拖拽逻辑
  if (isDraggingPoint.value && selectedPoint.value) {
    // 更新关键点位置
    const { personIndex, pointIndex } = selectedPoint.value;

    const newKeypoints = [...formattedKeypoints.value];
    const newPersonKeypoints = [...newKeypoints[personIndex]];

    // 更新点的位置
    newPersonKeypoints[pointIndex] = {
      ...newPersonKeypoints[pointIndex],
      x: x + dragOffset.value.x,
      y: y + dragOffset.value.y
    };

    // 更新数据
    newKeypoints[personIndex] = newPersonKeypoints;

    // 更新原始数据中的关键点位置
    if (currentSkeletonData.value && currentSkeletonData.value.instance_info[personIndex]) {
      const { x: xScale, y: yScale, xOffset, yOffset } = scaleFactors.value;

      // 将画布坐标转换回原始坐标
      const originalX = ((x + dragOffset.value.x) - xOffset) / xScale;
      const originalY = ((y + dragOffset.value.y) - yOffset) / yScale;

      // 更新原始数据
      const keypoints = currentSkeletonData.value.instance_info[personIndex].keypoints;
      keypoints[pointIndex][0] = originalX;
      keypoints[pointIndex][1] = originalY;
    }

    // 重新渲染
    updateRendering();
  } else {
    // 如果没有拖拽，检测鼠标是否悬停在点上
    const hit = hitTestPoint(x, y);
    hoverPoint.value = hit;

    // 如果悬停状态改变，重新渲染以显示或隐藏边框
    updateRendering();
  }
}

// 处理鼠标释放事件
const handleCanvasMouseUp = () => {
  // 如果是拖拽操作结束
  if (isDraggingPoint.value && selectedPoint.value) {
    if (!hasDragged.value) {
      // 纯点击：切换选中状态
      togglePointSelection(selectedPoint.value);
    } else {
      // 拖拽操作：如果点未选中，则自动选中；如果已选中，保持选中状态
      const isAlreadySelected = selectedPoints.value.some(
        p => p.personIndex === selectedPoint.value!.personIndex &&
          p.pointIndex === selectedPoint.value!.pointIndex
      );

      if (!isAlreadySelected) {
        // 如果点未选中，自动添加到选中列表
        selectedPoints.value.push({ ...selectedPoint.value });
        console.log('拖拽结束，自动选中点:', selectedPoint.value);
      }
    }
  }

  // 重置所有拖拽相关状态
  isDraggingPoint.value = false;
  selectedPoint.value = null;
  mouseDownPosition.value = null;
  hasDragged.value = false;

  updateRendering(); // 重新渲染以更新视觉效果
}

// 处理鼠标离开画布事件
const handleCanvasMouseLeave = () => {
  isDraggingPoint.value = false;
  selectedPoint.value = null;
  hoverPoint.value = null; // 清除悬停状态
  mouseDownPosition.value = null; // 重置鼠标按下位置
  hasDragged.value = false; // 重置拖拽状态
  updateRendering(); // 重新渲染以移除边框
}

// 进度条点击事件
const handleProgressClick = (event: MouseEvent) => {
  if (!progressBarRef.value || isDragging.value) return

  const rect = progressBarRef.value.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (clickX / rect.width) * 100))
  const targetFrame = Math.round((percentage / 100) * (videoTotalFrames.value - 1))

  manualGoToFrame(targetFrame)
}

// 滑块拖拽开始
const handleThumbMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragging.value = true

  const handleMouseMove = (moveEvent: MouseEvent) => {
    if (!progressBarRef.value || !isDragging.value) return

    const rect = progressBarRef.value.getBoundingClientRect()
    const moveX = moveEvent.clientX - rect.left
    const percentage = Math.max(0, Math.min(100, (moveX / rect.width) * 100))
    const targetFrame = Math.round((percentage / 100) * (videoTotalFrames.value - 1))

    manualGoToFrame(targetFrame)
  }

  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 渲染骨骼
const renderSkeleton = () => {
  if (!skeletonCanvas.value) return;

  const ctx = skeletonCanvas.value.getContext('2d');
  if (!ctx) return;

  // 清除画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);

  // 如果不显示骨骼，直接返回（只清除画布）
  if (!showSkeleton.value) return;

  // 如果当前帧超出骨骼数据范围，不显示骨骼
  if (currentFrame.value >= totalFrames.value) return;

  const options = renderOptions.value;
  const offsetX = options.offsetX;
  const offsetY = options.offsetY;

  // 绘制骨架线条
  if (formattedKeypoints.value.length > 0) {
    formattedKeypoints.value.forEach((item, personIndex) => {
      // 绘制线条
      mappings.value.SKELETON_LINKS.forEach(([i, j]: [number, number]) => {
        if (i >= item.length || j >= item.length) return;

        const point1 = item[i];
        const point2 = item[j];

        ctx.beginPath();
        ctx.moveTo(point1.x + offsetX, point1.y + offsetY);
        ctx.lineTo(point2.x + offsetX, point2.y + offsetY);
        ctx.strokeStyle = options.lineColor;
        ctx.lineWidth = options.lineWidth;
        ctx.stroke();
      });

      // 绘制关键点
      item.forEach((value, pointIndex) => {

        ctx.beginPath();

        // 判断是否是当前选中的点或悬停的点
        const isSelected = isDraggingPoint.value &&
          selectedPoint.value?.personIndex === personIndex &&
          selectedPoint.value?.pointIndex === pointIndex;

        const isHovered = !isDraggingPoint.value &&
          hoverPoint.value?.personIndex === personIndex &&
          hoverPoint.value?.pointIndex === pointIndex;

        // 检查点是否在选中列表中
        const isClickSelected = selectedPoints.value.some(
          p => p.personIndex === personIndex && p.pointIndex === pointIndex
        );

        // 正常点的大小和颜色
        let pointSize = options.pointSize;
        let pointColor = options.pointColor;

        // 如果是选中的点，增大尺寸并改变颜色
        if (isSelected) {
          // pointSize = options.pointSize * 1.5;
          // pointColor = '#ff0000';
        } else if (isClickSelected) {
          // 如果是点击选中的点，使用选中颜色
          pointColor = selectedPointColor.value;
          // pointSize = options.pointSize * 1.2;
        } else if (isHovered) {
          // 如果是悬停的点，稍微增大尺寸
          // pointSize = options.pointSize * 1.2;
        }

        ctx.arc(value.x + offsetX, value.y + offsetY, pointSize, 0, 2 * Math.PI);
        ctx.fillStyle = pointColor;
        ctx.fill();

        // 为选中的点或悬停的点添加边框
        // if (isSelected || isHovered || isClickSelected) {
        if (isHovered || isClickSelected) {
          ctx.strokeStyle = getCSSVariableValue('--color-white');
          ctx.lineWidth = 2;
          ctx.stroke();
        }
      });
    });
  }
}

// 渲染边界框
const renderBoundingBoxes = () => {
  if (!bboxCanvas.value || !showBBox.value) return

  const ctx = bboxCanvas.value.getContext('2d')
  if (!ctx) return

  // 清除画布
  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

  // 如果当前帧超出骨骼数据范围，不显示边界框
  if (currentFrame.value >= totalFrames.value) return

  const options = renderOptions.value
  const offsetX = options.offsetX
  const offsetY = options.offsetY

  // 绘制边界框
  if (formattedBoundingBoxes.value.length > 0) {
    formattedBoundingBoxes.value.forEach((box) => {
      ctx.beginPath()
      ctx.rect(box.x + offsetX, box.y + offsetY, box.width, box.height)
      ctx.strokeStyle = options.bboxColor
      ctx.lineWidth = options.bboxLineWidth
      ctx.stroke()
    })
  }
}

// 更新所有渲染内容
const updateRendering = () => {
  renderSkeleton()
  renderBoundingBoxes()
}

// 监听帧变化
watch(currentFrame, () => {
  updateRendering()
})

// 监听骨骼显示状态变化
watch(showSkeleton, (newValue) => {
  console.log('骨骼显示状态变更为:', newValue)
  if (!newValue && skeletonCanvas.value) {
    // 如果不显示骨骼，清除骨骼画布
    const ctx = skeletonCanvas.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)
    }
  } else {
    // 如果显示骨骼，重新渲染
    renderSkeleton()
  }
})

// 监听边界框显示状态变化
watch(showBBox, (newValue) => {
  console.log('边界框显示状态变更为:', newValue)
  if (!newValue && bboxCanvas.value) {
    // 如果不显示边界框，清除边界框画布
    const ctx = bboxCanvas.value.getContext('2d')
    if (ctx) {
      ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)
    }
  }
})

watch(bboxCanvas, (canvas) => {
  if (canvas && showBBox.value) {
    nextTick(() => {
      renderBoundingBoxes()
    })
  }
})

// 监听数据变化
watch(
  [formattedKeypoints, formattedBoundingBoxes],
  () => {
    updateRendering()
  },
  { deep: true },
)



// 动画帧ID，用于取消动画
let animationFrameId: number | null = null

// 使用 requestAnimationFrame 平滑更新帧数
const updateFrameFromVideo = () => {
  if (videoRef.value && videoRef.value.duration && isPlaying.value) {
    // 根据视频当前时间计算对应的帧数
    const currentTime = videoRef.value.currentTime
    const calculatedFrame = Math.floor(currentTime * effectiveFrameRate.value)

    // 允许帧数超出骨骼数据范围，让视频完整播放
    if (calculatedFrame >= 0) {
      currentFrame.value = calculatedFrame
    }

    // 如果还在播放，继续下一帧
    if (isPlaying.value) {
      animationFrameId = requestAnimationFrame(updateFrameFromVideo)
    }
  }
}

// 手动跳转到指定帧（需要同步视频时间）
const manualGoToFrame = (frame: number) => {
  goToFrame(frame)

  // 同步视频时间
  if (videoRef.value && videoRef.value.duration) {
    const timeInSeconds = frame / effectiveFrameRate.value
    if (timeInSeconds <= videoRef.value.duration) {
      videoRef.value.currentTime = timeInSeconds
    }
  }
}

// 手动下一帧
const manualNextFrame = () => {
  const newFrame = Math.min(currentFrame.value + 1, totalFrames.value - 1)
  manualGoToFrame(newFrame)
}

// 手动上一帧
const manualPreviousFrame = () => {
  const newFrame = Math.max(currentFrame.value - 1, 0)
  manualGoToFrame(newFrame)
}

// 创建原始数据备份
const createBackup = () => {
  originalSkeletonData.value = deepClone(props.skeletonData);
};

onMounted(() => {
  updateRendering()
  if (props.skeletonData && props.skeletonData.length > 0) {
    createBackup();
  }
})

onBeforeUnmount(() => {
  cleanup()

  // 取消 requestAnimationFrame
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }

  // 移除视频事件监听器
  if (videoRef.value) {
    videoRef.value.removeEventListener('play', handleVideoPlay)
    videoRef.value.removeEventListener('pause', handleVideoPause)
    videoRef.value.removeEventListener('ended', handleVideoEnded)
  }
})

const getSkeletonData = () => {
  // 返回完整的骨骼数据，包括所有帧
  return skeletonDataRef.value;
};

// 获取当前帧的骨骼数据
const getCurrentFrameData = () => {
  return currentSkeletonData.value;
};


// 切换点的选中状态
const togglePointSelection = (point: { personIndex: number, pointIndex: number }) => {
  // 检查点是否已经被选中
  const index = selectedPoints.value.findIndex(
    p => p.personIndex === point.personIndex && p.pointIndex === point.pointIndex
  );

  if (index !== -1) {
    // 如果已选中，则移除
    selectedPoints.value.splice(index, 1);
  } else {
    // 如果未选中，则添加
    selectedPoints.value.push({ ...point });
  }

  // 重新渲染以更新视觉效果
  updateRendering();
}

const selectNodeGroup = (groupName: string | string[]) => {
  const groupNames = Array.isArray(groupName) ? groupName : [groupName];
  console.log('选择节点组:', groupNames);

  // 检查所有指定组是否都已被选中
  const allSelected = groupNames.every(name => selectedNodeGroups.value.has(name));

  // 使用 Set 存储目标点的唯一标识符，避免重复
  const targetPoints = new Set<string>();
  // 存储目标点的完整对象信息
  const targetPointObjects: Array<{ personIndex: number; pointIndex: number }> = [];

  /**
   * 单次遍历计算所有目标点
   */
  groupNames.forEach(groupName => {
    // 获取当前组对应的节点索引数组
    const nodeIndices = NODE_GROUPS[groupName as keyof typeof NODE_GROUPS] || [];

    // 遍历所有人员的关键点数据
    formattedKeypoints.value.forEach((person, personIndex) => {
      // 遍历当前组的所有节点索引
      nodeIndices.forEach((pointIndex: number) => {
        // 检查节点索引是否在有效范围内
        if (pointIndex < person.length) {
          const key = `${personIndex}-${pointIndex}`;

          // 多个组包含相同节点
          if (!targetPoints.has(key)) {
            targetPoints.add(key);
            targetPointObjects.push({ personIndex, pointIndex });
          }
        }
      });
    });
  });

  /**
   * 批量更新选中状态
   * 根据当前选中状态决定是选中还是取消选中
   */
  if (allSelected) {
    // 从选中组集合中移除所有指定的组
    groupNames.forEach(name => selectedNodeGroups.value.delete(name));

    // 从已选中点数组中过滤掉目标点
    selectedPoints.value = selectedPoints.value.filter(
      p => !targetPoints.has(`${p.personIndex}-${p.pointIndex}`)
    );
  } else {
    // 将所有指定的组添加到选中组集合中
    groupNames.forEach(name => selectedNodeGroups.value.add(name));

    // 创建现有选中点的 Set
    const existingPoints = new Set(
      selectedPoints.value.map(p => `${p.personIndex}-${p.pointIndex}`)
    );

    // 筛选出尚未被选中的新点
    // 只添加不存在的点，避免重复
    const newPoints = targetPointObjects.filter(
      p => !existingPoints.has(`${p.personIndex}-${p.pointIndex}`)
    );

    // 批量添加新选中的点
    selectedPoints.value.push(...newPoints);
  }

  // 触发重新渲染以显示选中效果
  updateRendering();
};

// 清除选择
const clearSelection = () => {
  selectedNodeGroups.value.clear();
  selectedPoints.value = [];
  updateRendering();
}

// 检测是否可以进行左右互换
const canSwapLeftRight = computed(() => {
  // 检查当前帧是否有数据，以及是否有可互换的节点组对被同时选中
  return hasCurrentFrameData.value && SWAPPABLE_PAIRS.some(([left, right]) =>
    selectedNodeGroups.value.has(left) && selectedNodeGroups.value.has(right)
  );
});

// 获取可互换的节点组对列表
const getSwappablePairs = () => {
  return SWAPPABLE_PAIRS.filter(([left, right]) =>
    selectedNodeGroups.value.has(left) && selectedNodeGroups.value.has(right)
  );
};

// 左右互换功能
const swapLeftRightNodes = () => {
  if (!canSwapLeftRight.value) return;

  const swappablePairs = getSwappablePairs();
  console.log('执行左右互换:', swappablePairs);

  // 遍历所有可互换的节点组对
  swappablePairs.forEach(([leftGroup, rightGroup]) => {
    const leftIndices = NODE_GROUPS[leftGroup as keyof typeof NODE_GROUPS] || [];
    const rightIndices = NODE_GROUPS[rightGroup as keyof typeof NODE_GROUPS] || [];

    // 确保两组节点数量相同
    const minLength = Math.min(leftIndices.length, rightIndices.length);

    // 遍历所有人员
    if (formattedKeypoints.value.length > 0) {
      formattedKeypoints.value.forEach((person, personIndex) => {
        // 交换对应节点的坐标
        for (let i = 0; i < minLength; i++) {
          const leftIndex = leftIndices[i];
          const rightIndex = rightIndices[i];

          if (leftIndex < person.length && rightIndex < person.length) {
            // 交换显示数据的坐标
            const leftPoint = person[leftIndex];
            const rightPoint = person[rightIndex];

            const tempX = leftPoint.x;
            const tempY = leftPoint.y;

            leftPoint.x = rightPoint.x;
            leftPoint.y = rightPoint.y;
            rightPoint.x = tempX;
            rightPoint.y = tempY;

            // 交换原始数据的坐标
            if (currentSkeletonData.value && currentSkeletonData.value.instance_info[personIndex]) {
              const keypoints = currentSkeletonData.value.instance_info[personIndex].keypoints;

              const leftOriginal = keypoints[leftIndex];
              const rightOriginal = keypoints[rightIndex];

              const tempOriginalX = leftOriginal[0];
              const tempOriginalY = leftOriginal[1];

              leftOriginal[0] = rightOriginal[0];
              leftOriginal[1] = rightOriginal[1];
              rightOriginal[0] = tempOriginalX;
              rightOriginal[1] = tempOriginalY;
            }
          }
        }
      });
    }
  });

  // 重新渲染以显示互换效果
  updateRendering();
};

// 重置当前帧到初始状态
const resetCurrentFrame = () => {
  if (!originalSkeletonData.value.length || !currentSkeletonData.value) {
    console.log('没有可用的备份数据或当前帧数据');
    return;
  }

  const currentFrameIndex = currentFrame.value;
  if (currentFrameIndex >= originalSkeletonData.value.length) {
    console.log('当前帧索引超出备份数据范围');
    return;
  }

  // 从备份数据中恢复当前帧
  const originalFrame = originalSkeletonData.value[currentFrameIndex];
  const currentFrame_data = currentSkeletonData.value;

  // 逐个恢复关键点数据，保持响应式
  originalFrame.instance_info.forEach((originalInstance, instanceIndex) => {
    if (currentFrame_data.instance_info[instanceIndex]) {
      originalInstance.keypoints.forEach((originalKeypoint, keypointIndex) => {
        if (currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex]) {
          currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex][0] = originalKeypoint[0];
          currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex][1] = originalKeypoint[1];
        }
      });

      // 恢复边界框数据
      if (originalInstance.bbox && currentFrame_data.instance_info[instanceIndex].bbox) {
        currentFrame_data.instance_info[instanceIndex].bbox = deepClone(originalInstance.bbox);
      }
      if (originalInstance.bbox_score !== undefined) {
        currentFrame_data.instance_info[instanceIndex].bbox_score = originalInstance.bbox_score;
      }
    }
  });

  console.log('重置当前帧:', currentFrameIndex);
  console.log('重置后关键点:', currentFrame_data.instance_info[0]?.keypoints[0]);

  // 强制触发响应式更新
  nextTick(() => {
    updateRendering();
  });
};

// 重置所有帧到初始状态 - 完全重新初始化组件
const resetAllFrames = () => {
  if (!originalSkeletonData.value.length) {
    console.log('没有可用的备份数据');
    return;
  }
  pause();

  // 恢复骨骼数据
  const currentData = skeletonDataRef.value;

  // 逐帧恢复数据，保持响应式
  for (let frameIndex = 0; frameIndex < Math.min(currentData.length, originalSkeletonData.value.length); frameIndex++) {
    const originalFrame = originalSkeletonData.value[frameIndex];
    const currentFrame_data = currentData[frameIndex];

    // 逐个恢复关键点数据
    originalFrame.instance_info.forEach((originalInstance, instanceIndex) => {
      if (currentFrame_data.instance_info[instanceIndex]) {
        originalInstance.keypoints.forEach((originalKeypoint, keypointIndex) => {
          if (currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex]) {
            currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex][0] = originalKeypoint[0];
            currentFrame_data.instance_info[instanceIndex].keypoints[keypointIndex][1] = originalKeypoint[1];
          }
        });

        // 恢复边界框数据
        if (originalInstance.bbox && currentFrame_data.instance_info[instanceIndex].bbox) {
          currentFrame_data.instance_info[instanceIndex].bbox = deepClone(originalInstance.bbox);
        }
        if (originalInstance.bbox_score !== undefined) {
          currentFrame_data.instance_info[instanceIndex].bbox_score = originalInstance.bbox_score;
        }
      }
    });
  }

  // 重置所有组件状态
  selectedNodeGroups.value.clear();
  selectedPoints.value = [];
  isDraggingPoint.value = false;
  selectedPoint.value = null;
  hoverPoint.value = null;
  isDragging.value = false;

  manualGoToFrame(0); // 跳转到第一帧

  //  重置视频播放状态
  if (videoRef.value) {
    videoRef.value.pause();
  }

  nextTick(() => {
    createBackup();
    updateRendering();

    console.log('重置所有帧完成，组件已完全重新初始化');
  });
};

// 复制选中的点
const copySelectedPoints = () => {
  if (selectedPoints.value.length === 0) {
    console.log('没有选中的点可以复制');
    return;
  }

  if (!currentSkeletonData.value) {
    console.log('没有当前帧数据');
    return;
  }

  copiedPoints.value = [];

  selectedPoints.value.forEach(({ personIndex, pointIndex }) => {
    // 获取显示坐标
    if (formattedKeypoints.value[personIndex] && formattedKeypoints.value[personIndex][pointIndex]) {
      const displayPoint = formattedKeypoints.value[personIndex][pointIndex];

      // 获取原始坐标 - 添加null检查
      if (currentSkeletonData.value &&
        currentSkeletonData.value.instance_info[personIndex] &&
        currentSkeletonData.value.instance_info[personIndex].keypoints[pointIndex]) {
        const originalKeypoint = currentSkeletonData.value.instance_info[personIndex].keypoints[pointIndex];

        copiedPoints.value.push({
          personIndex,
          pointIndex,
          x: displayPoint.x,
          y: displayPoint.y,
          originalX: originalKeypoint[0],
          originalY: originalKeypoint[1],
        });
      }
    }
  });

  console.log(`复制了 ${copiedPoints.value.length} 个点的坐标:`, copiedPoints.value);
};

// 粘贴到当前帧
const pasteToCurrentFrame = () => {
  if (copiedPoints.value.length === 0) {
    console.log('没有复制的数据可以粘贴');
    return;
  }

  if (!currentSkeletonData.value) {
    console.log('没有当前帧数据');
    return;
  }

  let pastedCount = 0;

  copiedPoints.value.forEach(copiedPoint => {
    const { personIndex, pointIndex, originalX, originalY } = copiedPoint;

    // 检查目标帧是否有对应的人员和点
    if (currentSkeletonData.value &&
      currentSkeletonData.value.instance_info[personIndex] &&
      currentSkeletonData.value.instance_info[personIndex].keypoints[pointIndex]) {

      // 更新原始数据
      const keypoints = currentSkeletonData.value.instance_info[personIndex].keypoints;
      keypoints[pointIndex][0] = originalX;
      keypoints[pointIndex][1] = originalY;

      pastedCount++;
    }
  });

  // 粘贴成功后清除复制的内容，避免重复粘贴
  if (pastedCount > 0) {
    copiedPoints.value = [];
    console.log('粘贴完成，已清除复制的内容');
  }

  // 重新渲染以显示粘贴效果
  updateRendering();
};

// 线性插值功能
const handleInterpolation = (startFrameIndex: number, endFrameIndex: number) => {
  // 参数验证
  if (startFrameIndex < 0 || endFrameIndex < 0) {
    console.log('开始帧和结束帧必须大于等于0');
    return;
  }

  if (startFrameIndex >= totalFrames.value || endFrameIndex >= totalFrames.value) {
    console.log('开始帧和结束帧不能超过总帧数');
    return;
  }

  if (startFrameIndex >= endFrameIndex) {
    console.log('开始帧必须小于结束帧');
    return;
  }

  if (selectedPoints.value.length === 0) {
    console.log('请先选择要插值的关键点');
    return;
  }

  console.log(`开始插值处理：从第${startFrameIndex}帧到第${endFrameIndex}帧，共${selectedPoints.value.length}个选中点`);

  // 获取骨骼数据
  const skeletonData = skeletonDataRef.value;
  if (!skeletonData || skeletonData.length === 0) {
    console.log('没有可用的骨骼数据');
    return;
  }

  // 对每个选中的点进行插值处理
  selectedPoints.value.forEach(({ personIndex, pointIndex }) => {
    // 获取开始帧和结束帧的坐标
    const startFrameData = skeletonData[startFrameIndex];
    const endFrameData = skeletonData[endFrameIndex];

    if (!startFrameData?.instance_info[personIndex]?.keypoints[pointIndex] ||
      !endFrameData?.instance_info[personIndex]?.keypoints[pointIndex]) {
      console.log(`跳过插值：帧${startFrameIndex}或帧${endFrameIndex}中缺少人员${personIndex}的关键点${pointIndex}`);
      return;
    }

    const startKeypoint = startFrameData.instance_info[personIndex].keypoints[pointIndex];
    const endKeypoint = endFrameData.instance_info[personIndex].keypoints[pointIndex];

    const startX = startKeypoint[0];
    const startY = startKeypoint[1];

    const endX = endKeypoint[0];
    const endY = endKeypoint[1];

    // 计算总的帧数差
    const totalFrames_diff = endFrameIndex - startFrameIndex;

    // 对中间的每一帧进行线性插值
    for (let frameIndex = startFrameIndex + 1; frameIndex < endFrameIndex; frameIndex++) {
      const currentFrameData = skeletonData[frameIndex];

      // 检查当前帧是否有骨骼数据
      if ((currentFrameData?.instance_info[0]?.bbox_score ?? 0) <= 0) {
        console.log(`跳过插值：帧${frameIndex}原本就没有骨骼数据`);
        continue;
      }

      if (!currentFrameData?.instance_info[personIndex]?.keypoints[pointIndex]) {
        console.log(`跳过插值：帧${frameIndex}中缺少人员${personIndex}的关键点${pointIndex}`);
        continue;
      }

      // 计算插值比例
      const ratio = (frameIndex - startFrameIndex) / totalFrames_diff;

      // 线性插值计算
      const interpolatedX = startX + (endX - startX) * ratio;
      const interpolatedY = startY + (endY - startY) * ratio;

      // 更新关键点坐标
      const keypoints = currentFrameData.instance_info[personIndex].keypoints;
      keypoints[pointIndex][0] = interpolatedX;
      keypoints[pointIndex][1] = interpolatedY;
    }

    console.log(`完成关键点插值：人员${personIndex}的关键点${pointIndex}`);
  });

  console.log('插值处理完成');


  updateRendering();
};

// 恢复选中点状态
const restoreSelectedPoints = (points: Set<string> | string[] | Array<{ personIndex: number, pointIndex: number }>) => {
  try {
    selectedPoints.value = []

    if (points instanceof Set) {
      // 处理Set<string>格式
      points.forEach(pointStr => {
        const [personIndex, pointIndex] = pointStr.split('-').map(Number)
        if (!isNaN(personIndex) && !isNaN(pointIndex)) {
          selectedPoints.value.push({ personIndex, pointIndex })
        }
      })
    } else if (Array.isArray(points)) {
      if (points.length > 0 && typeof points[0] === 'string') {
        // 处理string[]格式
        points.forEach(pointStr => {
          const [personIndex, pointIndex] = (pointStr as string).split('-').map(Number)
          if (!isNaN(personIndex) && !isNaN(pointIndex)) {
            selectedPoints.value.push({ personIndex, pointIndex })
          }
        })
      } else {
        // 处理对象数组格式
        selectedPoints.value = [...points as Array<{ personIndex: number, pointIndex: number }>]
      }
    }

    console.log('已恢复选中点状态:', selectedPoints.value)
  } catch (error) {
    console.error('恢复选中点状态失败:', error)
  }
}

// 恢复复制数据状态
const restoreCopiedData = (data: any) => {
  try {
    if (data && Array.isArray(data)) {
      copiedPoints.value = [...data]
      console.log('已恢复复制数据状态:', copiedPoints.value.length, '个点')
    }
  } catch (error) {
    console.error('恢复复制数据状态失败:', error)
  }
}

// 获取完整状态（用于状态缓存）
const getFullState = () => {
  return {
    currentFrame: currentFrame.value,
    isPlaying: isPlaying.value,
    playbackSpeed: playbackSpeed.value,
    selectedPoints: selectedPoints.value,
    copiedData: copiedPoints.value,
    videoCurrentTime: videoRef.value?.currentTime || 0,
    skeletonData: getSkeletonData() // 获取修改后的骨骼数据
  }
}

defineExpose({
  getSkeletonData,
  getCurrentFrameData,
  currentFrame,
  totalFrames,
  play,
  pause,
  manualNextFrame,
  manualPreviousFrame,
  manualGoToFrame,
  selectNodeGroup,
  canSwapLeftRight,
  swapLeftRightNodes,
  clearSelection,
  resetCurrentFrame,
  resetAllFrames,
  copySelectedPoints,
  pasteToCurrentFrame,
  canCopy,
  hasCopiedData,
  hasCurrentFrameData, // 暴露当前帧数据检测状态
  selectedPoints, // 暴露选中的点
  handleInterpolation, // 暴露插值函数
  // 状态管理相关方法
  restoreSelectedPoints,
  restoreCopiedData,
  getFullState,
  isPlaying,
  playbackSpeed,
  videoRef,
});

</script>

<style scoped lang="scss">
.rc-box {
  position: absolute;
  border: 1px solid red;
  z-index: 100;
  pointer-events: none;
}

.skeleton-player-with-bg {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  user-select: none;
}

.player-box {
  flex: 1;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.player-container {
  position: relative;
  overflow: hidden;
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.skeleton-canvas,
.bbox-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.skeleton-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  /* 修改为auto以允许鼠标事件 */
  cursor: default;

  &:hover {
    cursor: grab;
  }

  &:active {
    cursor: grabbing;
  }
}

.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: rem(76);
  padding-left: rem(28);

  .player-process {
    display: flex;
    align-items: center;
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: 400;
    font-size: rem(18);
    color: #FFFFFF;
    line-height: rem(26);
    text-align: right;
    font-style: normal;
    height: rem(76);

    .frame-info {
      width: rem(130);
      height: 100%;
      margin-right: rem(12);
      display: flex;
      align-items: center;

      span {
        &:first-child {
          width: rem(45);
          margin-right: rem(5);
          text-align: left;
        }

        &:last-child {
          flex: 1;
          text-align: left;
        }
      }
    }

    // 自定义进度条样式
    .custom-progress-bar {
      width: rem(200);
      height: rem(12);
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;

      .progress-track {
        width: 100%;
        height: rem(12);
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: rem(10);
        position: relative;
      }

      .progress-fill {
        height: 100%;
        background-color: #ffffff;
        border-radius: rem(10);
        transition: width 0.1s ease;
      }

      .progress-thumb {
        position: absolute;
        top: 50%;
        width: rem(24);
        height: rem(24);
        background-color: #ffffff;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        cursor: grab;
        box-shadow: 0 0 rem(16) 0 rgba(255, 255, 255, 0.7);
        transition: left 0.1s ease;

        &:hover {
          transform: translate(-50%, -50%) scale(1.1);
        }

        &:active {
          cursor: grabbing;
          transform: translate(-50%, -50%) scale(1.2);
        }
      }
    }
  }

  .play-cavans {
    cursor: pointer;
  }

  .player-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: rem(76);
    width: rem(160);

    .prev-btn,
    .next-btn {
      font-size: rem(20);
      color: #fff;
      cursor: pointer;
      flex: 1;
    }

    .prev-btn {
      text-align: right;
      padding-right: rem(15);
    }

    .next-btn {
      text-align: left;
      padding-left: rem(15);
    }

    .play-input {
      width: rem(60);
      height: rem(32);

      input {
        width: 100%;
        height: 100%;
        text-align: center;
        border-radius: rem(2);
        background-color: rgba(61, 60, 70, 1);
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: rem(18);
        color: #FFFFFF;
        line-height: rem(26);
        font-style: normal;
        text-align: center;
      }
    }
  }

  .player-controls {
    display: flex;
    align-items: center;
    height: rem(76);
    padding-right: rem(28);

    .point-watch,
    .rw-watch {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: rem(24);

      span {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(18);
        color: #FFFFFF;
        line-height: rem(26);
        text-align: right;
        font-style: normal;
      }
    }

    .rw-watch {
      margin-right: 0;
    }

    img {
      width: rem(20);
      height: rem(20);
      margin-right: rem(3);
    }
  }
}

// 复制粘贴按钮样式
.copy-btn {
  background-color: #4CAF50 !important;

  &:hover:not(:disabled) {
    background-color: #45a049 !important;
  }

  &:disabled {
    background-color: #cccccc !important;
    cursor: not-allowed !important;
  }
}

.paste-btn {
  background-color: #2196F3 !important;

  &:hover:not(:disabled) {
    background-color: #1976D2 !important;
  }

  &:disabled {
    background-color: #cccccc !important;
    cursor: not-allowed !important;
  }
}

// 骨骼选择控制区域样式
.skeleton-selection-controls {
  width: 100%;
  padding: rem(20);
  background-color: rgba(0, 0, 0, 0.5);

  .selection-group {
    display: flex;
    flex-wrap: wrap;
    gap: rem(10);
    justify-content: center;

    .skeleton-btn {
      padding: rem(8) rem(16);
      background-color: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: rem(4);
      color: #ffffff;
      font-size: rem(14);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
      }

      &.active {
        background-color: #00ff00;
        color: #000000;
        border-color: #00ff00;
      }

      &:disabled {
        background-color: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.5);
        cursor: not-allowed;
        border-color: rgba(255, 255, 255, 0.1);
      }

      &.swap-btn {
        background-color: #ff9800;

        &:hover:not(:disabled) {
          background-color: #f57c00;
        }
      }

      &.clear-btn {
        background-color: #f44336;

        &:hover {
          background-color: #d32f2f;
        }
      }

      &.reset-btn {
        background-color: #9c27b0;

        &:hover {
          background-color: #7b1fa2;
        }
      }
    }
  }
}
</style>
