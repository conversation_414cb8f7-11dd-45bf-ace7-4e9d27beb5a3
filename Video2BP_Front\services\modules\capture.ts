import type { AxiosProgressEvent } from 'axios';
import type {
	UploadResponse,
	CreateFileRequest,
	CreateFileResponseData,
} from '~/types/capture';
import type { ApiService } from '../api';

const API_PREFIX = '/video'; // 该模块的 API 前缀

export function createVideoService(apiService: ApiService) {
	return {
		/**
		 * @param file 要上传的视频文件
		 * @param onProgress 上传进度回调
		 * @returns 上传结果，包含视频URL
		 */
		uploadSVideo(
			file: File,
			onProgress?: (e: AxiosProgressEvent) => void
		): Promise<UploadResponse> {
			const formData = new FormData();
			formData.append('video', file);
			formData.append('filename', file.name);

			return apiService.post<UploadResponse>(
				`${API_PREFIX}/simple-upload`,
				formData,
				{
					headers: {
						'Content-Type': 'multipart/form-data',
					},
					onUploadProgress: onProgress,
				}
			);
		},

		/**
		 * 创建文件 - 视频上传完成后创建文件
		 * @param data 创建文件的请求参数
		 * @returns 创建文件的结果
		 */
		createFile(data: CreateFileRequest): Promise<CreateFileResponseData> {
			return apiService.post<CreateFileResponseData>(
				`${API_PREFIX}/create-file`,
				data
			);
		},
	};
}
