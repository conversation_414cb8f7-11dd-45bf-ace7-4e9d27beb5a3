export type PlanType = 'year' | 'month'

export type ValueMap = {
  year: string
  month: string
}

export type Feature = {
  name: string
  valueMap: ValueMap
}

export type PlanConfig = {
  id: 'free' | 'economy' | 'premium'
  title: string
  isCurrent: boolean
  buttonText: string
  features: Feature[]
}

export type PriceConfig = {
  free: ValueMap
  economy: ValueMap
  premium: ValueMap
}

export type PlanTab = {
  label: string
  value: PlanType
}