@use './variables.scss' as *;

// ============================================
// CSS 自定义属性 
// ============================================

// 默认主题（深色主题）
:root {
  // 功能颜色
  --color-primary: #{$primary-color-dark};
  --color-success: #{$success-color-dark};
  --color-error: #{$error-color-dark};
  --color-warning: #{$warning-color-dark};
  
  // 基础颜色
  --color-white: #{$white};
  --color-black: #{$black};
  
  // 背景颜色
  --color-background-primary: #{$background-primary-dark};
  --color-background-secondary: #{$background-secondary-dark};
  --color-background-tertiary: #{$background-tertiary-dark};
  --color-background-gradient-from: #{$background-tertiary-dark};
  --color-background-gradient-to: #{$background-primary-dark};
  
  // 文字颜色
  --color-text-primary: #{$text-primary-dark};
  --color-text-secondary: #{$text-secondary-dark};
  --color-text-inverse: #{$text-inverse-dark};
  
  // 边框颜色
  --color-border-primary: #{$border-primary-dark};
  --color-border-secondary: #{$border-secondary-dark};
  --color-border-light: #{$border-light-dark};
  
  // 透明度颜色
  --color-opacity-white-10: #{$opacity-white-10};
  --color-opacity-white-20: #{$opacity-white-20};
  --color-opacity-white-30: #{$opacity-white-30};
  --color-opacity-white-50: #{$opacity-white-50};
  --color-opacity-white-70: #{$opacity-white-70};
  
  // 骨骼检测颜色
  --color-skeleton-point-default: #{$skeleton-point-default};
  --color-skeleton-point-selected: #{$skeleton-point-selected};
  --color-skeleton-point-hover: #{$skeleton-point-hover};
  --color-skeleton-point-red: #{$skeleton-point-red};
  --color-skeleton-point-green: #{$skeleton-point-green};
  --color-skeleton-line-default: #{$skeleton-line-default};
  --color-skeleton-line-selected: #{$skeleton-line-selected};
  --color-skeleton-bbox-default: #{$skeleton-bbox-default};
  --color-skeleton-bbox-selected: #{$skeleton-bbox-selected};
}

// 浅色主题
[data-theme="light"] {
  // 功能颜色
  --color-primary: #{$primary-color};
  --color-success: #{$success-color};
  --color-error: #{$error-color};
  --color-warning: #{$warning-color};
  
  // 基础颜色
  --color-white: #{$white};
  --color-black: #{$black};
  
  // 背景颜色
  --color-background-primary: #{$white};
  --color-background-secondary: #{$background-color};
  --color-background-tertiary: #fafafa;
  --color-background-gradient-from: #fafafa;
  --color-background-gradient-to: #{$white};
  
  // 文字颜色
  --color-text-primary: #{$text-color};
  --color-text-secondary: #{$text-color-secondary};
  --color-text-inverse: #{$white};
  
  // 边框颜色
  --color-border-primary: #{$border-color};
  --color-border-secondary: #f0f0f0;
  --color-border-light: rgba(0, 0, 0, 0.1);
  
  // 透明度颜色
  --color-opacity-white-10: rgba(0, 0, 0, 0.1);
  --color-opacity-white-20: rgba(0, 0, 0, 0.2);
  --color-opacity-white-30: rgba(0, 0, 0, 0.3);
  --color-opacity-white-50: rgba(0, 0, 0, 0.5);
  --color-opacity-white-70: rgba(0, 0, 0, 0.7);
  
  // 骨骼检测颜色（浅色主题下的调整）
  --color-skeleton-point-default: #{$success-color};
  --color-skeleton-point-selected: #{$error-color};
  --color-skeleton-point-hover: #{$primary-color};
  --color-skeleton-point-red: #{$error-color};
  --color-skeleton-point-green: #{$success-color};
  --color-skeleton-line-default: #722ed1;
  --color-skeleton-line-selected: #{$warning-color};
  --color-skeleton-bbox-default: #13c2c2;
  --color-skeleton-bbox-selected: #{$error-color};
}

// ============================================
// SCSS 函数和混入
// ============================================

// 获取主题颜色的函数
@function theme-color($color-name) {
  @return var(--color-#{$color-name});
}

// 常用的主题颜色混入
@mixin theme-background-primary {
  background-color: theme-color('background-primary');
}

@mixin theme-background-secondary {
  background-color: theme-color('background-secondary');
}

@mixin theme-text-primary {
  color: theme-color('text-primary');
}

@mixin theme-text-secondary {
  color: theme-color('text-secondary');
}

@mixin theme-border-primary {
  border-color: theme-color('border-primary');
}

@mixin theme-gradient-background {
  background: linear-gradient(to top, theme-color('background-gradient-from') 0%, theme-color('background-gradient-to') 100%);
}

// 骨骼检测相关混入
@mixin skeleton-point-default {
  background-color: theme-color('skeleton-point-default');
  border-color: theme-color('skeleton-point-default');
}

@mixin skeleton-point-red {
  background-color: theme-color('skeleton-point-red');
  border-color: theme-color('skeleton-point-red');
}

@mixin skeleton-point-green {
  background-color: theme-color('skeleton-point-green');
  border-color: theme-color('skeleton-point-green');
}
