from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON>NResponse
from starlette.status import HTTP_401_UNAUTHORIZED
from jose import JWTError, jwt
from datetime import datetime

from core.config import settings
from core.e import <PERSON>rror<PERSON><PERSON>, ErrorMessage


class JWTAuthMiddleware(BaseHTTPMiddleware):
    """JWT认证中间件"""
    
    # 不需要认证的路径
    EXCLUDED_PATHS = {
        "/docs",
        "/redoc", 
        "/openapi.json",
        "/api/auth/login",
        "/api/auth/register",
        "/api/auth/token",
        "/api/user/login",
        "/api/user/register",
        "/api/user/send_verification_code",
        "/api/compony/info",
        "/api/combo/info",
        "/api/news/list",
        "/api/news/detail",
        "/api/monitor",
        "/",
    }
    
    async def dispatch(self, request: Request, call_next):
        """中间件处理逻辑"""
        
        # 检查是否为排除路径
        if self._is_excluded_path(request.url.path):
            return await call_next(request)
        
        # 验证JWT token
        token = self._extract_token(request)
        if not token:
            return self._unauthorized_response("Token missing")
        
        try:
            # 验证token
            payload = jwt.decode(
                token, 
                key=settings.SECRET_KEY._value, 
                algorithms=[settings.JWT_ALGORITHM]
            )
            
            # 检查必要字段
            user_id = payload.get("sub")
            if not user_id:
                return self._unauthorized_response("Invalid token payload")
            
            # 检查过期时间
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp) < datetime.now():
                return self._unauthorized_response("Token expired")
            
            # 将用户ID添加到请求状态中，供后续使用
            request.state.user_id = user_id
            
        except JWTError:
            return self._unauthorized_response("Invalid token")
        
        return await call_next(request)
    
    def _is_excluded_path(self, path: str) -> bool:
        """检查路径是否在排除列表中"""
        # 精确匹配
        if path in self.EXCLUDED_PATHS:
            return True
        
        # 前缀匹配（用于静态文件等）
        excluded_prefixes = ["/static/", "/favicon.ico"]
        for prefix in excluded_prefixes:
            if path.startswith(prefix):
                return True
        
        return False
    
    def _extract_token(self, request: Request) -> str:
        """从请求中提取token"""
        # 优先从Authorization header获取
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # 移除 "Bearer " 前缀
        
        # 从Cookie获取
        return request.cookies.get("auth_token")
    
    def _unauthorized_response(self, detail: str) -> JSONResponse:
        """返回未授权响应"""
        return JSONResponse(
            status_code=HTTP_401_UNAUTHORIZED,
            content={
                "code": ErrorCode.USER_UNAUTHORIZED,
                "message": f"{ErrorMessage.get(ErrorCode.USER_UNAUTHORIZED)}: {detail}",
                "data": None
            },
            headers={"WWW-Authenticate": "Bearer"}
        )
