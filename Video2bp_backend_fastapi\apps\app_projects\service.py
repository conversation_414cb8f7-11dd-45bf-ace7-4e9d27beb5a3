from typing import List, Dict, Any
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from starlette.status import HTTP_400_BAD_REQUEST

from db.models import Projects
from db.database import get_async_db
from apps.app_projects.schema import Project, ProjectListResponse


class ProjectService:
    """项目服务"""

    def __init__(self):
        pass
    
    async def get_project_list(self, page_num: int = 1, page_size: int = 8, db: AsyncSession = None) -> ProjectListResponse:
        """获取项目列表"""
        try:
            if db is None:
                # 如果没有传入db会话，创建一个新的
                async for session in get_async_db():
                    db = session
                    break

            # 查询总数
            from sqlalchemy import func
            count_stmt = select(func.count(Projects.id)).where(Projects.is_active == True)
            count_result = await db.execute(count_stmt)
            total = count_result.scalar()

            # 查询分页数据
            offset = (page_num - 1) * page_size
            stmt = select(Projects).where(Projects.is_active == True).order_by(Projects.sort_order).offset(offset).limit(page_size)
            result = await db.execute(stmt)
            projects = result.scalars().all()

            project_list = []
            for project in projects:
                # 创建Project模型实例
                project_instance = Project(
                    id=project.id,
                    name=project.title,  # 数据库字段title映射到name
                    image=project.image_url,
                    fbxUrl=f"https://example.com/models/project{project.id}.fbx",  # 模拟FBX URL
                    videoUrl=f"https://example.com/videos/project{project.id}.mp4",  # 模拟视频URL
                    createdAt=project.created_at.strftime("%Y-%m-%d") if project.created_at else "",
                    updatedAt=project.updated_at.strftime("%Y-%m-%d") if project.updated_at else "",
                    status="completed"  # 模拟状态
                )
                project_list.append(project_instance)

            # 返回分页响应格式
            return ProjectListResponse(
                data=project_list,
                total=total,
                pageNum=page_num,
                pageSize=page_size
            )

        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"500_获取项目列表失败: {str(e)}"
            )


# 创建服务实例
project_service = ProjectService()
