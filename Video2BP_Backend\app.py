import os

from flask import Flask, Response
from flask_cors import *
import apis
import service
import metrics
from settings import MAIL_PORT, MAIL_SERVER, MAIL_USERNAME, MAIL_PASSWORD, MAIL_USE_TLS, VIDEO_UPLOAD_FOLDER, \
    BP_UPLOAD_FOLDER
from utils import R
from utils.R import AlchemyEncoder
from utils.error_handler import init_error_handlers
from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail

from utils.logger_utils import init_logger


class MyFlask(Flask):
    def make_response(self, rv):
        if isinstance(rv, R.R):
            return Response(str(rv))
        return super().make_response(rv)

mail = Mail()
mysql_db = SQLAlchemy()

def create_app():
    app = MyFlask(__name__)
    app.config["JSON_AS_ASCII"] = False
    app.json_encoder = AlchemyEncoder
    app.config.from_object('settings.Config')
    app.config['MAIL_SERVER'] = MAIL_SERVER
    app.config['MAIL_PORT'] = MAIL_PORT 
    app.config['MAIL_USERNAME'] = MAIL_USERNAME
    app.config['MAIL_PASSWORD'] = MAIL_PASSWORD
    app.config['MAIL_USE_TLS'] = MAIL_USE_TLS
    mail.init_app(app)
    # 初始化数据库
    mysql_db.init_app(app)
    # 再初始化其他组件
    CORS(app)
    metrics.init_app(app)
    service.init_app()
    init_error_handlers(app)
    # apis
    apis.init_app(app)
    if not os.path.exists("\\static"):
        os.mkdir("\\static")
    if not os.path.exists(VIDEO_UPLOAD_FOLDER):
        os.mkdir(VIDEO_UPLOAD_FOLDER)
    if not os.path.exists(BP_UPLOAD_FOLDER):
        os.mkdir(BP_UPLOAD_FOLDER)
    init_logger()
    return app