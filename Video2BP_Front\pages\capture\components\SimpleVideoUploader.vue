<template>
  <div class="simple-video-uploader">
    <a-upload :before-upload="beforeUpload" :custom-request="customUpload" :multiple="false" :show-upload-list="false"
      accept="video/*" :max-count="1" class="upload-wrapper">
      <div class="upload-area">
        <div class="upload-placeholder">
          <div class="upload-icon">
            <UploadOutlined />
          </div>
          <div class="upload-text">
            <div class="main-text">
              点击选择视频文件
            </div>
            <div class="sub-text">
              <div>推荐选择MP4格式，H.264编码视频</div>
              <div>其他格式和编码视频支持自动转码</div>
            </div>
            <div class="file-info">
              免费版 用户支持30秒视频，100M大小
            </div>
          </div>
        </div>
      </div>
    </a-upload>
  </div>
</template>

<script setup lang="ts">
import { UploadOutlined } from '@ant-design/icons-vue'

// 定义组件事件
const emit = defineEmits<{
  fileSelected: [file: File]
  fileError: [error: string]
}>()



// 文件上传前的验证
const beforeUpload = (file: any) => {
  // 检查文件类型
  const isVideo = file.type.startsWith('video/')
  if (!isVideo) {
    console.error('只能上传视频文件!')
    return false
  }

  // 检查文件大小 (100MB)
  const isLt100M = file.size / 1024 / 1024 < 500
  if (!isLt100M) {
    console.error('视频文件大小不能超过500MB!')
    return false
  }

  return true
}

// 自定义文件选择处理
const customUpload = async (options: any) => {
  const { file, onSuccess, onError } = options

  if (!(file instanceof File)) {
    onError?.(new Error('Invalid file object'))
    return
  }

  try {
    console.log('文件选择成功:', file.name)
    emit('fileSelected', file)
    onSuccess?.(file)
  } catch (error: unknown) {
    const errorMessage = '文件选择失败'
    console.error(`文件选择失败: ${errorMessage}`)
    emit('fileError', errorMessage)
    onError?.(new Error(errorMessage))
  }
}
</script>

<style lang="scss" scoped>
.simple-video-uploader {
  user-select: none;
  width: 100%;
  height: 100%;

  .upload-wrapper {
    width: 100%;
    height: 100%;

    :deep(.ant-upload) {
      width: 100%;
      height: 100%;
    }
  }

  .upload-area {
    width: 100%;
    height: 100%;
    background: rgba(42, 40, 68, 0.8);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: rem(12);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: rgba(0, 145, 255, 0.6);
      background: rgba(42, 40, 68, 0.9);
    }

    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: rem(60) rem(40);
      text-align: center;

      .upload-icon {
        margin-bottom: rem(15);

        span {
          font-size: rem(80);
          color: rgba(255, 255, 255, 1);
        }
      }

      .upload-text {
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: bold;
        font-size: rem(24);
        color: #FFFFFF;
        line-height: rem(38);
        text-align: center;
        font-style: normal;

        .main-text {
          margin-bottom: rem(16);
        }

        .sub-text,
        .file-info {
          font-family: SourceHanSerifSC, SourceHanSerifSC;
          font-weight: 400;
          font-size: rem(18);
          color: rgba(255, 255, 255, 0.5);
          line-height: rem(26);
          margin-bottom: rem(30);
        }

        .file-info {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
