from fastapi import APIRouter, Body, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_400_BAD_REQUEST

from apps.app_business.schema import (
    BusinessListResponseModel,
    ProductListResponseModel,
    UserCaseListResponseModel,
    ContactRequest,
    ContactResponseModel,
)
from apps.app_business.service import business_service
from core.e import ErrorCode
from db.database import get_async_db


router = APIRouter()


@router.post(
    "/list",
    name="获取业务轮播图列表",
    response_model=BusinessListResponseModel,
)
async def get_business_list(
    request: dict = Body({}),
    db: AsyncSession = Depends(get_async_db),
):
    """获取所有业务数据，无需参数"""
    try:
        business_data = await business_service.get_business_list(db)
        return BusinessListResponseModel(
            code=0,
            message="success",
            data=business_data
        )
    except HTTPException as e:
        return BusinessListResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return BusinessListResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/products",
    name="获取产品列表",
    response_model=ProductListResponseModel,
)
async def get_product_list(
    request: dict = Body({}),
    db: AsyncSession = Depends(get_async_db),
):
    """获取所有产品数据，无需参数"""
    try:
        product_data = await business_service.get_product_list(db)
        return ProductListResponseModel(
            code=0,
            message="success",
            data=product_data
        )
    except HTTPException as e:
        return ProductListResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return ProductListResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_400_BAD_REQUEST)



@router.post(
    "/user-cases",
    name="获取用户案例列表",
    response_model=UserCaseListResponseModel,
)
async def get_user_cases(
    request: dict = Body({}),
    db: AsyncSession = Depends(get_async_db),
):
    """获取所有用户案例数据，无需参数"""
    try:
        case_data = await business_service.get_user_cases(db)
        return UserCaseListResponseModel(
            code=0,
            message="success",
            data=case_data
        )
    except HTTPException as e:
        return UserCaseListResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return UserCaseListResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/contact",
    name="提交联系表单",
    response_model=ContactResponseModel,
)
async def submit_contact(
    request: ContactRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
):
    """提交用户联系信息和咨询项目"""
    try:
        await business_service.submit_contact(
            request.phone,
            request.companyName,
            request.project,
            db
        )
        return ContactResponseModel(
            code=0,
            message="联系表单提交成功",
            data={}
        )
    except HTTPException as e:
        return ContactResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return ContactResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_400_BAD_REQUEST)
