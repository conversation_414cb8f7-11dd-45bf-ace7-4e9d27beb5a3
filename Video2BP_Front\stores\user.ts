import { defineStore } from 'pinia';
import type { UserInfo } from '~/types/user';
import { useApi } from '~/composables/useApi';
// 用户状态类型
export interface UserState {
	userInfo: UserInfo | null;
	permissions: string[];
}

export const useUserStore = defineStore('user', () => {
	// 状态
	const userInfo = ref<UserInfo | null>(null);
	const permissions = ref<string[]>([]); // 用户权限

	const { user } = useApi();

	const userRole = computed(() => userInfo.value?.role);
	const hasPermission = (permissionId: string) =>
		permissions.value.includes(permissionId);

	function setUserInfo(info: UserInfo) {
		userInfo.value = info;
	}

	function setPermissions(userPermissions: string[]) {
		permissions.value = userPermissions;
	}

	function clearUserInfo() {
		userInfo.value = null;
		permissions.value = [];
	}

	// 获取用户信息
	async function fetchUserInfo() {
		try {
			const result = await user?.getUserInfo();
			if (result) {
				setUserInfo(result);
			}
			// 模拟权限
			setPermissions(['read:profile', 'update:profile']);

			return true;
		} catch (error) {
			console.error('获取用户信息失败', error);
			return null;
		}
	}

	// 监听认证状态变化
	function setupAuthListener() {
		const authStore = useAuthStore();

		watch(
			() => authStore.isAuthenticated,
			(isAuthenticated: boolean) => {
				if (!isAuthenticated) {
					clearUserInfo();
				}
			}
		);
	}

	onMounted(() => {
		setupAuthListener();

		// 如果有token则获取用户信息
		const authStore = useAuthStore();
		if (authStore.isAuthenticated && !userInfo.value) {
			fetchUserInfo();
		}
	});

	return {
		userInfo,
		permissions,
		userRole,
		hasPermission,
		setUserInfo,
		setPermissions,
		clearUserInfo,
		fetchUserInfo,
	};
});
