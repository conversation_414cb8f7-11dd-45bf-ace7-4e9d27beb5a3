import os

from loguru import logger
from settings import LOGGING, GUNICORN_ACCESS_LOG_FORMAT, GUNICORN_ERROR_LOG_FORMAT, LOG_PATH, VIDEO_UPLOAD_FOLDER, \
    BP_UPLOAD_FOLDER


def init_logger():
    if not os.path.exists(LOG_PATH):
        os.mkdir(LOG_PATH)
        fp = open(LOG_PATH + "/" + ".gitignore", "w+")
        fp.write("*.log\n*.lock")
        fp.close()
    # 如果不是直接运行，则将日志输出到 gunicorn 中
    logger.add(LOGGING["handlers"]["access_file"]["filename"],
               # filter=lambda record: record["extra"]["name"] == "gunicorn_access",
               format=GUNICORN_ACCESS_LOG_FORMAT,
               level=LOGGING["loggers"]["gunicorn.access"]["level"],
               rotation=LOGGING["handlers"]["access_file"]["rotation"],
               retention=LOGGING["handlers"]["access_file"]["retention"],
               backtrace=LOGGING["handlers"]["access_file"]["backtrace"],  # 链路追踪
               encoding=LOGGING["handlers"]["access_file"]["encoding"],
               diagnose=LOGGING["handlers"]["access_file"]["diagnose"],
               # 异常跟踪是否应显示变量值以简化调试。这应该False在生产中设置，以避免泄露敏感数据
               enqueue=LOGGING["handlers"]["access_file"]["enqueue"])  # 异步写入


    logger.add(LOGGING["handlers"]["error_file"]["filename"],
               filter=lambda x: "ERROR" in str(x["level"]).upper(),
               format=GUNICORN_ERROR_LOG_FORMAT,
               level=LOGGING["loggers"]["gunicorn.error"]["level"],
               rotation=LOGGING["handlers"]["error_file"]["rotation"],
               retention=LOGGING["handlers"]["error_file"]["retention"],
               backtrace=LOGGING["handlers"]["error_file"]["backtrace"],  # 链路追踪
               encoding=LOGGING["handlers"]["error_file"]["encoding"],
               diagnose=LOGGING["handlers"]["error_file"]["diagnose"],
               # 异常跟踪是否应显示变量值以简化调试。这应该False在生产中设置，以避免泄露敏感数据
               enqueue=LOGGING["handlers"]["error_file"]["enqueue"])  # 异步写入

