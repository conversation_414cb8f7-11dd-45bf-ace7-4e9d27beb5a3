from fastapi import APIRouter, Body, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_400_BAD_REQUEST

from apps.app_projects.schema import ProjectListResponseModel, ProjectListRequest
from apps.app_projects.service import project_service
from core.e import ErrorCode
from db.database import get_async_db


router = APIRouter()


@router.post(
    "/list",
    name="获取项目列表",
    response_model=ProjectListResponseModel,
)
async def get_project_list(
    request: ProjectListRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
):
    """获取项目列表，支持分页"""
    try:
        project_data = await project_service.get_project_list(
            page_num=request.pageNum,
            page_size=request.pageSize,
            db=db
        )
        return ProjectListResponseModel(
            code=0,
            message="success",
            data=project_data
        )
    except HTTPException as e:
        return ProjectListResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return ProjectListResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_400_BAD_REQUEST)
