from typing import List, Dict, Any
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from starlette.status import HTTP_400_BAD_REQUEST

from db.models import Resources
from db.database import get_async_db


class ResourceService:
    """资源服务"""

    def __init__(self):
        pass
    
    async def get_resource_list(self, db: AsyncSession = None) -> List[Dict[str, Any]]:
        """获取资源列表"""
        try:
            if db is None:
                # 如果没有传入db会话，创建一个新的
                async for session in get_async_db():
                    db = session
                    break

            # 从数据库查询资源
            stmt = select(Resources).where(Resources.is_active == True).order_by(Resources.sort_order)
            result = await db.execute(stmt)
            resources = result.scalars().all()

            resource_list = []
            for resource in resources:
                resource_list.append({
                    "id": resource.id,
                    "title": resource.title,
                    "description": resource.description,
                    "type": resource.resource_type,
                    "url": resource.file_url,
                    "thumbnail": resource.thumbnail_url,
                    "size": resource.file_size,
                    "format": resource.file_format,
                    "createdAt": resource.created_at.isoformat() if resource.created_at else None,
                    "downloadCount": resource.download_count
                })

            return resource_list

        except Exception as e:
            raise HTTPException(
                status_code=HTTP_400_BAD_REQUEST,
                detail=f"500_获取资源列表失败: {str(e)}"
            )


# 创建服务实例
resource_service = ResourceService()
