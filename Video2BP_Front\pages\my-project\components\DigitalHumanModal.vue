<template>
  <a-modal v-model:open="visible" :width="1400" :centered="true" :closable="false" :footer="null" :mask-closable="true"
    wrap-class-name="digital-human-modal" @cancel="handleClose">
    <div class="modal-content" ref="modalContentRef">
      <div class="id-display">
        ID: {{ projectData?.id || 'N/A' }}
      </div>

      <!-- 关闭按钮 -->
      <div class="close-btn" @click="handleClose">
        <CloseOutlined />
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 3D数字人显示区域 -->
        <div class="digital-human-container">
          <ClientOnly>
            <vue3dLoader ref="modelLoader" :position="position" :filePath="fbxFilePath" :scale="scale"
              :cameraPosition="cameraPosition" :autoPlay="isPlaying" @load="onModelLoaded" />
          </ClientOnly>
        </div>

        <!-- 右上角源视频区域 -->
        <div class="source-video-container" ref="sourceVideoContainerRef" :style="videoContainerStyle"
          @mousedown="handleVideoMouseDown">
          <video ref="videoRef" :src="sourceVideoUrl" :width="videoWidth" :height="videoHeight" class="source-video"
            @loadedmetadata="onVideoLoaded" />
        </div>

      </div>

      <!-- 底部控制区域 -->
      <div class="control-panel">
        <div class="play-button" @click="togglePlayPause">
          <img v-if="!isPlaying" src="~/assets/images/play-button.webp" alt="">
          <img v-else src="~/assets/images/pause-button.webp" alt="">
        </div>

        <!-- 左下角帧数显示 -->
        <div class="frame-display">
          {{ currentFrame }}
        </div>

        <div class="play-controls">
          <div class="progress-bar">
            <div ref="progressBarRef" class="progress-track" @click="handleProgressClick">
              <div class="progress-fill" :style="{ width: progressPercent + '%' }"></div>
              <div class="progress-thumb" :style="{ left: progressPercent + '%' }" @mousedown="handleThumbMouseDown">
              </div>
            </div>
          </div>
        </div>

        <!-- 右下角按钮组 -->
        <div class="action-buttons">
          <a-button type="primary" class="export-btn" @click="handleExport">
            2d修复检测
          </a-button>
          <a-button type="primary" class="download-btn" @click="handleDownload">
            下载文件
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>


<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick, type StyleValue } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import {
  Color,
  TextureLoader,
  SphereGeometry,
  MeshBasicMaterial,
  Mesh,
  BackSide,
  Material, // 用于 instanceof 类型检查
  Texture   // 用于类型注解R
} from 'three';

import autumn_field_puresky from '~/assets/images/autumn_field_puresky.webp'


defineOptions({
  name: 'DigitalHumanModal',
})

// Props定义
interface ProjectData {
  id: number
  name: string
  image: string
  fbxUrl?: string
  videoUrl?: string;
}

const props = defineProps<{
  visible: boolean
  projectData: ProjectData | null
}>()

// Emits定义
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'repair': [data: ProjectData]
  'download': [data: ProjectData]
}>()

// 响应式数据
const modelLoader = ref<any>(null)
const videoRef = ref<HTMLVideoElement | null>(null) // 视频实例
const progressBarRef = ref<HTMLDivElement | null>(null) // 进度条引用
const currentFrame = ref(0) // 当前帧数
const totalFrames = ref(0) // 总帧数，从FBX动画获取
const isPlaying = ref(false)
const animationId = ref<number | null>(null)
const isDragging = ref(false) // 拖拽状态

// 加载状态管理
const isModelLoaded = ref(false) // 3D模型加载状态
const isVideoLoaded = ref(false) // 视频加载状态

// 动画配置
const animationFPS = ref(30) // 动画帧率，默认30fps
const animationDuration = ref(0) // 动画持续时间（秒）


const sourceVideoContainerRef = ref<HTMLDivElement | null>(null);
const isDraggingVideo = ref(false);
const dragStartOffset = ref({ x: 0, y: 0 });

const videoContainerStyle = ref({
  top: '20px',
  left: 'auto',
  right: '20px',
  cursor: 'grab',
});

const handleVideoMouseDown = (event: MouseEvent) => {
  if (!sourceVideoContainerRef.value) return;
  event.preventDefault();

  isDraggingVideo.value = true;
  videoContainerStyle.value.cursor = 'grabbing';

  const el = sourceVideoContainerRef.value;
  const rect = el.getBoundingClientRect();

  el.style.position = 'fixed';
  videoContainerStyle.value.left = `${rect.left}px`;
  videoContainerStyle.value.top = `${rect.top}px`;
  videoContainerStyle.value.right = 'auto';

  dragStartOffset.value.x = event.clientX - rect.left;
  dragStartOffset.value.y = event.clientY - rect.top;

  document.addEventListener('mousemove', handleVideoMouseMove);
  document.addEventListener('mouseup', handleVideoMouseUp);
};

const handleVideoMouseMove = (event: MouseEvent) => {
  if (!isDraggingVideo.value || !sourceVideoContainerRef.value) return;

  const el = sourceVideoContainerRef.value;
  let newX = event.clientX - dragStartOffset.value.x;
  let newY = event.clientY - dragStartOffset.value.y;

  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const elWidth = el.offsetWidth;
  const elHeight = el.offsetHeight;


  newX = Math.max(0, Math.min(newX, viewportWidth - elWidth));
  newY = Math.max(0, Math.min(newY, viewportHeight - elHeight));

  videoContainerStyle.value.left = `${newX}px`;
  videoContainerStyle.value.top = `${newY}px`;
};

const handleVideoMouseUp = () => {
  if (!isDraggingVideo.value) return;
  isDraggingVideo.value = false;
  videoContainerStyle.value.cursor = 'grab';
  document.removeEventListener('mousemove', handleVideoMouseMove);
  document.removeEventListener('mouseup', handleVideoMouseUp);
};


// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const progressPercent = computed(() => {
  if (totalFrames.value === 0) return 0
  return (currentFrame.value / (totalFrames.value - 1)) * 100
})

// 判断是否全部加载完成
const isAllLoaded = computed(() => {
  return isModelLoaded.value && isVideoLoaded.value
})

// 3D模型配置
const position = ref([{ x: 0, y: 0, z: 0 }])
const scale = ref([{ x: 0.5, y: 0.5, z: 0.5 }])
const cameraPosition = ref({ x: 0, y: 0, z: 0 })

// 背景配置 - 使用图片背景
const backgroundImage = ref(autumn_field_puresky) // 背景图片路径
const backgroundType = ref<'skybox'>('skybox') // 背景类型

// 视频配置
const videoWidth = ref(300)
const videoHeight = ref(200)

const fbxFilePath = computed(() => {
  return props.projectData?.fbxUrl ?? '/models/RumbaDancing.fbx'
})

const sourceVideoUrl = computed(() => {
  return props.projectData?.videoUrl ?? ''
})



const handleClose = () => {
  visible.value = false
  stopAnimation()
}

const onModelLoaded = () => {
  console.log('3D模型加载完成')
  isModelLoaded.value = true

  if (videoRef.value && videoRef.value.duration > 0) {
    animationDuration.value = videoRef.value.duration
    totalFrames.value = Math.floor(animationDuration.value * animationFPS.value)
    console.log('基于视频设置动画参数 - 时长:', animationDuration.value, '帧数:', totalFrames.value)
  } else {
    animationDuration.value = 10

    totalFrames.value = animationDuration.value * animationFPS.value
    console.log('使用默认动画参数 - 时长:', animationDuration.value, '帧数:', totalFrames.value)
  }

  nextTick(() => {
    setTimeout(() => {
      console.log('开始设置背景，当前背景类型:', backgroundType.value)
      setSceneBackground()
    }, 200)
  })
}

const setSceneBackground = () => {
  console.log('开始设置场景背景...')
  console.log('backgroundType:', backgroundType.value)

  if (!modelLoader.value || !modelLoader.value.scene) {
    console.warn('modelLoader 或 scene 未初始化')
    return
  }

  clearAllBackgrounds()

  if (!backgroundImage.value) {
    console.log('未设置背景图片，使用黑色背景')
    modelLoader.value.scene.background = new Color(0x000000)
    return
  }

  console.log('开始加载背景图片:', backgroundImage.value)
  const textureLoader = new TextureLoader()
  textureLoader.setCrossOrigin('anonymous')

  textureLoader.load(
    backgroundImage.value,
    (texture) => {
      console.log('背景图片加载成功，设置背景类型:', backgroundType.value)

      if (!modelLoader.value || !modelLoader.value.scene) return

      try {
        createSimpleSkybox(texture)
        console.log('3D场景背景设置成功')
      } catch (error) {
        console.error('设置天空盒背景失败，回退到平面背景:', error)
        modelLoader.value.scene.background = texture
      }
    },
    undefined,
    (error) => {
      console.error('背景图片加载失败:', error)
      if (modelLoader.value && modelLoader.value.scene) {
        modelLoader.value.scene.background = new Color(0x000000)
      }
    }
  )
}

const createSimpleSkybox = (texture: Texture) => {
  if (!modelLoader.value || !modelLoader.value.scene) return

  try {
    console.log('创建天空盒...')

    const geometry = new SphereGeometry(1000, 32, 32)
    const material = new MeshBasicMaterial({
      map: texture,
      side: BackSide,
      depthWrite: false,
      depthTest: false
    })

    const skybox = new Mesh(geometry, material)
    skybox.name = 'skybox'
    skybox.position.set(0, 0, 0)
    skybox.scale.set(-1, 1, 1)
    skybox.renderOrder = -1

    modelLoader.value.scene.add(skybox)

    if (modelLoader.value.renderer) {
      modelLoader.value.renderer.render(modelLoader.value.scene, modelLoader.value.camera)
    }

  } catch (error) {
    console.error('创建天空盒失败:', error)
    if (modelLoader.value && modelLoader.value.scene) {
      modelLoader.value.scene.background = texture
    }
  }
}

const onVideoLoaded = () => {
  console.log('视频加载完成')
  isVideoLoaded.value = true

  if (videoRef.value) {
    const duration = videoRef.value.duration
    console.log('视频时长:', duration, '秒')
    totalFrames.value = Math.floor(duration * animationFPS.value)
    animationDuration.value = duration
    console.log('基于视频计算的总帧数:', totalFrames.value)
    console.log('动画持续时间:', animationDuration.value, '秒')
  }
}

const goToFrame = (frame: number) => {
  if (frame >= 0 && frame < totalFrames.value) {
    currentFrame.value = frame

    if (videoRef.value && animationDuration.value > 0) {
      const timeInSeconds = frame / animationFPS.value
      if (timeInSeconds <= videoRef.value.duration) {
        videoRef.value.currentTime = timeInSeconds
      }
    }
    console.log(`跳转到帧 ${frame}，进度: ${((frame / totalFrames.value) * 100).toFixed(1)}%`)
  }
}

const handleProgressClick = (event: MouseEvent) => {
  if (isDragging.value) return

  const target = event.currentTarget as HTMLElement
  const rect = target.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percent = Math.max(0, Math.min(1, clickX / rect.width))
  const targetFrame = Math.floor(percent * (totalFrames.value - 1))

  console.log(`点击进度条: ${percent.toFixed(2)} -> 帧 ${targetFrame}/${totalFrames.value}`)
  goToFrame(targetFrame)
  console.log(`跳转完成，当前进度: ${progressPercent.value.toFixed(1)}%`)
}

const handleThumbMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragging.value = true

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value || !progressBarRef.value) return

    const rect = progressBarRef.value.getBoundingClientRect()
    const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width))
    const targetFrame = Math.floor(percent * (totalFrames.value - 1))
    goToFrame(targetFrame)
  }

  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const startAnimationLoop = () => {
  if (animationId.value) return

  const frameInterval = 1000 / animationFPS.value
  let lastFrameTime = 0

  const animate = (currentTime: number) => {
    if (!isPlaying.value) return

    if (currentTime - lastFrameTime >= frameInterval) {
      currentFrame.value++
      if (currentFrame.value >= totalFrames.value) {
        currentFrame.value = 0
      }

      if (videoRef.value && animationDuration.value > 0) {
        const timeInSeconds = currentFrame.value / animationFPS.value
        if (timeInSeconds <= videoRef.value.duration) {
          videoRef.value.currentTime = timeInSeconds
        }
      }
      lastFrameTime = currentTime
      // console.log(`当前帧: ${currentFrame.value}/${totalFrames.value}, 进度: ${progressPercent.value.toFixed(1)}%`)
    }
    animationId.value = requestAnimationFrame(animate)
  }

  lastFrameTime = performance.now()
  animationId.value = requestAnimationFrame(animate)
}

const stopAnimationLoop = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }
}

const startAnimation = () => {
  if (isPlaying.value) return
  console.log('开始播放动画，总帧数:', totalFrames.value)
  isPlaying.value = true
  startAnimationLoop()
  if (videoRef.value) {
    videoRef.value.play().catch(error => {
      console.warn('视频播放失败:', error)
    })
  }
}

const stopAnimation = () => {
  console.log('停止播放动画')
  isPlaying.value = false
  stopAnimationLoop()
  if (videoRef.value) {
    videoRef.value.pause()
  }
}

const togglePlayPause = () => {
  console.log('切换播放状态，当前状态:', isPlaying.value ? '播放中' : '已暂停')
  if (isPlaying.value) {
    console.log('执行暂停操作')
    stopAnimation()
  } else {
    console.log('执行播放操作')
    startAnimation()
  }
}

const handleExport = () => {
  if (props.projectData) {
    emit('repair', props.projectData)
  }
}

const handleDownload = () => {
  if (props.projectData) {
    emit('download', props.projectData)
  }
}

const clearAllBackgrounds = () => {
  if (!modelLoader.value || !modelLoader.value.scene) return

  const existingSkybox = modelLoader.value.scene.getObjectByName('skybox')
  if (existingSkybox) {
    modelLoader.value.scene.remove(existingSkybox)
    if (existingSkybox instanceof Mesh) {
      existingSkybox.geometry.dispose()
      if (existingSkybox.material instanceof Material) {
        existingSkybox.material.dispose()
      }
    }
  }
  modelLoader.value.scene.background = null
}

defineExpose({
  setSceneBackground
})

watch(currentFrame, (newFrame) => {
  if (videoRef.value && animationDuration.value > 0 && !isDragging.value) {
    const timeInSeconds = newFrame / animationFPS.value
    if (timeInSeconds <= videoRef.value.duration) {
      videoRef.value.currentTime = timeInSeconds
    }
  }
})

watch(visible, (newVal) => {
  if (newVal) {
    isModelLoaded.value = false
    isVideoLoaded.value = false
    currentFrame.value = 0

    // 重置视频容器位置到初始CSS值和定位方式
    if (sourceVideoContainerRef.value) {
      sourceVideoContainerRef.value.style.position = 'absolute';
    }
    videoContainerStyle.value.top = '20px';
    videoContainerStyle.value.left = 'auto';
    videoContainerStyle.value.right = '20px';
    videoContainerStyle.value.cursor = 'grab';

    setTimeout(() => {
      if (modelLoader.value && modelLoader.value.scene) {
        setSceneBackground()
      }
    }, 500)

    setTimeout(() => {
      console.log('自动开始播放')
      startAnimation()
    }, 1000)
  } else {
    stopAnimation()
  }
})

onMounted(() => {
})

onBeforeUnmount(() => {
  stopAnimation()
  // 如果组件在拖拽过程中被卸载，清理视频拖拽监听器
  if (isDraggingVideo.value) {
    document.removeEventListener('mousemove', handleVideoMouseMove);
    document.removeEventListener('mouseup', handleVideoMouseUp);
    isDraggingVideo.value = false;
  }
})
</script>

<style lang="scss" scoped>
:deep(.digital-human-modal) {
  .ant-modal {
    max-width: none;

    .ant-modal-content {
      border-radius: rem(12);
      padding: 0;
      height: rem(800);
      background: #000000 !important;
      overflow: hidden;
    }

    .ant-modal-body {
      padding: 0;
      height: rem(800);
    }
  }
}

.viewer-container {
  width: 100% !important;
  height: 100% !important;
}

.viewer-canvas {
  width: 100% !important;
  height: 100% !important;
}

.modal-content {
  width: 100%;
  height: rem(800);
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: rem(400);
  }

  .id-display {
    position: absolute;
    top: rem(20);
    left: rem(20);
    z-index: 10;
    padding: rem(8) rem(16);
    background: rgba(0, 0, 0, 0.7);
    border-radius: rem(4);
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: rem(14);
    font-weight: bold;
  }

  .close-btn {
    position: absolute;
    top: rem(20);
    right: rem(20);
    z-index: 10; // Ensure close button is above video if video is dragged to top right
    width: rem(40);
    height: rem(40);
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    color: #ffffff;
    font-size: rem(18);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 0, 0, 0.7);
      transform: scale(1.1);
    }
  }

  .main-content {
    width: 100%;
    height: rem(680); // This is the height of the main content area, video drag is within viewport
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative; // Added for potential absolute positioning of children like video container

    .digital-human-container {
      width: 100%;
      height: 100%;
    }

    .source-video-container {
      position: absolute; // Changed to absolute for modal-relative positioning
      // top, left, right are now controlled by videoContainerStyle ref
      z-index: 5; // Ensure it's above 3D model but below close button/ID if they overlap
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: rem(8);
      overflow: hidden;
      background: rgba(0, 0, 0, 0.5);
      width: rem(300); // Keep fixed width
      height: rem(168); // Keep fixed height (or adjust as needed, e.g. based on video aspect ratio)
      // cursor: grab; // Moved to videoContainerStyle ref

      .source-video {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
        pointer-events: none; // Prevent video element from capturing mouse events meant for dragging the container
      }
    }
  }

  .control-panel {
    width: 100%;
    height: rem(120);
    background: rgba(0, 0, 0, 0.9);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: rem(20) rem(40);

    .frame-display {
      width: rem(40);
      color: #ffffff;
      font-family: 'Courier New', monospace;
      font-size: rem(24);
      font-weight: bold;
      text-shadow: rem(2) rem(2) rem(4) rgba(0, 0, 0, 0.8);
    }

    .play-button {
      display: flex;
      align-items: center;
      height: rem(80);
      margin-right: rem(10);
      padding-bottom: rem(3);
      cursor: pointer; // Added for better UX

      img {
        width: rem(28);
        height: rem(28);
      }
    }

    .play-controls {
      flex: 1;
      margin: 0 rem(40) 0 0;
      display: flex;
      align-items: center;
      gap: rem(16);

      .progress-bar {
        flex: 1;

        .progress-track {
          width: 100%;
          height: rem(12);
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: rem(10);
          position: relative;
          cursor: pointer;

          .progress-fill {
            height: 100%;
            background-color: #ffffff;
            border-radius: rem(10);
            transition: width 0.1s ease;
          }

          .progress-thumb {
            position: absolute;
            top: 50%;
            width: rem(24);
            height: rem(24);
            background-color: #ffffff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            cursor: grab;
            box-shadow: 0 0 rem(16) 0 rgba(255, 255, 255, 0.7);
            transition: left 0.1s ease; // Smoother thumb transition

            &:hover {
              transform: translate(-50%, -50%) scale(1.1);
            }

            &:active {
              cursor: grabbing;
              transform: translate(-50%, -50%) scale(1.2);
            }
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      align-items: center;
      gap: rem(16);

      // .background-controls class was present in original but not used, keeping commented
      /*
      .background-controls {
        display: flex;
        gap: rem(8);
        margin-right: rem(16);

        .ant-btn {
          height: rem(32);
          padding: 0 rem(12);
          font-size: rem(12);

          &[data-type="default"] {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: rgba(255, 255, 255, 0.4);
            }
          }
        }
      }
      */

      .export-btn,
      .download-btn {
        height: rem(40);
        padding: 0 rem(24);
        border: none;
        border-radius: rem(6);
        font-size: rem(14);
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &.export-btn {
          background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
          color: #ffffff;

          &:hover {
            transform: translateY(rem(-2));
            box-shadow: 0 rem(4) rem(12) rgba(76, 175, 80, 0.4);
          }
        }

        &.download-btn {
          background: linear-gradient(135deg, #0091FF 0%, #B620E0 100%);
          color: #ffffff;

          &:hover {
            transform: translateY(rem(-2));
            box-shadow: 0 rem(4) rem(12) rgba(0, 145, 255, 0.4);
          }
        }
      }
    }
  }
}
</style>
