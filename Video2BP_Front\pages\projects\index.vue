<template>
  <div class="projects-page">

    <div class="projects-content">
      <!-- 上半部分：视频播放器和关键姿势 -->
      <div class="upper-section">
        <!-- 左侧：视频播放器 -->
        <div class="video-player-section">
          <RepairBone ref="repairBoneRef" :mode="currentMode" />
        </div>

        <!-- 右侧：关键姿势显示 -->
        <div class="key-pose-section">
          <div class="key-pose-header">
            <h3 v-if="currentMode === 'kp'">Key Pose</h3>
            <h3 v-else>脚部 IK/FK</h3>
          </div>
          <div class="key-pose-display" v-if="currentMode === 'kp'">
            <div class="pose-figure">
              <!-- 人体姿势图标 -->
              <div class="ef-head" @click="toggleRedClass($event, 'head')"></div>

              <div class="ef-body">
                <!-- 左胳膊节点（UI左侧对应图像左侧，即人体右臂） -->
                <div class="ef-top-left" @click="toggleRedClass($event, 'rightArm')">
                  <div class="ef-top-left-inner" @click="toggleRedClass($event, 'rightArm')"></div>
                </div>
                <!-- 全身节点 -->
                <div class="ef-top-center"
                  @click="toggleRedClass($event, ['head', 'spine', 'leftArm', 'rightArm', 'leftLeg', 'rightLeg', 'leftFoot', 'rightFoot'])">
                </div>
                <!-- 右胳膊节点（UI右侧对应图像右侧，即人体左臂） -->
                <div class="ef-top-right" @click="toggleRedClass($event, 'leftArm')">
                  <div class="ef-top-right-inner" @click="toggleRedClass($event, 'leftArm')"></div>
                </div>
                <div class="ef-bottom-left" @click="toggleRedClass($event, ['rightLeg', 'rightFoot'])">
                  <!-- 左腿节点（UI左侧对应图像左侧，即人体右腿） -->
                  <div class="ml-top-inner" @click="toggleRedClass($event, 'rightLeg')"></div>
                  <!-- 左脚节点（UI左侧对应图像左侧，即人体右脚） -->
                  <div class="ml-bottom-inner" @click="toggleRedClass($event, 'rightFoot')"></div>
                </div>
                <div class="ef-bottom-right" @click="toggleRedClass($event, ['leftLeg', 'leftFoot'])">
                  <!-- 右腿节点（UI右侧对应图像右侧，即人体左腿） -->
                  <div class="mr-top-inner" @click="toggleRedClass($event, 'leftLeg')"></div>
                  <!-- 右脚节点（UI右侧对应图像右侧，即人体左脚） -->
                  <div class="mr-bottom-inner" @click="toggleRedClass($event, 'leftFoot')"></div>
                </div>
              </div>
            </div>

          </div>

          <div v-else class="key-pose-ik">
            <div class="pi-left-foot">
              <img src="~/assets/images/left-foot.webp" alt="">
              <div class="pi-left-foot-inner"
                :class="{ 'red': getFootPointColor('right', 0) === 'red', 'green': getFootPointColor('right', 0) === 'green' }"
                @click="handleFootPointClick('right', 0)"></div>
              <div class="pi-left-foot-inner"
                :class="{ 'red': getFootPointColor('right', 1) === 'red', 'green': getFootPointColor('right', 1) === 'green' }"
                @click="handleFootPointClick('right', 1)"></div>
            </div>
            <div class="pi-right-foot">
              <img src="~/assets/images/right-foot.webp" alt="">
              <div class="pi-right-foot-inner"
                :class="{ 'red': getFootPointColor('left', 0) === 'red', 'green': getFootPointColor('left', 0) === 'green' }"
                @click="handleFootPointClick('left', 0)"></div>
              <div class="pi-right-foot-inner"
                :class="{ 'red': getFootPointColor('left', 1) === 'red', 'green': getFootPointColor('left', 1) === 'green' }"
                @click="handleFootPointClick('left', 1)"></div>
            </div>

          </div>

        </div>
      </div>

      <!-- 下半部分：视频编辑控制 -->
      <div class="lower-section">
        <div class="video-editing-header">
          <div class="video-edit-tips">视频编辑</div>
          <div class="editing-actions">
            <div :class="['action-btn secondary', { 'disabled': !hasCurrentFrameData }]"
              @click="handleReset('current')"><i class="iconfont icon-reset_defalut"></i> 重置当前帧
            </div>
            <div class="action-btn secondary" @click="handleReset('all')"><i class="iconfont icon-reset"></i> 重置所有帧
            </div>
            <div class="action-btn primary" @click="handleSave">
              <SaveOutlined />保存
            </div>
          </div>
        </div>

        <!-- 编辑控制区域 -->
        <div class="editing-controls">
          <!-- 编辑模式选择 -->
          <div class="editing-modes">
            <div v-for="mode in editingModes" :key="mode.id" :class="['mode-btn', { active: currentMode === mode.id }]"
              @click="handleModeChange(mode.id)">
              {{ mode.name }}
            </div>
          </div>


          <!-- 左侧控制 -->
          <div class="left-controls" v-if="currentMode === 'kp'">
            <div class="tc-top">
              <div :class="['control-div', { 'disabled': !hasCurrentFrameData || canCopy }]" @click="handleCopy">复制
              </div>
              <div :class="['control-div', { 'disabled': !hasCurrentFrameData || !hasCopiedData }]"
                @click="handlePaste">粘贴</div>
            </div>
            <div :class="['tc-bottom', { 'disabled': !hasCurrentFrameData || !canSwapLeftRight }]">
              <div :class="['control-div lr-exchange']" @click="handleSwapLeftRight">
                左右互换</div>
            </div>
          </div>

          <!-- 右侧状态 -->
          <div class="right-status" v-if="currentMode === 'kp'">
            <div class="ts-top">
              <div class="status-item">
                <span class="status-label">开始帧</span>
                <span class="status-value">
                  <input type="number" v-model.number="displayStartFrame" :min="1" :max="totalFrames" placeholder="开始帧">
                </span>
              </div>
              <div class="status-item">
                <span class="status-label">结束帧</span>
                <span class="status-value">
                  <input type="number" v-model.number="displayEndFrame" :min="1" :max="totalFrames" placeholder="结束帧">
                </span>
              </div>
            </div>
            <div class="ts-center" @click="handleInterpolation">
              <div class="timeline-controls">
                <div class="control-div active">插值</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SaveOutlined } from '@ant-design/icons-vue'
import RepairBone from '~/pages/repair-bone/index.vue'
import { ref, onMounted, computed } from 'vue'

defineOptions({
  name: 'ProjectsView',
})

// 使用 Nuxt 3 的状态管理
const layoutConfig = useState('layoutConfig', () => ({}))

layoutConfig.value = {
  footerVisible: false
}

const checked = ref(false)
const repairBoneRef = ref<InstanceType<typeof RepairBone> | null>(null) // 添加对RepairBone组件的引用

// 编辑模式相关数据
const editingModes = ref([
  { id: 'kp', name: 'KP模式' },
  { id: 'ik', name: 'IK模式' }
])
const currentMode = ref('kp') // 默认选中KP模式

// 插值功能相关变量
const startFrame = ref(0); // 开始帧索引（从0开始）
const endFrame = ref(0); // 结束帧索引（从0开始）

// 获取总帧数
const totalFrames = computed(() => {
  return repairBoneRef.value?.totalFrames ?? 0
})

// 插值显示用的帧数（从1开始，与页面显示保持一致）
const displayStartFrame = computed({
  get: () => startFrame.value + 1,
  set: (value) => {
    const frameIndex = Math.max(1, Math.min(totalFrames.value, Number(value))) - 1
    startFrame.value = frameIndex
  },
})

const displayEndFrame = computed({
  get: () => endFrame.value + 1,
  set: (value) => {
    const frameIndex = Math.max(1, Math.min(totalFrames.value, Number(value))) - 1
    endFrame.value = frameIndex
  },
})

// 添加保存处理函数
const handleSave = () => {
  console.log('点击保存按钮');
  console.log('RepairBone引用:', repairBoneRef.value);

  if (repairBoneRef.value) {
    try {
      const updatedData = repairBoneRef.value.saveSkeletonData()
      console.log('保存项目数据成功:', updatedData)

    } catch (error) {
      console.error('保存数据时出错:', error)
    }
  } else {
    console.error('RepairBone组件引用不存在')
  }
}

// 添加重置处理函数
const handleReset = (type: 'current' | 'all') => {
  if (type === 'current') {
    // 检查当前帧是否有数据
    if (!hasCurrentFrameData.value) {
      console.log('当前帧没有骨骼数据，无法重置')
      return
    }
    Modal.confirm({
      title: '确认重置当前帧',
      content: '确定要重置当前帧吗？',
      cancelText: '取消',
      okText: '确认',
      onOk() {
        repairBoneRef.value?.resetCurrentFrame()
      },
    })

  } else if (type === 'all') {
    Modal.confirm({
      title: '确认重置所有帧',
      content: '确定要重置所有帧吗？',
      cancelText: '取消',
      okText: '确认',
      onOk() {
        repairBoneRef.value?.resetAllFrames()
      },
    })
  }
}
const handleSwitchChange = () => {
  //触发.ef-top-center点击，传递$event和['head', 'leftArm', 'rightArm', 'leftLeg', 'rightLeg', 'leftFoot', 'rightFoot']
  const efTopCenter = document.querySelector('.ef-top-center')
  if (efTopCenter) {
    const event = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    })
    efTopCenter.dispatchEvent(event)
  }
}
// 切换元素的 project-red 类
const toggleRedClass = (event: MouseEvent, stoneType: string | string[]) => {
  // 阻止事件冒泡，确保只有点击的元素会被处理
  event.stopPropagation()

  // 检查当前帧是否有骨骼数据
  if (!hasCurrentFrameData.value) {
    message.error('当前帧没有骨骼数据，无法进行选择操作')
    return
  }
  const stoneTypes = Array.isArray(stoneType) ? stoneType : [stoneType]
  console.log(event, stoneTypes)
  const target = event.currentTarget as HTMLElement

  repairBoneRef.value?.selectNodeGroup(stoneTypes)

  const className = target.className

  // 处理 ef-top-center 的特殊点击行为
  if (className.includes('ef-top-center')) {
    // 获取 ef-head 和 ef-body 元素
    const efHead = document.querySelector('.ef-head')
    const efBody = document.querySelector('.ef-body')

    // 检查 ef-top-center 是否已有 project-red 类
    const hasRedClass = target.classList.contains('project-red')

    if (hasRedClass) {
      // 如果已有类，移除所有相关元素的类
      target.classList.remove('project-red')

      if (efHead) efHead.classList.remove('project-red')

      if (efBody) {
        // 不给 ef-body 自身添加样式，只操作子元素
        // 移除 ef-body 下所有元素的类
        const allElements = efBody.querySelectorAll('*')
        allElements.forEach(el => el.classList.remove('project-red'))
      }
    } else {
      // 如果没有类，添加到所有相关元素
      target.classList.add('project-red')

      if (efHead) efHead.classList.add('project-red')

      if (efBody) {
        // 不给 ef-body 自身添加样式，只操作子元素
        // 添加到 ef-body 下所有元素
        const allElements = efBody.querySelectorAll('*')
        allElements.forEach(el => el.classList.add('project-red'))
      }
    }
    return
  }

  // 检查是否是主要容器元素（ef-top-left, ef-top-right, ef-bottom-left, ef-bottom-right）
  const isMainContainer = ['ef-top-left', 'ef-top-right', 'ef-bottom-left', 'ef-bottom-right'].some(
    cls => className.includes(cls) && !className.includes('inner')
  )

  if (isMainContainer) {
    // 如果是主容器元素，给自身和所有子元素添加/移除类
    const hasRedClass = target.classList.contains('project-red')

    target.classList.toggle('project-red')

    // 获取所有子元素并切换它们的类
    const children = target.querySelectorAll('*')
    children.forEach(child => {
      if (hasRedClass) {
        child.classList.remove('project-red')
      } else {
        child.classList.add('project-red')
      }
    })
  } else {
    let parent = target.parentElement

    if (parent) {
      // 检查父元素是否是 ef-bottom-left 或 ef-bottom-right
      const isBottomParent = ['ef-bottom-left', 'ef-bottom-right'].some(
        cls => parent?.className.includes(cls)
      )

      if (isBottomParent) {
        // 获取所有兄弟节点
        const siblings = Array.from(parent.children).filter(child => child !== target)

        // 检查是否有兄弟节点具有 project-red 类
        const hasSiblingWithRedClass = siblings.some(sibling =>
          sibling.classList.contains('project-red')
        )

        if (hasSiblingWithRedClass || target.classList.contains('project-red')) {
          // 如果有兄弟节点具有类或自身已有类，切换自身和父元素的类
          const hasRedClass = target.classList.contains('project-red')

          if (hasRedClass) {
            // 移除自身和父元素的类
            target.classList.remove('project-red')
            parent.classList.remove('project-red')
          } else {
            // 添加到自身和父元素
            target.classList.add('project-red')
            parent.classList.add('project-red')
          }
        } else {
          // 如果没有兄弟节点具有类，只切换自身的类
          target.classList.toggle('project-red')
        }

        return
      }

      // 检查父元素是否是 ef-top-left 或 ef-top-right
      const isTopParent = ['ef-top-left', 'ef-top-right'].some(
        cls => parent?.className.includes(cls)
      )

      if (isTopParent) {
        // 对于 ef-top-left 和 ef-top-right 的子元素，行为与父元素一致
        // 获取所有兄弟节点（包括父元素的所有子元素）
        const siblings = Array.from(parent.children)

        // 切换自身的类
        const hasRedClass = target.classList.contains('project-red')
        target.classList.toggle('project-red')

        // 同步父元素的类
        if (hasRedClass) {
          parent.classList.remove('project-red')
          // 移除所有兄弟元素的类
          siblings.forEach(sibling => {
            if (sibling !== target) {
              sibling.classList.remove('project-red')
            }
          })
        } else {
          parent.classList.add('project-red')
          // 添加到所有兄弟元素
          siblings.forEach(sibling => {
            if (sibling !== target) {
              sibling.classList.add('project-red')
            }
          })
        }

        return
      }
    }

    target.classList.toggle('project-red')
  }
}
const handleSwapLeftRight = () => {
  if (!hasCurrentFrameData.value) {
    console.log('当前帧没有骨骼数据，无法左右互换')
    return
  }
  if (repairBoneRef.value) {
    repairBoneRef.value.swapLeftRightNodes()
  } else {
    console.error('RepairBone组件引用不存在')
  }
}
const canSwapLeftRight = computed(() => {
  return repairBoneRef.value?.canSwapLeftRight ?? false
})

const canCopy = computed(() => {
  return repairBoneRef.value?.canCopy ?? false
})
const hasCopiedData = computed(() => {
  return repairBoneRef.value?.hasCopiedData ?? false
})

const hasCurrentFrameData = computed(() => {
  return repairBoneRef.value?.hasCurrentFrameData ?? false
})

const handleCopy = () => {
  if (!hasCurrentFrameData.value) {
    console.log('当前帧没有骨骼数据，无法复制')
    return
  }
  if (repairBoneRef.value) {
    repairBoneRef.value.copySelectedPoints()
  } else {
    console.error('RepairBone组件引用不存在')
  }
}
const handlePaste = () => {
  if (!hasCurrentFrameData.value) {
    console.log('当前帧没有骨骼数据，无法粘贴')
    return
  }
  if (repairBoneRef.value) {
    repairBoneRef.value.pasteToCurrentFrame()
  } else {
    console.error('RepairBone组件引用不存在')
  }
}

// 线性插值功能
const handleInterpolation = () => {
  if (repairBoneRef.value?.handleInterpolation) {
    repairBoneRef.value.handleInterpolation(startFrame.value, endFrame.value);
  } else {
    console.error('插值函数不可用');
  }
};

// 编辑模式切换处理函数
const handleModeChange = (modeId: string) => {
  currentMode.value = modeId
}

// 获取脚部点的颜色状态
const getFootPointColor = (foot: 'left' | 'right', index: 0 | 1) => {

  if (!hasCurrentFrameData.value) return '';
  if (currentMode.value !== 'ik') return 'green';
  if (!repairBoneRef.value) return 'green';

  // 获取 SimpleSkeletonPlayer 组件的引用
  const skeletonPlayer = repairBoneRef.value.skeletonPlayerRef;
  if (!skeletonPlayer) return 'green';


  const player = skeletonPlayer;
  if (!player.getPointIndexByFootPosition || !player.getPointCurrentColor) return 'green';

  // 根据脚部位置和索引获取对应的pointIndex
  const pointIndex = player.getPointIndexByFootPosition(foot, index);

  if (pointIndex === undefined) return 'green';

  // 获取点的当前颜色
  return player.getPointCurrentColor(pointIndex);
}

// 处理脚部点的点击事件
const handleFootPointClick = (foot: 'left' | 'right', index: 0 | 1) => {

  if (!hasCurrentFrameData.value) {
    return message.warning('当前帧没有骨骼数据，无法切换点颜色');
  }

  if (currentMode.value !== 'ik') {
    return;
  }

  if (!repairBoneRef.value) {
    console.error('RepairBone组件引用不存在');
    return;
  }

  // 获取 SimpleSkeletonPlayer 组件的引用
  const skeletonPlayer = repairBoneRef.value.skeletonPlayerRef;
  if (!skeletonPlayer) {
    console.error('SimpleSkeletonPlayer组件引用不存在');
    return;
  }

  const player = skeletonPlayer;
  if (!player.getPointIndexByFootPosition || !player.togglePointColor) {
    console.error('SimpleSkeletonPlayer组件方法不存在');
    return;
  }

  // 根据脚部位置和索引获取对应的pointIndex
  const pointIndex = player.getPointIndexByFootPosition(foot, index);
  if (pointIndex === undefined) {
    console.error('无法获取pointIndex');
    return;
  }

  // 切换点的颜色
  player.togglePointColor(pointIndex);

}

// 确保组件挂载后能正确获取引用
onMounted(() => {
  console.log('projects/index.vue已挂载');
  console.log('RepairBone引用状态:', repairBoneRef.value ? '已获取' : '未获取');
})
</script>

<style lang="scss" scoped>
.action-btn {
  @include action-btn;
}

.projects-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: url('~/assets/images/upgrade-bg.webp');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;

  .projects-content {
    flex: 1;
    padding: rem(172) rem(120) rem(90);
    display: flex;
    flex-direction: column;
    gap: rem(8);
  }

  .disabled {
    opacity: 0.3 !important;
    cursor: not-allowed !important;
  }
}

.project-red {
  background-color: red !important;
}

.upper-section {
  display: flex;
  gap: rem(8);
  height: 60%;
  min-height: rem(716);
}

// 视频播放器区域
.video-player-section {
  flex: 1;
  background: var(--color-background-primary);
}


// 关键姿势区域
.key-pose-section {
  width: rem(328);
  min-height: rem(716);
  border-radius: rem(10);
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0)) 1 1;
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to top, var(--color-background-gradient-from) 0%, var(--color-background-gradient-to) 100%);
  padding: rem(93) rem(24) 0;
  position: relative;
}

.key-pose-header {
  margin-bottom: rem(33);

  h3 {
    color: var(--color-text-primary);
    font-size: rem(18);
    font-weight: 600;
    margin: 0;
  }
}

.key-pose-display {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: center;
}

.key-pose-ik {
  width: 100%;
  flex: 1;
  display: flex;
  justify-content: center;
  gap: rem(24);
  user-select: none;
  padding-top: rem(100);

  img {
    width: rem(84);
    height: rem(68);
  }

  .pi-left-foot,
  .pi-right-foot {
    position: relative;
    width: rem(84);
    height: rem(68);

    .pi-left-foot-inner,
    .pi-right-foot-inner {
      position: absolute;
      width: rem(20);
      height: rem(20);
      background-color: rgba(255, 255, 255, 0.1);
      border: rem(1) solid #fff;
      border-radius: 50%;
      cursor: pointer;

      &.red {
        color: $skeleton-point-red;
        background-color: $skeleton-point-red;
      }

      &.green {
        color: $skeleton-point-green;
        background-color: $skeleton-point-green;
      }
    }
  }

  .pi-left-foot {
    .pi-left-foot-inner {
      &:nth-child(2) {
        top: rem(15);
        right: rem(13);
      }

      &:nth-child(3) {
        left: rem(22);
        bottom: rem(2);
      }
    }
  }

  .pi-right-foot {
    .pi-right-foot-inner {
      &:nth-child(2) {
        top: rem(15);
        left: rem(13);
      }

      &:nth-child(3) {
        right: rem(22);
        bottom: rem(2);
      }
    }
  }

}

.pose-figure {
  width: 100%;
  position: relative;
  margin-bottom: rem(23);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .ef-head {
    width: rem(60);
    height: rem(60);
    border-radius: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid #fff;
  }

  .ef-body {
    position: relative;
    width: 80%;
    height: rem(200);
    margin-top: rem(10);

    &>div {
      position: absolute;

      &>div {
        width: rem(20);
        height: rem(20);
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.1);
        position: absolute;
        border: 1px solid #fff;
        z-index: 99;
        cursor: pointer;
      }
    }

    .ef-top-left {
      width: 35%;
      height: rem(20);
      background-color: rgba(255, 255, 255, 0.1);
      top: 0;
      left: 0;
      border-top-left-radius: rem(10);
      border-bottom-left-radius: rem(10);

      &>div {
        right: rem(-5);
        top: 0;
      }

    }

    .ef-top-right {
      width: 35%;
      height: rem(20);
      background-color: rgba(255, 255, 255, 0.1);
      top: 0;
      right: 0;
      border-top-right-radius: rem(10);
      border-bottom-right-radius: rem(10);

      &>div {
        left: rem(-5);
        top: 0;
      }

    }

    .ef-top-center {
      width: 30%;
      height: rem(120);
      background-color: rgba(255, 255, 255, 0.1);
      top: 0;
      left: 35%;

      &>div {
        &:first-child {
          left: 0;
          bottom: rem(-10);
        }

        &:nth-child(2) {
          right: 0;
          bottom: rem(-10);
        }
      }
    }

    .ef-bottom-left {
      width: rem(20);
      height: rem(80);
      background-color: rgba(255, 255, 255, 0.1);
      top: rem(120);
      left: 35%;
      border-radius: rem(10);
      border-top-left-radius: 0;
      border-top-right-radius: 0;

      &>div {
        &:nth-child(1) {
          left: 0;
          top: rem(-5);
        }

        &:nth-child(2) {
          left: 0;
          bottom: 0;
        }
      }
    }

    .ef-bottom-right {
      width: rem(20);
      height: rem(80);
      background-color: rgba(255, 255, 255, 0.1);
      top: rem(120);
      right: 35%;
      border-radius: rem(10);
      border-top-left-radius: 0;
      border-top-right-radius: 0;

      &>div {
        &:nth-child(1) {
          right: 0;
          top: rem(-5);
        }

        &:nth-child(2) {
          right: 0;
          bottom: 0;
        }
      }
    }

  }
}

// 简单的人体姿势图标
.stick-figure {
  position: relative;
  width: rem(80);
  height: rem(120);
}

.figure-head {
  width: rem(20);
  height: rem(20);
  background: #ffffff;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.figure-body {
  width: rem(3);
  height: rem(40);
  background: #ffffff;
  position: absolute;
  top: rem(20);
  left: 50%;
  transform: translateX(-50%);
}

.figure-arms {
  position: absolute;
  top: rem(25);
  left: 50%;
  transform: translateX(-50%);
}

.arm {
  width: rem(25);
  height: rem(3);
  background: #ffffff;
  position: absolute;

  &.left-arm {
    transform: rotate(-30deg);
    left: rem(-20);
  }

  &.right-arm {
    transform: rotate(30deg);
    right: rem(-20);
  }
}

.figure-legs {
  position: absolute;
  top: rem(60);
  left: 50%;
  transform: translateX(-50%);
}

.leg {
  width: rem(3);
  height: rem(30);
  background: #ffffff;
  position: absolute;

  &.left-leg {
    transform: rotate(-15deg);
    left: rem(-8);
  }

  &.right-leg {
    transform: rotate(15deg);
    right: rem(-8);
  }
}

.pose-controls {
  .pose-option {
    display: flex;
    align-items: center;
    gap: rem(8);
    color: #ffffff;
    cursor: pointer;

    input[type="radio"] {
      accent-color: #1890ff;
    }
  }
}

// 下半部分：视频编辑控制
.lower-section {
  @include theme-background-primary;
  border-radius: rem(10);
  display: flex;
  flex-direction: column;
  padding: 0 rem(24);
}

.video-editing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: rem(91);

  font-style: normal;
  position: relative;

  .video-edit-tips {
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: bold;
    font-size: rem(24);
    color: #FFFFFF;
    line-height: rem(38);
    text-align: left;
  }


  &::after {
    content: '';
    width: 100%;
    height: rem(1);
    width: 100%;
    background: linear-gradient(270deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
    position: absolute;
    bottom: 0;
    left: 0;
  }
}

.editing-actions {
  display: flex;
  gap: rem(14);

  .icon-reset,
  .icon-reset_defalut {
    font-size: rem(14);
    margin-right: rem(8);
  }
}


.editing-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: rem(40) rem(100);
  position: relative;

  .editing-modes {
    display: flex;

    .mode-btn {
      padding: rem(36) rem(60);
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: 400;
      font-size: rem(24);
      @include theme-text-primary;
      line-height: rem(38);
      text-align: left;
      font-style: normal;
      @include theme-background-secondary;
      border-radius: rem(3);
      cursor: pointer;

      &.active {
        background: var(--color-primary);
      }
    }
  }

  .left-controls {
    flex: 1;
    min-width: rem(348);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    gap: rem(16);
    position: relative;

    &::before {
      content: '';
      width: rem(1);
      height: rem(140);
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
      position: absolute;
      left: 13%;
      top: rem(-20);
    }

    &::after {
      content: '';
      width: rem(1);
      height: rem(140);
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
      position: absolute;
      right: 13%;
      top: rem(-20);
    }


    .tc-top {
      gap: rem(16);
      display: flex;

      .control-div {
        padding: rem(10) rem(64);
        border-radius: rem(4);
        border: 1px solid rgba(255, 255, 255, 0.3);
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(18);
        color: #FFFFFF;
        font-style: normal;
        cursor: pointer;
      }
    }

    .tc-bottom {
      width: rem(348);
      border-radius: rem(4);
      border: 1px solid rgba(255, 255, 255, 0.3);
      font-family: SourceHanSerifSC, SourceHanSerifSC;
      font-weight: 400;
      font-size: rem(18);
      color: #FFFFFF;
      font-style: normal;
      padding: rem(8) 0;
      text-align: center;
      cursor: pointer;
    }
  }

  .right-status {
    display: flex;
    flex-direction: column;
    gap: rem(16);

    .ts-top {
      display: flex;
      gap: rem(16);

      .status-item {
        min-width: rem(164);
        padding: rem(9) rem(16);
        border-radius: rem(4);
        border: 1px solid rgba(255, 255, 255, 0.3);
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(18);
        color: #FFFFFF;
        font-style: normal;
        cursor: pointer;
        display: flex;
        justify-content: space-between;

        .status-value {
          input {
            background: transparent;
            border: none;
            outline: none;
            width: rem(60);
            color: #FFFFFF;
            text-align: right;
            height: rem(27);
            font-weight: 400;
            font-size: rem(18);
          }
        }
      }
    }

    .ts-center,
    .ts-bottom {
      .control-div {
        background: #0091FF;
        border-radius: rem(4);
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: 400;
        font-size: rem(18);
        color: #FFFFFF;
        font-style: normal;
        padding: rem(9) 0;
        text-align: center;
        cursor: pointer;
      }
    }

    .ts-bottom {
      .control-div {
        background: transparent;
        border: rem(1) solid rgba(255, 255, 255, 0.3);
      }
    }
  }
}
</style>
