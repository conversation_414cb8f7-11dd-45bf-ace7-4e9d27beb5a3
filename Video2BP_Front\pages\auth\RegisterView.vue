<template>
  <div class="auth-content">
    <TabsHeader v-model="activeTab" :tabs="tabsList" @tab-change="handleTabChange" />

    <div class="auth-form-container">
      <form v-if="activeTab === 'personal'" class="custom-form" @submit.prevent="onPersonalRegisterFinish">
        <div class="form-item" :class="{ 'has-error': personalErrors.account }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <PhoneOutlined />
            </span>
            <input type="text" v-model="personalRegisterForm.account" placeholder="请输入手机号或邮箱" class="form-input"
              @blur="validatePersonalAccount" />
          </div>
          <div v-if="personalErrors.account" class="error-message">{{ personalErrors.account }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': personalErrors.verificationCode }">
          <div class="input-group">
            <input type="text" v-model="personalRegisterForm.verificationCode" placeholder="邮箱验证码"
              class="form-input code-input" @blur="validatePersonalCode" />
            <button type="button" class="code-button" :disabled="personalCodeSending || personalCodeCountdown > 0"
              @click="sendPersonalVerificationCode">
              {{ personalCodeCountdown > 0 ? `${personalCodeCountdown}秒后重试` : '发送验证码' }}
            </button>
          </div>
          <div v-if="personalErrors.verificationCode" class="error-message">{{ personalErrors.verificationCode }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': personalErrors.password }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <LockOutlined />
            </span>
            <input :type="personalPasswordVisible ? 'text' : 'password'" v-model="personalRegisterForm.password"
              placeholder="密码" class="form-input" @blur="validatePersonalPassword" />
            <span class="input-suffix" @click="personalPasswordVisible = !personalPasswordVisible">
              <EyeOutlined v-if="personalPasswordVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="personalErrors.password" class="error-message">{{ personalErrors.password }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': personalErrors.confirmPassword }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <LockOutlined />
            </span>
            <input :type="personalConfirmPasswordVisible ? 'text' : 'password'"
              v-model="personalRegisterForm.confirmPassword" placeholder="确认密码" class="form-input"
              @blur="validatePersonalConfirmPassword" />
            <span class="input-suffix" @click="personalConfirmPasswordVisible = !personalConfirmPasswordVisible">
              <EyeOutlined v-if="personalConfirmPasswordVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="personalErrors.confirmPassword" class="error-message">{{ personalErrors.confirmPassword }}</div>
        </div>


        <!-- <div class="form-item checkbox-item">
          <label class="custom-checkbox">
            <input type="checkbox" v-model="personalRegisterForm.agreeToTerms" />
            <span class="checkmark"></span>
            <span class="checkbox-text">我同意用户协议和隐私声明</span>
          </label>
          <div v-if="personalErrors.agreeToTerms" class="error-message">{{ personalErrors.agreeToTerms }}</div>
        </div> -->

        <div class="form-item">
          <button type="submit" class="submit-button">注册</button>
        </div>
      </form>

      <form v-else class="custom-form" @submit.prevent="onApiRegisterFinish">
        <div class="form-item" :class="{ 'has-error': apiErrors.account }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <PhoneOutlined />
            </span>
            <input type="text" v-model="apiRegisterForm.account" placeholder="请输入手机号或邮箱" class="form-input"
              @blur="validateApiAccount" />
          </div>
          <div v-if="apiErrors.account" class="error-message">{{ apiErrors.account }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': apiErrors.verificationCode }">
          <div class="input-group">
            <input type="text" v-model="apiRegisterForm.verificationCode" placeholder="验证码"
              class="form-input code-input" @blur="validateApiCode" />
            <button type="button" class="code-button" :disabled="apiCodeSending || apiCodeCountdown > 0"
              @click="sendApiVerificationCode">
              {{ apiCodeCountdown > 0 ? `${apiCodeCountdown}秒后重试` : '发送验证码' }}
            </button>
          </div>
          <div v-if="apiErrors.verificationCode" class="error-message">{{ apiErrors.verificationCode }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': apiErrors.password }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <LockOutlined />
            </span>
            <input :type="apiPasswordVisible ? 'text' : 'password'" v-model="apiRegisterForm.password" placeholder="密码"
              class="form-input" @blur="validateApiPassword" />
            <span class="input-suffix" @click="apiPasswordVisible = !apiPasswordVisible">
              <EyeOutlined v-if="apiPasswordVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="apiErrors.password" class="error-message">{{ apiErrors.password }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': apiErrors.confirmPassword }">
          <div class="input-wrapper">
            <span class="input-prefix">
              <LockOutlined />
            </span>
            <input :type="apiConfirmPasswordVisible ? 'text' : 'password'" v-model="apiRegisterForm.confirmPassword"
              placeholder="确认密码" class="form-input" @blur="validateApiConfirmPassword" />
            <span class="input-suffix" @click="apiConfirmPasswordVisible = !apiConfirmPasswordVisible">
              <EyeOutlined v-if="apiConfirmPasswordVisible" />
              <EyeInvisibleOutlined v-else />
            </span>
          </div>
          <div v-if="apiErrors.confirmPassword" class="error-message">{{ apiErrors.confirmPassword }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': apiErrors.companyName }">
          <div class="input-wrapper">
            <input type="text" v-model="apiRegisterForm.companyName" placeholder="公司" class="form-input"
              @blur="validateApiCompanyName" />
          </div>
          <div v-if="apiErrors.companyName" class="error-message">{{ apiErrors.companyName }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': apiErrors.industry }">
          <div class="input-wrapper custom-select">
            <select v-model="apiRegisterForm.industry" class="form-select" @blur="validateApiIndustry">
              <option value="" disabled selected>行业</option>
              <option value="tech">科技</option>
              <option value="finance">金融</option>
              <option value="education">教育</option>
            </select>
          </div>
          <div v-if="apiErrors.industry" class="error-message">{{ apiErrors.industry }}</div>
        </div>

        <div class="form-item" :class="{ 'has-error': apiErrors.phoneNumber }">
          <div class="input-wrapper">
            <input type="text" v-model="apiRegisterForm.phoneNumber" placeholder="手机号" class="form-input"
              @blur="validateApiPhoneNumber" />
          </div>
          <div v-if="apiErrors.phoneNumber" class="error-message">{{ apiErrors.phoneNumber }}</div>
        </div>

        <div class="form-item checkbox-item">
          <label class="custom-checkbox">
            <input type="checkbox" v-model="apiRegisterForm.agreeToTerms" />
            <span class="checkmark"></span>
            <span class="checkbox-text">我同意用户协议和隐私声明</span>
          </label>
          <div v-if="apiErrors.agreeToTerms" class="error-message">{{ apiErrors.agreeToTerms }}</div>
        </div>

        <div class="form-item">
          <button type="submit" class="submit-button">注册</button>
        </div>
      </form>

      <div class="auth-form-footer">
        <div class="switch-auth-link">
          已经注册账号? <a href="#" @click.prevent="$emit('switchToLogin')">登录账号</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LockOutlined, PhoneOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'
import TabsHeader from './components/Header.vue'
import type { Tab } from './components/Header.vue'
import { useApi } from '~/composables/useApi'


const { auth } = useApi()

const emit = defineEmits(['switchToLogin', 'registrationComplete'])

const activeTab = ref('personal')
const tabsList = ref<Tab[]>([
  { key: 'personal', label: '账号登录' },
  // { key: 'api', label: 'API登录' },
])

// 验证码发送状态
const personalCodeSending = ref(false)
const personalCodeCountdown = ref(0)
const apiCodeSending = ref(false)
const apiCodeCountdown = ref(0)

// 密码可见性控制
const personalPasswordVisible = ref(false)
const personalConfirmPasswordVisible = ref(false)
const apiPasswordVisible = ref(false)
const apiConfirmPasswordVisible = ref(false)

// 处理标签页切换事件
const handleTabChange = (key: string) => {
  console.log('Tab changed to:', key)
}

const personalRegisterForm = reactive({
  account: '',
  accountType: '', // 'phone' 或 'email'
  verificationCode: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: false,
  subscribeToUpdates: false,
})

const apiRegisterForm = reactive({
  account: '',
  accountType: '', // 'phone' 或 'email'
  verificationCode: '',
  password: '',
  confirmPassword: '',
  companyName: '',
  industry: '',
  commonSoftware: '',
  phoneNumber: '',
  agreeToTerms: false,
  subscribeToUpdates: false,
})

// 表单验证错误信息
const personalErrors = reactive({
  account: '',
  verificationCode: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: '',
})

const apiErrors = reactive({
  account: '',
  verificationCode: '',
  password: '',
  confirmPassword: '',
  companyName: '',
  industry: '',
  phoneNumber: '',
  agreeToTerms: '',
})

// 添加账号类型识别函数
const identifyAccountType = (account: string): 'phone' | 'email' | 'unknown' => {
  const phoneRegex = /^1[3-9]\d{9}$/
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

  if (phoneRegex.test(account)) {
    return 'phone'
  } else if (emailRegex.test(account)) {
    return 'email'
  } else {
    return 'unknown'
  }
}

// 验证函数
const validatePersonalAccount = () => {
  if (!personalRegisterForm.account) {
    personalErrors.account = '请输入手机号或邮箱!'
    personalRegisterForm.accountType = ''
    return false
  }

  const accountType = identifyAccountType(personalRegisterForm.account)
  personalRegisterForm.accountType = accountType

  if (accountType === 'unknown') {
    personalErrors.account = '请输入有效的手机号或邮箱!'
    return false
  }

  personalErrors.account = ''
  return true
}

const validatePersonalCode = () => {
  if (!personalRegisterForm.verificationCode) {
    personalErrors.verificationCode = '请输入邮箱验证码!'
    return false
  }
  personalErrors.verificationCode = ''
  return true
}

const validatePersonalPassword = () => {
  if (!personalRegisterForm.password) {
    personalErrors.password = '请输入密码!'
    return false
  }
  personalErrors.password = ''
  return true
}

const validatePersonalConfirmPassword = () => {
  if (!personalRegisterForm.confirmPassword) {
    personalErrors.confirmPassword = '请再次输入密码!'
    return false
  } else if (personalRegisterForm.confirmPassword !== personalRegisterForm.password) {
    personalErrors.confirmPassword = '两次输入的密码不一致!'
    return false
  }
  personalErrors.confirmPassword = ''
  return true
}

const validateApiAccount = () => {
  if (!apiRegisterForm.account) {
    apiErrors.account = '请输入手机号或邮箱!'
    apiRegisterForm.accountType = ''
    return false
  }

  const accountType = identifyAccountType(apiRegisterForm.account)
  apiRegisterForm.accountType = accountType

  if (accountType === 'unknown') {
    apiErrors.account = '请输入有效的手机号或邮箱!'
    return false
  }

  apiErrors.account = ''
  return true
}

const validateApiCode = () => {
  if (!apiRegisterForm.verificationCode) {
    apiErrors.verificationCode = '请输入验证码!'
    return false
  }
  apiErrors.verificationCode = ''
  return true
}

const validateApiPassword = () => {
  if (!apiRegisterForm.password) {
    apiErrors.password = '请输入密码!'
    return false
  }
  apiErrors.password = ''
  return true
}

const validateApiConfirmPassword = () => {
  if (!apiRegisterForm.confirmPassword) {
    apiErrors.confirmPassword = '请再次输入密码!'
    return false
  } else if (apiRegisterForm.confirmPassword !== apiRegisterForm.password) {
    apiErrors.confirmPassword = '两次输入的密码不一致!'
    return false
  }
  apiErrors.confirmPassword = ''
  return true
}

const validateApiCompanyName = () => {
  if (!apiRegisterForm.companyName) {
    apiErrors.companyName = '请输入公司名称!'
    return false
  }
  apiErrors.companyName = ''
  return true
}

const validateApiIndustry = () => {
  if (!apiRegisterForm.industry) {
    apiErrors.industry = '请选择行业!'
    return false
  }
  apiErrors.industry = ''
  return true
}

const validateApiPhoneNumber = () => {
  if (!apiRegisterForm.phoneNumber) {
    apiErrors.phoneNumber = '请输入手机号!'
    return false
  }
  apiErrors.phoneNumber = ''
  return true
}

const validatePersonalForm = () => {
  const accountValid = validatePersonalAccount()
  const codeValid = validatePersonalCode()
  const passwordValid = validatePersonalPassword()
  const confirmPasswordValid = validatePersonalConfirmPassword()

  // if (!personalRegisterForm.agreeToTerms) {
  //   personalErrors.agreeToTerms = '请同意用户协议和隐私声明'
  //   return false
  // } else {
  //   personalErrors.agreeToTerms = ''
  // }

  return accountValid && codeValid && passwordValid && confirmPasswordValid
}

const validateApiForm = () => {
  const accountValid = validateApiAccount()
  const codeValid = validateApiCode()
  const passwordValid = validateApiPassword()
  const confirmPasswordValid = validateApiConfirmPassword()
  const companyNameValid = validateApiCompanyName()
  const industryValid = validateApiIndustry()
  const phoneNumberValid = validateApiPhoneNumber()

  // if (!apiRegisterForm.agreeToTerms) {
  //   apiErrors.agreeToTerms = '请同意用户协议和隐私声明'
  //   return false
  // } else {
  //   apiErrors.agreeToTerms = ''
  // }

  return accountValid && codeValid && passwordValid && confirmPasswordValid &&
    companyNameValid && industryValid && phoneNumberValid
}

const onPersonalRegisterFinish = async () => {
  if (validatePersonalForm()) {
    try {
      // 根据账号类型构建不同的注册参数
      const registerParams = {
        email: personalRegisterForm.account,
        code: personalRegisterForm.verificationCode,
        password: personalRegisterForm.password,
      }

      // 调用注册API
      await auth?.register(registerParams)
      message.success('注册成功');
      emit('registrationComplete')
    } catch (error) {
      message.error(error as string)
    }
  }
}

const onApiRegisterFinish = async () => {
  if (validateApiForm()) {
    try {
      // 根据账号类型构建不同的注册参数
      const registerParams = {
        accountType: apiRegisterForm.accountType,
        account: apiRegisterForm.account,
        verificationCode: apiRegisterForm.verificationCode,
        password: apiRegisterForm.password,
        companyName: apiRegisterForm.companyName,
        industry: apiRegisterForm.industry,
        commonSoftware: apiRegisterForm.commonSoftware || undefined,
        phoneNumber: apiRegisterForm.phoneNumber,
      }

      // 调用注册API
      // const result = await auth?.registerApi(registerParams)

      // 如果API尚未实现，可以先模拟成功
      console.log('API注册参数:', registerParams)
      emit('registrationComplete')
    } catch (error) {
      console.error('注册失败:', error)
      alert('注册失败，请稍后重试')
    }
  }
}

/**
 * 发送个人账号验证码
 */
const sendPersonalVerificationCode = async () => {
  if (!validatePersonalAccount()) {
    return
  }

  try {
    personalCodeSending.value = true
    await auth?.sendEmailVerificationCode(personalRegisterForm.account)
    message.success('验证码已发送');
    // 开始倒计时
    personalCodeCountdown.value = 60
    const timer = setInterval(() => {
      personalCodeCountdown.value--
      if (personalCodeCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)

  } catch (error) {
    console.error('发送验证码出错:', error)
  } finally {
    personalCodeSending.value = false
  }
}

/**
 * 发送API账号验证码
 */
const sendApiVerificationCode = async () => {
  // if (!validateApiAccount()) {
  //   return
  // }

  // try {
  //   apiCodeSending.value = true
  //   const result = await auth?.sendEmailVerificationCode(apiRegisterForm.account)

  //   message.success('验证码已发送');
  //   console.log(result, 'result')


  //   if (result?.success) {
  //     // 开始倒计时
  //     apiCodeCountdown.value = 60
  //     const timer = setInterval(() => {
  //       apiCodeCountdown.value--
  //       if (apiCodeCountdown.value <= 0) {
  //         clearInterval(timer)
  //       }
  //     }, 1000)
  //   } else {
  //     alert(result.message || '发送验证码失败')
  //   }
  // } catch (error) {
  //   console.error('发送验证码出错:', error)
  //   alert('发送验证码失败，请稍后重试')
  // } finally {
  //   apiCodeSending.value = false
  // }
}
</script>

<style lang="scss" scoped>
.auth-content {
  width: rem(500);
  padding: rem(60) rem(40);
  background-color: transparent;
  color: #e0e0e0;
  box-shadow: 0 rem(2) rem(8) 0 #d9e7ff;
  border-radius: rem(16);
  border: 1px solid #ffffff;
}

.custom-form {
  width: 100%;
}

.form-item {
  margin-bottom: rem(20);
  position: relative;

  &.has-error .form-input,
  &.has-error .form-select,
  &.has-error .code-input {
    border-color: #ff4d4f;
  }

  .error-message {
    color: #ff4d4f;
    font-size: rem(14);
    margin-top: rem(4);
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: 1px solid #555;
  border-radius: rem(4);
  height: rem(64);

  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 rem(2) rgba(24, 144, 255, 0.2);
  }
}

.input-prefix {
  display: flex;
  align-items: center;
  justify-content: center;
  height: rem(50);
  width: rem(40);
  color: #888;
  margin-left: rem(10);
  font-size: rem(20);
}

.input-suffix {
  display: flex;
  align-items: center;
  justify-content: center;
  height: rem(50);
  width: rem(40);
  color: #888;
  cursor: pointer;
  margin-right: rem(10);
}

.form-input {
  flex: 1;
  height: rem(50);
  background-color: transparent;
  border: none;
  outline: none;
  color: #e0e0e0;
  font-size: rem(22);
  padding: 0 rem(10);

  &::placeholder {
    color: #888;
  }

  &:focus {
    outline: none;
  }
}

.form-select {
  flex: 1;
  height: rem(50);
  background-color: transparent;
  border: none;
  outline: none;
  color: #e0e0e0;
  font-size: rem(22);
  padding: 0 rem(10);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  &:focus {
    outline: none;
  }

  option {
    background-color: #2a2844;
    color: #e0e0e0;
  }
}

.custom-select {
  position: relative;

  &::after {
    content: '▼';
    position: absolute;
    right: rem(15);
    top: 50%;
    transform: translateY(-50%);
    color: #888;
    pointer-events: none;
  }
}

.submit-button {
  width: 100%;
  height: rem(64);
  background-color: #0052cc;
  border: none;
  border-radius: rem(4);
  color: #ffffff;
  font-family: SourceHanSerifSC, SourceHanSerifSC;
  font-weight: bold;
  font-size: rem(24);
  line-height: rem(32);
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #0069d9;
  }

  &:active {
    background-color: #0062cc;
  }
}

.auth-form-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: rem(16);
  margin-top: rem(10);

  .switch-auth-link {
    text-align: center;
    color: #a0a0a0;
  }

  .switch-auth-link a {
    color: #1890ff;
    font-weight: bold;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.input-group {
  display: flex;
  align-items: center;

  .code-input {
    flex: 1;
    border: 1px solid #555;
    border-radius: rem(4);
    height: rem(64);
    background-color: transparent;
    color: #e0e0e0;
    font-size: rem(22);
    padding: 0 rem(15);

    &::placeholder {
      color: #888;
    }

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 rem(2) rgba(24, 144, 255, 0.2);
    }
  }

  .code-button {
    margin-left: rem(10);
    height: rem(64);
    min-width: rem(120);
    background-color: #1890ff;
    border: none;
    border-radius: rem(4);
    color: white;
    font-size: rem(14);
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #40a9ff;
    }

    &:disabled {
      background-color: #d9d9d9;
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;
    }
  }
}

.custom-checkbox {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: rem(30);
  cursor: pointer;
  user-select: none;

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;

    &:checked~.checkmark {
      background-color: #1890ff;
      border-color: #1890ff;

      &:after {
        display: block;
      }
    }
  }

  .checkmark {
    position: absolute;
    left: 0;
    height: rem(18);
    width: rem(18);
    background-color: transparent;
    border: 1px solid #555;
    border-radius: rem(2);

    &:after {
      content: "";
      position: absolute;
      display: none;
      left: rem(6);
      top: rem(2);
      width: rem(5);
      height: rem(10);
      border: solid white;
      border-width: 0 rem(2) rem(2) 0;
      transform: rotate(45deg);
    }
  }

  .checkbox-text {
    color: #a0a0a0;
    font-size: rem(14);
  }
}

.checkbox-item {
  margin-top: rem(15);
}
</style>
