from sqlalchemy import DateTime, cast, <PERSON><PERSON>an
from sqlalchemy import desc

from models.home_model import News
from service.mysql_client import DBSessionContext


def get_news_list(page=1, size=9, news_type=None):
    with DBSessionContext() as db_session:
        query = db_session.query(News).filter(News.is_pinned == True)

        if news_type:
            query = query.filter(News.type == int(news_type))

        total = query.count()
        items = query.order_by(
            desc(cast(News.is_pinned, Boolean)),
            desc(cast(News.created_at, DateTime))
        ).offset((page - 1) * size).limit(size).all()

        news_list = [{
            "id": news.id,
            "title": news.title,
            "cover": news.cover,
            "author": news.author,
            "content": news.content,
            "excerpt": news.excerpt,
            "createdAt": news.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "isPinned": news.is_pinned,
            "profilePicture": news.profile_picture,
            "tag": news.tag,
            "type": news.type,
        } for news in items]

        return {
            "newsList": news_list,
            "totalCount": total,
        }


def get_news_detail(news_id):
    with DBSessionContext() as db_session:
        news = db_session.query(News).filter(
            News.id == news_id,
            News.is_display == True
        ).first()
        
        if not news:
            return None
            
        return {
            "id": news.id,
            "title": news.title,
            "cover": news.cover,
            "author": news.author,
            "content": news.content,
            "excerpt": news.excerpt,
            "createdAt": news.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "isPinned": news.is_pinned,
            "profilePicture": news.profile_picture,
            "tag": news.tag,
            "type": news.type
        }


def get_compony_info():
    return "未完成"


def get_combo_info():
    return "未完成"
