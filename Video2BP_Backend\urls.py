"""记录当前服务中所有的url,便于统一管理"""
from dataclasses import dataclass
from typing import List, ClassVar


@dataclass
class URLConfig:
    """URL配置数据类"""
    url: str
    methods: List[str]
    comment: str


class URLs:
    """URL配置管理类"""
    BASE_URL: ClassVar[str] = "/api"

    system_monitor = URLConfig(
        url=f"{BASE_URL}/monitor",
        methods=["GET"],
        comment="探活"
    )

    # 添加 user_manage.py 中的 URL 配置
    user_send_verification_code = URLConfig(
        url=f"{BASE_URL}/user/send_verification_code",
        methods=["POST"],
        comment="发送邮箱验证码"
    )
    user_register = URLConfig(
        url=f"{BASE_URL}/user/register",
        methods=["POST"],
        comment="用户注册"
    )
    user_login = URLConfig(
        url=f"{BASE_URL}/user/login",
        methods=["POST"],
        comment="用户登录"
    )
    user_logout = URLConfig(
        url=f"{BASE_URL}/user/logout",
        methods=["GET"],
        comment="用户退出登录"
    )
    user_info = URLConfig(
        url=f"{BASE_URL}/user/info",
        methods=["POST"],
        comment="获取用户信息"
    )
    # =====================================
    user_video_upload = URLConfig(
        url=f"{BASE_URL}/video/upload",
        methods=["POST"],
        comment="视频上传"
    )
    user_video_upload_chunk = URLConfig(
        url=f"{BASE_URL}/video/upload_chunk",
        methods=["POST"],
        comment="分片上传"
    )
    user_video_merge_chunk = URLConfig(
        url=f"{BASE_URL}/video/merge_chunks",
        methods=["POST"],
        comment="合并分片"
    )
    user_video_list = URLConfig(
        url=f"{BASE_URL}/video/list",
        methods=["POST"],
        comment="视频列表查询"
    )
    user_video_download = URLConfig(
        url=f"{BASE_URL}/video/download",
        methods=["GET"],
        comment="视频下载"
    )
    user_video_bp_download = URLConfig(
        url=f"{BASE_URL}/video/bp/download",
        methods=["GET"],
        comment="视频骨骼点下载"
    )
    user_video_download_all = URLConfig(
        url=f"{BASE_URL}/video/download/all",
        methods=["GET"],
        comment="打包下载"
    )
    user_video_upload_status = URLConfig(
        url=f"{BASE_URL}/video/upload/status",
        methods=["GET"],
        comment="上传进度查询"
    )
    # =====================================
    compony_info = URLConfig(
        url=f"{BASE_URL}/compony/info",
        methods=["GET"],
        comment="公司信息"
    )
    combo_info = URLConfig(
        url=f"{BASE_URL}/combo/info",
        methods=["GET"],
        comment="套餐信息"
    )
    news_list = URLConfig(
        url=f"{BASE_URL}/news/list",
        methods=["POST"],
        comment="资讯信息"
    )
    news_detail = URLConfig(
        url=f"{BASE_URL}/news/detail",
        methods=["GET"],
        comment="资讯详情信息"
    )
    # =====================================
    order_create = URLConfig(
        url=f"{BASE_URL}/order/create",
        methods=["POST"],
        comment="用户下单"
    )
    @classmethod
    def get_doc(cls) -> str:
        """生成URL文档"""
        return """
============================基础url============================
base_url = "{base_url}"
============================用户模块user=======================
{user}
============================视频模块video=======================
{video}
============================首页模块home=======================
{home}
============================订单模块order=======================
{order}

""".format(
            base_url=cls.BASE_URL,
            user=f"{cls._format_module_urls('user')}\n{cls._format_module_urls('user_send_verification_code')}\n{cls._format_module_urls('user_register')}\n{cls._format_module_urls('user_login')}\n{cls._format_module_urls('user_logout')}\n{cls._format_module_urls('user_info')}",
            video=f"{cls._format_module_urls('user_video_upload')}\n{cls._format_module_urls('user_video_upload_chunk')}\n{cls._format_module_urls('user_video_merge_chunk')}\n{cls._format_module_urls('user_video_list')}\n{cls._format_module_urls('user_video_download')}\n{cls._format_module_urls('user_video_bp_download')}\n{cls._format_module_urls('user_video_download_all')}\n{cls._format_module_urls('user_video_upload_status')}",
            home=f"{cls._format_module_urls('compony_info')}\n{cls._format_module_urls('combo_info')}\n{cls._format_module_urls('information_info')}",
            order=f"{cls._format_module_urls('order_create')}",
        )

    @classmethod
    def _format_module_urls(cls, prefix: str) -> str:
        """格式化模块URL文档"""
        urls = [
            (name, value) for name, value in vars(cls).items()
            if isinstance(value, URLConfig) and name.startswith(prefix)
        ]
        return "\n".join(f"# {url.comment}\n{name}: {url.url}" for name, url in urls)


# 设置模块文档
__doc__ = URLs.get_doc()