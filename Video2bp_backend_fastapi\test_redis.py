#!/usr/bin/env python3
"""
Redis连接测试脚本
验证Redis配置和连接是否正常
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.redis import redis_manager, get_redis, get_async_redis
from core.redis.redis_utils import RedisUtils, AsyncRedisUtils
from core.config import settings


async def test_redis_connection():
    """测试Redis连接"""
    print("=== Redis连接测试 ===\n")
    
    print(f"Redis URL: {settings.REDIS_URL}")
    print(f"配置来源: core/.env 文件\n")
    
    # 测试同步连接
    print("1. 测试同步Redis连接...")
    try:
        sync_client = get_redis()
        result = sync_client.ping()
        print(f"   同步连接测试: {'✅ 成功' if result else '❌ 失败'}")
        
        # 测试基本操作
        test_key = "test:sync:connection"
        sync_client.set(test_key, "Hello Redis!", ex=60)
        value = sync_client.get(test_key)
        print(f"   读写测试: {'✅ 成功' if value == 'Hello Redis!' else '❌ 失败'}")
        sync_client.delete(test_key)
        
    except Exception as e:
        print(f"   同步连接测试: ❌ 失败 - {e}")
    
    print()
    
    # 测试异步连接
    print("2. 测试异步Redis连接...")
    try:
        async_client = await get_async_redis()
        result = await async_client.ping()
        print(f"   异步连接测试: {'✅ 成功' if result else '❌ 失败'}")
        
        # 测试基本操作
        test_key = "test:async:connection"
        await async_client.set(test_key, "Hello Async Redis!", ex=60)
        value = await async_client.get(test_key)
        print(f"   读写测试: {'✅ 成功' if value == 'Hello Async Redis!' else '❌ 失败'}")
        await async_client.delete(test_key)
        
    except Exception as e:
        print(f"   异步连接测试: ❌ 失败 - {e}")
    
    print()


async def test_redis_utils():
    """测试Redis工具类"""
    print("=== Redis工具类测试 ===\n")
    
    # 测试同步工具类
    print("1. 测试同步Redis工具类...")
    try:
        # 字符串操作
        key = "test:utils:string"
        success = RedisUtils.set_string(key, "test value", 60)
        value = RedisUtils.get_string(key)
        print(f"   字符串操作: {'✅ 成功' if success and value == 'test value' else '❌ 失败'}")
        
        # JSON操作
        json_key = "test:utils:json"
        test_data = {"name": "Redis Test", "version": 1.0, "active": True}
        success = RedisUtils.set_json(json_key, test_data, 60)
        retrieved_data = RedisUtils.get_json(json_key)
        print(f"   JSON操作: {'✅ 成功' if success and retrieved_data == test_data else '❌ 失败'}")
        
        # 计数器操作
        counter_key = "test:utils:counter"
        count1 = RedisUtils.incr(counter_key)
        count2 = RedisUtils.incr(counter_key, 5)
        print(f"   计数器操作: {'✅ 成功' if count1 == 1 and count2 == 6 else '❌ 失败'}")
        
        # 清理测试数据
        RedisUtils.delete(key)
        RedisUtils.delete(json_key)
        RedisUtils.delete(counter_key)
        
    except Exception as e:
        print(f"   同步工具类测试: ❌ 失败 - {e}")
    
    print()
    
    # 测试异步工具类
    print("2. 测试异步Redis工具类...")
    try:
        # 字符串操作
        key = "test:async_utils:string"
        success = await AsyncRedisUtils.set_string(key, "async test value", 60)
        value = await AsyncRedisUtils.get_string(key)
        print(f"   字符串操作: {'✅ 成功' if success and value == 'async test value' else '❌ 失败'}")
        
        # JSON操作
        json_key = "test:async_utils:json"
        test_data = {"name": "Async Redis Test", "version": 2.0, "active": True}
        success = await AsyncRedisUtils.set_json(json_key, test_data, 60)
        retrieved_data = await AsyncRedisUtils.get_json(json_key)
        print(f"   JSON操作: {'✅ 成功' if success and retrieved_data == test_data else '❌ 失败'}")
        
        # 计数器操作
        counter_key = "test:async_utils:counter"
        count1 = await AsyncRedisUtils.incr(counter_key)
        count2 = await AsyncRedisUtils.incr(counter_key, 3)
        print(f"   计数器操作: {'✅ 成功' if count1 == 1 and count2 == 4 else '❌ 失败'}")
        
        # 清理测试数据
        await AsyncRedisUtils.delete(key)
        await AsyncRedisUtils.delete(json_key)
        await AsyncRedisUtils.delete(counter_key)
        
    except Exception as e:
        print(f"   异步工具类测试: ❌ 失败 - {e}")
    
    print()


async def test_auth_security_redis():
    """测试认证安全系统的Redis使用"""
    print("=== 认证安全系统Redis测试 ===\n")
    
    try:
        from core.security.auth_security import AuthSecurityService
        
        auth_service = AuthSecurityService()
        test_email = "<EMAIL>"
        
        print("1. 测试登录尝试计数...")
        
        # 模拟登录失败
        attempts1 = await auth_service._increment_login_attempts(test_email)
        attempts2 = await auth_service._increment_login_attempts(test_email)
        print(f"   登录失败计数: 第1次={attempts1}, 第2次={attempts2}")
        
        # 检查计数
        current_attempts = await auth_service._get_login_attempts(test_email)
        print(f"   当前失败次数: {current_attempts}")
        
        # 重置计数
        await auth_service._reset_login_attempts(test_email)
        reset_attempts = await auth_service._get_login_attempts(test_email)
        print(f"   重置后次数: {reset_attempts}")
        
        print(f"   登录尝试计数测试: {'✅ 成功' if reset_attempts == 0 else '❌ 失败'}")
        
        print("\n2. 测试账号锁定...")
        
        # 测试锁定状态
        is_locked_before = await auth_service._is_account_locked(test_email)
        print(f"   锁定前状态: {'已锁定' if is_locked_before else '未锁定'}")
        
        # 锁定账号
        await auth_service._lock_account(test_email)
        is_locked_after = await auth_service._is_account_locked(test_email)
        print(f"   锁定后状态: {'已锁定' if is_locked_after else '未锁定'}")
        
        # 获取剩余锁定时间
        remaining_time = await auth_service.get_remaining_lockout_time(test_email)
        print(f"   剩余锁定时间: {remaining_time} 秒")
        
        # 手动解锁
        await auth_service.unlock_account(test_email)
        is_locked_final = await auth_service._is_account_locked(test_email)
        print(f"   解锁后状态: {'已锁定' if is_locked_final else '未锁定'}")
        
        print(f"   账号锁定测试: {'✅ 成功' if not is_locked_final else '❌ 失败'}")
        
    except Exception as e:
        print(f"   认证安全系统测试: ❌ 失败 - {e}")
    
    print()


async def main():
    """主测试函数"""
    print("🔧 Redis集成测试开始\n")
    
    try:
        # 基础连接测试
        await test_redis_connection()
        
        # 工具类测试
        await test_redis_utils()
        
        # 认证安全系统测试
        await test_auth_security_redis()
        
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"💥 测试过程中发生错误: {e}")
    
    finally:
        # 关闭Redis连接
        try:
            from core.redis import close_redis
            await close_redis()
            print("\n🔗 Redis连接已关闭")
        except Exception as e:
            print(f"\n❌ 关闭Redis连接时出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
