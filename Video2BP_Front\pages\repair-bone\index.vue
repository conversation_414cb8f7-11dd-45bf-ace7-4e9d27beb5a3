<template>
  <div class="repair-bone-container">
    <div v-if="loading || modeLoading" class="loading-box">
      <Loading overlay size="large" text="加载中..." />
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="player-wrapper">
      <SkeletonPlayerWithBackground v-if="mode === 'kp'" :key="`kp-player-${componentKey}`" ref="skeletonPlayerRef"
        :mappings="mappings" :skeletonData="skeletonData" :width="playerWidth" :height="playerHeight"
        videoSrc="~/assets/videos/demo.mp4" :frameRate="30" :renderOptions="{
          pointColor: '#00ff00',
          lineColor: '#ff00ff',
          pointSize: 5,
          lineWidth: 2,
          offsetX: 0,
          offsetY: 0,
          bboxColor: '#00ffff',
          bboxLineWidth: 2,
          showBbox: true,
        }" @video-loaded="reloadSkeletonDataWithVideoFrames" />
      <SimpleSkeletonPlayer v-else :key="`ik-player-${componentKey}`" ref="skeletonPlayerRef" :mappings="mappings"
        :skeletonData="skeletonData" :width="playerWidth" :height="playerHeight" videoSrc="~/assets/videos/demo.mp4"
        :frameRate="30" :renderOptions="{
          pointColor: '#00ff00',
          lineColor: '#ff00ff',
          pointSize: 5,
          lineWidth: 2,
          offsetX: 0,
          offsetY: 0,
          bboxColor: '#00ffff',
          bboxLineWidth: 2,
          showBbox: true,
        }" />

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import SkeletonPlayerWithBackground from './components/SkeletonPlayerWithBackground.vue'
import SimpleSkeletonPlayer from './components/SimpleSkeletonPlayer.vue'
import Loading from '~/components/common/Loading.vue'
import type { SkeletonFrame } from './hooks/useSkeletonAnimation'
import newData from './newData.json'
import { adaptNewData, generateDynamicMappings, } from './utils/newDataAdapter'
import type { DynamicMappings } from './utils/newDataAdapter'
import { deepClone } from '~/utils'
import { useModeStateCache, type ModeState } from '~/composables/useModeStateCache'

type PlayerInstance = Record<string, any>

const props = defineProps({
  mode: {
    type: String,
    default: 'kp',
  },
})

// 状态缓存管理
const {
  saveCurrentModeState,
  restoreModeState,
  clearModeState,
  hasCachedState,
  getCacheInfo
} = useModeStateCache()

// 响应式数据
const mappings: DynamicMappings = generateDynamicMappings(newData);
const skeletonData = ref<SkeletonFrame[]>([]) // 骨骼数据
const originalSkeletonData = ref<SkeletonFrame[]>([]) // 原始骨骼数据备份
const loading = ref(true) // 加载状态
const modeLoading = ref(false) // 模式切换加载状态
const error = ref<string | null>(null) // 错误信息
const playerWidth = ref(960) // player的宽度
const playerHeight = ref(540) // player的高度
const componentKey = ref(0) // 组件强制重新挂载的key
const skeletonPlayerRef = ref<PlayerInstance | null>(null) // 添加对骨骼播放器组件的引用

// 从子组件导出的状态（只有 SkeletonPlayerWithBackground 支持）
const canSwapLeftRight = computed(() => {
  return skeletonPlayerRef.value?.canSwapLeftRight ?? false
})
const canCopy = computed(() => {
  return skeletonPlayerRef.value?.canCopy ?? false
})
const hasCopiedData = computed(() => {
  return skeletonPlayerRef.value?.hasCopiedData ?? false
})

const hasCurrentFrameData = computed(() => {
  return skeletonPlayerRef.value?.hasCurrentFrameData ?? false
})

// 加载骨骼数据
const loadSkeletonData = async (videoTotalFrames?: number) => {
  try {
    const adaptedData = adaptNewData(newData, mappings, videoTotalFrames) //处理返回的数据
    skeletonData.value = adaptedData;
    // 保存原始数据备份
    originalSkeletonData.value = deepClone(adaptedData)

    if (skeletonData.value.length > 0) {
      //计算player的宽高
      const [height, width] = skeletonData.value[0].image_shape

      const maxWidth = 960
      const maxHeight = 540
      const scale = Math.min(maxWidth / width, maxHeight / height)

      playerWidth.value = Math.floor(width * scale)
      playerHeight.value = Math.floor(height * scale)
    }

    loading.value = false
  } catch (err) {
    console.error('Failed to load skeleton data:', err)
    error.value = '加载骨骼数据失败，请刷新页面重试'
    loading.value = false
  }
}

// 重置骨骼数据到原始状态
const resetSkeletonData = () => {
  if (originalSkeletonData.value.length > 0) {
    skeletonData.value = deepClone(originalSkeletonData.value)
  }
}

// 获取当前模式的完整状态
const getCurrentModeState = (): Partial<ModeState> => {
  console.log(`获取 ${props.mode.toUpperCase()} 模式当前状态...`)

  if (!skeletonPlayerRef.value) {
    console.log('组件引用不存在，返回默认状态')
    return {
      currentFrame: 0,
      isPlaying: false,
      playbackSpeed: 1,
      skeletonData: skeletonData.value,
      videoCurrentTime: 0
    }
  }

  const playerState: Record<string, any> = {}
  const player = skeletonPlayerRef.value

  // 获取基础播放状态
  if (player.currentFrame !== undefined) {
    playerState.currentFrame = player.currentFrame
    console.log('当前帧:', player.currentFrame)
  }

  // 尝试获取播放状态
  if (player.isPlaying !== undefined) {
    playerState.isPlaying = player.isPlaying
    console.log('播放状态:', player.isPlaying)
  }
  if (player.playbackSpeed !== undefined) {
    playerState.playbackSpeed = player.playbackSpeed
    console.log('播放速度:', player.playbackSpeed)
  }

  // 获取视频当前时间
  const videoElement = player.videoRef
  if (videoElement?.currentTime !== undefined) {
    playerState.videoCurrentTime = videoElement.currentTime
    console.log('视频时间:', videoElement.currentTime)
  }

  // 获取KP模式特有状态
  if (props.mode === 'kp') {
    if (player.selectedPoints !== undefined) {
      playerState.selectedPoints = player.selectedPoints
      console.log('选中点数量:', player.selectedPoints?.length || 0)
    }
    if (player.copiedPoints !== undefined) {
      playerState.copiedData = player.copiedPoints
      console.log('复制数据数量:', player.copiedPoints?.length || 0)
    }
    if (player.getSkeletonData) {
      // 获取修改后的骨骼数据
      playerState.skeletonData = player.getSkeletonData()
      console.log('骨骼数据长度:', playerState.skeletonData?.length || 0)
    }
  }

  // 获取IK模式特有状态
  if (props.mode === 'ik') {
    if (player.getFootIKStates) {
      playerState.footIKStates = player.getFootIKStates()
      console.log('IK状态:', playerState.footIKStates)
    }
  }

  const finalState: Record<string, any> = {
    ...playerState,
    skeletonData: playerState.skeletonData || skeletonData.value
  }

  console.log(`${props.mode.toUpperCase()} 模式状态收集完成:`, {
    currentFrame: finalState.currentFrame,
    isPlaying: finalState.isPlaying,
    dataLength: finalState.skeletonData?.length,
    hasSelectedPoints: !!finalState.selectedPoints,
    hasCopiedData: !!finalState.copiedData,
    hasFootIKStates: !!finalState.footIKStates
  })

  return finalState
}

// 恢复模式状态到组件
const restoreStateToComponent = async (state: ModeState) => {
  if (!skeletonPlayerRef.value) {
    console.warn('组件引用不存在，无法恢复状态')
    return
  }

  try {
    console.log(`开始恢复 ${props.mode.toUpperCase()} 模式状态:`, {
      currentFrame: state.currentFrame,
      hasSkeletonData: state.skeletonData?.length > 0,
      hasSelectedPoints: !!state.selectedPoints,
      hasCopiedData: !!state.copiedData,
      hasFootIKStates: !!state.footIKStates
    })

    // 恢复骨骼数据
    if (state.skeletonData && state.skeletonData.length > 0) {
      skeletonData.value = deepClone(state.skeletonData)
      console.log('已恢复骨骼数据，长度:', state.skeletonData.length)
    }

    // 等待组件更新
    await nextTick()

    const player = skeletonPlayerRef.value
    console.log('播放器组件可用方法:', Object.keys(player).filter(key => typeof player[key] === 'function'))

    // 恢复播放状态
    if (state.currentFrame !== undefined) {
      console.log('恢复当前帧到:', state.currentFrame)
      if (player.manualGoToFrame) {
        // KP模式使用manualGoToFrame
        player.manualGoToFrame(state.currentFrame)
      } else if (player.goToFrame) {
        // IK模式使用goToFrame
        player.goToFrame(state.currentFrame)
      }
    }

    // 恢复视频播放时间
    if (state.videoCurrentTime !== undefined) {
      const videoElement = player.$refs?.videoRef || player.videoRef
      if (videoElement) {
        console.log('恢复视频时间到:', state.videoCurrentTime)
        videoElement.currentTime = state.videoCurrentTime
      }
    }

    // 恢复播放速度
    if (state.playbackSpeed !== undefined) {
      const videoElement = player.$refs?.videoRef || player.videoRef
      if (videoElement) {
        console.log('恢复播放速度到:', state.playbackSpeed)
        videoElement.playbackRate = state.playbackSpeed
      }
    }

    // 恢复KP模式特有状态
    if (props.mode === 'kp') {
      if (state.selectedPoints && player.restoreSelectedPoints) {
        console.log('恢复选中点:', state.selectedPoints)
        player.restoreSelectedPoints(state.selectedPoints)
      }
      if (state.copiedData && player.restoreCopiedData) {
        console.log('恢复复制数据:', state.copiedData.length, '个点')
        player.restoreCopiedData(state.copiedData)
      }
    }

    // 恢复IK模式特有状态
    if (props.mode === 'ik' && state.footIKStates && player.restoreFootIKStates) {
      console.log('恢复IK状态:', state.footIKStates)
      player.restoreFootIKStates(state.footIKStates)
    }

    console.log(`已成功恢复 ${props.mode.toUpperCase()} 模式状态到组件`)
  } catch (error) {
    console.error('恢复状态到组件失败:', error)
  }
}

// 当视频加载完成后重新加载骨骼数据
const reloadSkeletonDataWithVideoFrames = (videoTotalFrames: number) => {
  console.log('重新加载骨骼数据，视频总帧数:', videoTotalFrames)
  loadSkeletonData(videoTotalFrames)
}

// 监听模式变化，切换时保存当前状态并恢复目标模式状态
watch(() => props.mode, async (newMode, oldMode) => {
  if (oldMode !== undefined && newMode !== oldMode) {
    console.log(`模式切换: ${oldMode} -> ${newMode}`)

    modeLoading.value = true

    // 1. 保存当前模式状态（在组件销毁前）
    if (oldMode && skeletonPlayerRef.value) {
      const currentState = getCurrentModeState()
      saveCurrentModeState(oldMode as 'kp' | 'ik', currentState)
    }

    // 等待确保loading显示
    await nextTick()

    // 2. 检查是否有目标模式的缓存状态
    const hasCache = hasCachedState(newMode as 'kp' | 'ik')

    if (!hasCache) {
      // 没有缓存，重置到原始数据
      resetSkeletonData()
    }

    // 3. 更新组件key强制重新挂载
    componentKey.value++

    // 4. 等待组件重新挂载
    await nextTick()

    // 5. 恢复目标模式状态（如果有缓存）
    if (hasCache) {
      const cachedState = restoreModeState(newMode as 'kp' | 'ik')
      if (cachedState) {
        // 等待组件引用可用，然后恢复状态
        const waitForComponent = () => {
          if (skeletonPlayerRef.value) {
            restoreStateToComponent(cachedState)
          } else {
            // 如果组件还没准备好，继续等待
            setTimeout(waitForComponent, 50)
          }
        }
        // 延迟开始等待，确保组件开始挂载
        setTimeout(waitForComponent, 100)
      }
    }

    setTimeout(() => {
      modeLoading.value = false
    }, 500)
  }
})

// 保存骨骼数据的方法（只有 SkeletonPlayerWithBackground 支持）
const saveSkeletonData = () => {
  if (!skeletonPlayerRef.value) {
    console.error('骨骼播放器引用不存在');
    return null;
  }

  // 获取完整的骨骼数据（只有 SkeletonPlayerWithBackground 支持）
  const getSkeletonData = skeletonPlayerRef.value.getSkeletonData;
  if (!getSkeletonData) {
    console.warn('当前组件不支持保存骨骼数据');
    return null;
  }
  const data = getSkeletonData();


  return data;
}

const selectNodeGroup = (groupName: string | string[]) => {
  if (!skeletonPlayerRef.value) {
    console.error('骨骼播放器引用不存在');
    return;
  }

  // 只有 SkeletonPlayerWithBackground 支持此方法
  const selectMethod = skeletonPlayerRef.value.selectNodeGroup;
  if (selectMethod) {
    selectMethod(groupName);
  }
}
const swapLeftRightNodes = () => {
  if (!skeletonPlayerRef.value) {
    console.error('骨骼播放器引用不存在');
    return;
  }
  // 只有 SkeletonPlayerWithBackground 支持此方法
  const swapMethod = skeletonPlayerRef.value.swapLeftRightNodes;
  if (swapMethod) {
    swapMethod();
  }
}
const resetAllFrames = () => {
  if (!skeletonPlayerRef.value) {
    console.error('骨骼播放器引用不存在');
    return;
  }
  skeletonPlayerRef.value.resetAllFrames();
}
const resetCurrentFrame = () => {
  if (!skeletonPlayerRef.value) {
    console.error('骨骼播放器引用不存在');
    return;
  }
  skeletonPlayerRef.value.resetCurrentFrame();
}
const clearSelection = () => {
  if (!skeletonPlayerRef.value) {
    console.error('骨骼播放器引用不存在');
    return;
  }
  skeletonPlayerRef.value.clearSelection();
}

const copySelectedPoints = () => {
  if (!skeletonPlayerRef.value) {
    console.error('骨骼播放器引用不存在');
    return;
  }
  // 只有 SkeletonPlayerWithBackground 支持此方法
  const copyMethod = skeletonPlayerRef.value.copySelectedPoints;
  if (copyMethod) {
    copyMethod();
  }
}
const pasteToCurrentFrame = () => {
  if (!skeletonPlayerRef.value) {
    console.error('骨骼播放器引用不存在');
    return;
  }
  // 只有 SkeletonPlayerWithBackground 支持此方法
  const pasteMethod = skeletonPlayerRef.value.pasteToCurrentFrame;
  if (pasteMethod) {
    pasteMethod();
  }
}

// 生命周期
onMounted(async () => {
  await loadSkeletonData()

  // 检查是否有当前模式的缓存状态需要恢复
  if (hasCachedState(props.mode as 'kp' | 'ik')) {
    const cachedState = restoreModeState(props.mode as 'kp' | 'ik')
    if (cachedState) {
      // 延迟恢复状态，确保组件完全初始化
      setTimeout(async () => {
        await restoreStateToComponent(cachedState)
      }, 200)
    }
  }
})

// 获取子组件的 totalFrames
const totalFrames = computed(() => {
  return skeletonPlayerRef.value?.totalFrames ?? 0
})

// 插值函数的包装器（只有 SkeletonPlayerWithBackground 支持）
const handleInterpolation = (startFrameIndex: number, endFrameIndex: number) => {
  if (!skeletonPlayerRef.value) {
    return;
  }
  // 只有 SkeletonPlayerWithBackground 支持此方法
  const interpolationMethod = skeletonPlayerRef.value.handleInterpolation;
  if (interpolationMethod) {
    interpolationMethod(startFrameIndex, endFrameIndex);
  }
}

defineExpose({
  saveSkeletonData,
  selectNodeGroup,
  swapLeftRightNodes,
  clearSelection,
  canSwapLeftRight,
  resetAllFrames,
  resetCurrentFrame,
  canCopy,
  hasCopiedData,
  hasCurrentFrameData,
  copySelectedPoints,
  pasteToCurrentFrame,
  totalFrames,
  handleInterpolation,
  skeletonPlayerRef,
  reloadSkeletonDataWithVideoFrames,
  // 状态管理相关方法
  getCurrentModeState,
  clearModeState,
  getCacheInfo,
})
</script>

<style scoped lang="scss">
.repair-bone-container {
  width: 100%;
  height: 100%;
}

h1 {
  text-align: center;
  margin-bottom: removeEventListener(20);
}

.loading,
.error {
  text-align: center;
  padding: rem(40);
  font-size: rem(18);
}

.error {
  color: #ff0000;
}

.player-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
</style>
