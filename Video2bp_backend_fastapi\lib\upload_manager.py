"""
视频上传进度管理器
"""

import asyncio
import uuid
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta


@dataclass
class FileUploadProgress:
    """单个文件上传进度"""
    filename: str
    progress: int = 0
    status: str = "pending"  # pending, uploading, completed, failed
    video_id: Optional[str] = None
    video_url: Optional[str] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


@dataclass
class BatchUploadTask:
    """批量上传任务"""
    upload_id: str
    user_id: int
    total_files: int
    files: Dict[str, FileUploadProgress] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    
    @property
    def completed_files(self) -> int:
        """已完成文件数"""
        return sum(1 for f in self.files.values() if f.status == "completed")
    
    @property
    def failed_files(self) -> int:
        """失败文件数"""
        return sum(1 for f in self.files.values() if f.status == "failed")
    
    @property
    def overall_progress(self) -> int:
        """总体进度百分比"""
        if not self.files:
            return 0
        total_progress = sum(f.progress for f in self.files.values())
        return min(100, total_progress // len(self.files))
    
    @property
    def is_completed(self) -> bool:
        """是否全部完成"""
        return all(f.status in ["completed", "failed"] for f in self.files.values())


class UploadManager:
    """上传管理器"""
    
    def __init__(self):
        self._tasks: Dict[str, BatchUploadTask] = {}
        self._cleanup_interval = 3600  # 1小时清理一次过期任务
        self._task_expire_time = 24 * 3600  # 任务24小时后过期
        
    def create_batch_upload(self, user_id: int, filenames: List[str]) -> str:
        """创建批量上传任务"""
        upload_id = str(uuid.uuid4())
        task = BatchUploadTask(
            upload_id=upload_id,
            user_id=user_id,
            total_files=len(filenames)
        )
        
        # 初始化文件进度
        for filename in filenames:
            task.files[filename] = FileUploadProgress(filename=filename)
        
        self._tasks[upload_id] = task
        return upload_id
    
    def get_task(self, upload_id: str) -> Optional[BatchUploadTask]:
        """获取上传任务"""
        return self._tasks.get(upload_id)
    
    def update_file_progress(self, upload_id: str, filename: str, progress: int, status: str = "uploading"):
        """更新文件上传进度"""
        task = self._tasks.get(upload_id)
        if task and filename in task.files:
            file_progress = task.files[filename]
            file_progress.progress = progress
            file_progress.status = status
            
            if status == "uploading" and file_progress.start_time is None:
                file_progress.start_time = datetime.now()
            elif status in ["completed", "failed"]:
                file_progress.end_time = datetime.now()
    
    def set_file_result(self, upload_id: str, filename: str, video_id: str = None, video_url: str = None, error: str = None):
        """设置文件上传结果"""
        task = self._tasks.get(upload_id)
        if task and filename in task.files:
            file_progress = task.files[filename]
            if video_id and video_url:
                file_progress.video_id = video_id
                file_progress.video_url = video_url
                file_progress.status = "completed"
                file_progress.progress = 100
            elif error:
                file_progress.error = error
                file_progress.status = "failed"
                file_progress.progress = 0
            
            file_progress.end_time = datetime.now()
    
    def cleanup_expired_tasks(self):
        """清理过期任务"""
        now = datetime.now()
        expired_tasks = []
        
        for upload_id, task in self._tasks.items():
            if (now - task.created_at).total_seconds() > self._task_expire_time:
                expired_tasks.append(upload_id)
        
        for upload_id in expired_tasks:
            del self._tasks[upload_id]
    
    def get_user_tasks(self, user_id: int) -> List[BatchUploadTask]:
        """获取用户的所有上传任务"""
        return [task for task in self._tasks.values() if task.user_id == user_id]


# 全局上传管理器实例
upload_manager = UploadManager()


async def cleanup_task():
    """定期清理过期任务的后台任务"""
    while True:
        try:
            upload_manager.cleanup_expired_tasks()
            await asyncio.sleep(3600)  # 每小时清理一次
        except Exception as e:
            print(f"清理上传任务时出错: {e}")
            await asyncio.sleep(60)  # 出错后1分钟后重试
