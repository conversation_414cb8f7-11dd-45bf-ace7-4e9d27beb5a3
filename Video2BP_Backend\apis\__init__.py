import importlib
import pkgutil

def init_app(app):
    """
    Dynamically register all blueprints from the current package.

    :param app: Flask application instance
    """
    # Automatically discover and register blueprints
    package_name = __name__
    for _, module_name, is_pkg in pkgutil.iter_modules(__path__):
        if not is_pkg:  # Ignore sub-packages
            try:
                module = importlib.import_module(f"{package_name}.{module_name}")
                if hasattr(module, "blueprint"):
                    app.register_blueprint(module.blueprint)
            except Exception as e:
                app.logger.error(f"Failed to register blueprint from module {module_name}: {e}")
