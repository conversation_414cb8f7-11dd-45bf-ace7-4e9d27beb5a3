from flask import Blueprint
from flask import request, send_file

from service import video_manage_service
from urls import URLs
from utils.R import R
from utils.exceptions import ServerError
from utils.publicMethod import validate_pagination_params
from utils.publicMethod import token_required

blueprint = Blueprint("video_manage", __name__)

from flask import g


@blueprint.route(URLs.user_video_upload.url, methods=URLs.user_video_upload.methods)
@token_required
def video_upload():
    file = request.files.get("fileData")
    if not file:
        return R.error(ServerError.INVALID_PARAMS)
    
    user_id = g.user_id
    
    result = video_manage_service.upload_video(int(user_id), file)
    return R.success(data=result)


@blueprint.route(URLs.user_video_list.url, methods=URLs.user_video_list.methods)
@token_required
def video_list():
    j_data = request.json
    start_page, page_size = validate_pagination_params(j_data)
    user_id = g.user_id

    videos = video_manage_service.get_video_list(int(user_id), start_page, page_size)
    return R.success(data=videos)


@blueprint.route(URLs.user_video_download.url, methods=URLs.user_video_download.methods)
@token_required
def video_download():
    a_data = request.args
    video_id = a_data.get("videoId")
    user_id = g.user_id
    file_path = video_manage_service.get_video_path(int(user_id), int(video_id))
    return send_file(file_path, as_attachment=True)


@blueprint.route(URLs.user_video_bp_download.url, methods=URLs.user_video_bp_download.methods)
@token_required
def bp_download():
    a_data = request.args
    video_id = a_data.get("videoId")
    user_id = g.user_id
    file_path = video_manage_service.get_bp_path(int(user_id), int(video_id))
    return send_file(file_path, as_attachment=True)


@blueprint.route(URLs.user_video_download_all.url, methods=URLs.user_video_download_all.methods)
@token_required
def download_all():
    a_data = request.args
    video_id = a_data.get("videoId")
    user_id = g.user_id
    memory_file = video_manage_service.get_video_all(int(user_id), int(video_id))
    return send_file(
        memory_file,
        mimetype="application/zip",
        as_attachment=True,
        download_name=f"video_{video_id}_package.zip"
    )


@blueprint.route(URLs.user_video_upload_chunk.url, methods=URLs.user_video_upload_chunk.methods)
@token_required
def upload_chunk():
    chunk = request.files.get("chunk")
    chunk_number = int(request.form.get("chunkNumber"))
    total_chunks = int(request.form.get("totalChunks"))
    file_id = request.form.get("fileId")
    user_id = g.user_id
    
    result = video_manage_service.save_chunk(
        user_id=int(user_id),
        file_id=file_id,
        chunk=chunk,
        chunk_number=chunk_number,
        total_chunks=total_chunks
    )
    return R.success(data=result)



@blueprint.route(URLs.user_video_merge_chunk.url, methods=URLs.user_video_merge_chunk.methods)
@token_required
def merge_chunks():
    file_id = request.form.get("fileId")
    filename = request.form.get("filename")
    user_id = g.user_id

    result = video_manage_service.merge_chunks(
        user_id=int(user_id),
        file_id=file_id,
        filename=filename
    )
    return R.success(data=result)


@blueprint.route(URLs.user_video_upload_status.url, methods=URLs.user_video_upload_status.methods)
@token_required
def video_upload_status():
    file_id = request.args.get("fileId")
    user_id = g.user_id
    
    if not file_id:
        return R.error(ServerError.INVALID_PARAMS)
    
    result = video_manage_service.get_upload_status(
        user_id=int(user_id),
        file_id=file_id
    )
    return R.success(data=result)
