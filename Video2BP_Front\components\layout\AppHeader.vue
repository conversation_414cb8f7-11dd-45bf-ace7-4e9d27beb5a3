<template>
    <div class="head-menu" :style="style">
        <div class="left-area" @click="navigateTo('/home')">
            <slot name="left">
                <img src="~/assets/images/lingyuimage&logo.webp" alt="Logo" class="logo">
            </slot>
        </div>

        <div class="center-area">
            <slot />
        </div>

        <div class="right-area">
            <slot name="right">
                <img src="~/assets/images/menu-list.webp" alt="Logo" class="logo" @click="showDrawer">
                <img v-if="useAuthStore().isAuthenticated" src="~/assets/images/person.webp" alt="Logo" class="person"
                    @click="toggleUserMenu">
                <div v-if="showUserMenu" class="user-dropdown">
                    <div v-for="(item, index) in userMenuItems" :key="index" class="dropdown-item"
                        @click="handleUserMenuClick(item)">
                        {{ item.name }}
                    </div>
                </div>
            </slot>
        </div>

        <a-drawer v-model:open="open" class="custom-draw-class" root-class-name="root-class-name" placement="right"
            width="50%" :closable="false" :mask-style="styleObject" @after-open-change="afterOpenChange">
            <div class="menu-close" @click="open = false">
                <img src="~/assets/images/menu-close.webp" alt="">
            </div>
            <div class="menu-list">
                <div v-for="item in menuList" :key="item.name" class="menu-item" @click="jump(item)">
                    <div class="menu-item-name">{{ item.name }}<span /></div>
                    <div class="menu-item-arrow">
                        <img src="~/assets/images/menu-arrow.webp" alt="SSD">
                    </div>
                </div>
            </div>
            <div class="user-login" v-if="!useAuthStore().isAuthenticated" @click="navigateTo('/auth')">
                <span>
                    <UserOutlined />
                </span>
                <span>用户登录</span>
            </div>
        </a-drawer>
    </div>
</template>

<script setup lang="ts">
import { UserOutlined } from '@ant-design/icons-vue'
defineOptions({
    name: 'HeadMenu',
})
defineProps({
    style: {
        type: Object,
        default: () => ({}),
    },
})
interface Menu {
    name: string
    path: string
    url: string
    tips: string
}

interface UserMenuItem {
    name: string
    path: string
}
const authStore = useAuthStore();

const styleObject = reactive({
    // backgroundImage: "url('/srassets/images/menu-bg.webp')",
    // backgroundSize: '100% 100%',
})

const menuList = ref<Menu[]>([
    {
        name: '首页',
        path: '/home',
        url: '',
        tips: 'Home',
    },
    {
        name: '商业联系',
        path: '/business',
        url: '',
        tips: 'Contact',
    },
    {
        name: '产品文档',
        path: '/product-docs',
        url: '',
        tips: 'Document',
    },
    {
        name: '升级套餐 ',
        path: '/upgrade',
        url: '',
        tips: 'Package',
    },
    {
        name: '公司动态',
        path: '/company-news',
        url: '',
        tips: 'Dynamic',
    },
])

const open = ref<boolean>(false)
const afterOpenChange = (bool: boolean) => {
    console.log('open', bool)
}

const jump = (item: Menu) => {
    console.log('item', item)
    if (item.path) {
        navigateTo(item.path)
    } else {
        // 在新窗口打开外部链接
        navigateTo(item.url, {
            external: true, open: {
                target: '_blank'
            }
        })
    }
    // 关闭drawer
    open.value = false
}

const showDrawer = () => {
    open.value = true
}

// 用户菜单状态和选项
const showUserMenu = ref(false)
const userMenuItems = ref<UserMenuItem[]>([
    { name: '资源广场', path: '/resource' },
    { name: '我的项目', path: '/my-project' },
    { name: '视频动捕', path: '/capture' },
    { name: '用户中心', path: '/user' },
    { name: '退出登录', path: '/logout' }
])

// 切换用户菜单显示状态
const toggleUserMenu = () => {
    showUserMenu.value = !showUserMenu.value
}

// 处理用户菜单点击
const handleUserMenuClick = async (item: UserMenuItem) => {
    if (item.name === '退出登录') {
        // 处理登出逻辑
        const result = await authStore.logout()
        if (result) {
            return navigateTo('/auth')
        }
    } else {
        navigateTo(item.path)
    }
    showUserMenu.value = false
}

// 处理点击外部区域隐藏下拉框
const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as HTMLElement

    // 检查点击的元素是否是 person 元素或其子元素
    if (!target.closest('.person') && !target.closest('.user-dropdown')) {
        showUserMenu.value = false
    }
}

onMounted(() => {
    document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss">
.custom-draw-class {
    .ant-drawer-header {
        display: none;
    }

    .ant-drawer-body {
        padding: 0;
        position: relative;
        padding-top: rem(241);
        background-color: #000;
        background-image: url('~/assets/images/menu-bg.webp');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 100vh;
    }
}
</style>

<style lang="scss" scoped>
.menu-close {
    position: absolute;
    top: rem(68);
    right: rem(120);
    font-size: rem(24);
    cursor: pointer;
    color: white;

    img {
        width: rem(80);
        height: rem(80);
    }
}

.menu-list {
    display: flex;
    flex-direction: column;
    gap: rem(22);

    .menu-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-weight: bold;
        font-size: rem(72);
        color: #ffffff;
        line-height: rem(104);
        text-align: left;
        font-style: normal;
        cursor: pointer;
        padding: 0 rem(120);

        .menu-item-arrow {
            img {
                display: none;
                width: rem(28);
                height: rem(28);
            }
        }

        .menu-item-name {
            display: flex;

            span {
                font-family: YouSheBiaoTiHei;
                font-size: rem(72);
                transform: translateY(-50%);
                color: rgba(255, 255, 255, 0.1);
                font-size: rem(40);
                font-weight: bold;
                z-index: 1;
                color: white;
            }
        }

        &:hover {
            transition: all 0.3s ease;
            text-shadow: 0px 2px 20px rgba(255, 255, 255, 0.3);
            background: linear-gradient(to bottom, #0091ff 0%, #b620e0 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;

            .menu-item-arrow {
                img {
                    display: block;
                }
            }
        }
    }
}

.user-login {
    font-family: SourceHanSerifSC, SourceHanSerifSC;
    font-weight: bold;
    font-size: rem(32);
    color: #ffffff;
    line-height: rem(46);
    text-align: left;
    font-style: normal;
    position: absolute;
    left: rem(120);
    bottom: rem(67);
    cursor: pointer;

    span {
        &:first-child {
            margin-right: rem(5);
        }
    }
}

.head-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: transparent;
    // background-color: pink;
    color: #fff;
    width: 87.5%;
    position: absolute;
    top: rem(68);
    left: 6.25%;
    z-index: 1000;
}

.left-area,
.right-area {
    display: flex;
    align-items: center;
    min-width: rem(40);
    cursor: pointer;

    img {
        width: rem(80);
    }
}

.center-area {
    flex: 1;
    display: flex;
    justify-content: center;
    overflow: hidden;
}

.person {
    cursor: pointer;
}

.user-dropdown {
    position: absolute;
    top: rem(90);
    right: 0;
    width: rem(200);
    background-color: #2a2844;
    border-radius: rem(8);
    box-shadow: 0 rem(8) rem(16) rgba(0, 0, 0, 0.3);
    z-index: 1000;
    overflow: hidden;

    .dropdown-item {
        padding: rem(15) rem(20);
        font-family: SourceHanSerifSC, SourceHanSerifSC;
        font-size: rem(16);
        color: #ffffff;
        cursor: pointer;
        // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;

        &::after {
            content: '';
            width: calc(100% - rem(40));
            height: rem(1);
            background-color: rgba(255, 255, 255, 0.2);
            position: absolute;
            bottom: 0;
            left: rem(20);
        }

        &:last-child {
            border-bottom: none;

            &::after {
                height: 0;
            }
        }

        &:hover {
            color: #0091ff;
        }
    }
}
</style>
