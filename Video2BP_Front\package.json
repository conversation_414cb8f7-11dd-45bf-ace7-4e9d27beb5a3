{"name": "lingyu-nuxt", "version": "0.1.0", "description": "灵宇科技", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --fix", "format": "prettier --write ."}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@nuxt/eslint": "1.4.1", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.13.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.7", "@nuxt/ui": "3.1.3", "@pinia-plugin-persistedstate/nuxt": "^1.2.1", "@pinia/nuxt": "^0.11.0", "@unhead/vue": "^2.0.10", "axios": "^1.9.0", "dotenv": "^16.5.0", "eslint": "^9.28.0", "nuxt": "^3.17.5", "pinia": "^3.0.1", "serve": "14.2.4", "swiper": "^11.0.5", "three": "0.140.2", "typescript": "^5.8.3", "vue": "^3.5.16", "vue-3d-loader": "^2.2.4", "vue-router": "^4.5.1"}, "devDependencies": {"@ant-design-vue/nuxt": "^1.4.6", "@ant-design/icons-vue": "^7.0.1", "@types/node": "^22.14.0", "@types/three": "^0.143.2", "less": "^4.3.0", "postcss": "^8.5.4", "postcss-clamp": "^4.1.0", "postcss-preset-env": "^10.2.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "3.5.3", "sass-embedded": "^1.89.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0"}}