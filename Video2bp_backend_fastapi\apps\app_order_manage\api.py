from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_async_db
from lib.cookie_auth import get_current_user_from_cookie
from apps.app_user.model import User
from schemas.response import StandardResponse


router = APIRouter()


@router.post(
    "/create",
    name="用户下单",
    response_model=StandardResponse,
)
async def order_create(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """创建订单"""
    # 这里只是一个占位符实现，实际需要根据业务需求实现
    return StandardResponse()
