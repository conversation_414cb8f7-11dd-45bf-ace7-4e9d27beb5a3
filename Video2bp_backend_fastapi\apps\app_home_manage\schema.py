from typing import Optional, List
from pydantic import BaseModel, Field
from schemas.base import BaseSchema
from schemas.response import StandardResponse


class CompanyInfoResponse(BaseSchema):
    """公司信息响应"""
    name: str = Field(..., description="公司名称")
    description: str = Field(..., description="公司描述")
    address: str = Field(..., description="公司地址")
    phone: str = Field(..., description="联系电话")
    email: str = Field(..., description="联系邮箱")


class CompanyInfoResponseModel(StandardResponse):
    """公司信息响应模型"""
    data: Optional[CompanyInfoResponse] = None


class ComboInfo(BaseSchema):
    """套餐信息"""
    id: int = Field(..., description="套餐ID")
    name: str = Field(..., description="套餐名称")
    price: float = Field(..., description="价格")
    description: str = Field(..., description="套餐描述")
    features: List[str] = Field(..., description="功能特性")


class ComboInfoResponse(BaseSchema):
    """套餐信息响应"""
    combos: List[ComboInfo] = Field(..., description="套餐列表")


class ComboInfoResponseModel(StandardResponse):
    """套餐信息响应模型"""
    data: Optional[ComboInfoResponse] = None


class NewsListRequest(BaseSchema):
    """新闻列表请求"""
    page: int = Field(1, description="页码")
    size: int = Field(9, description="每页数量")
    type: Optional[int] = Field(None, description="新闻类型")


class NewsItem(BaseSchema):
    """新闻项"""
    id: int = Field(..., description="新闻ID")
    title: str = Field(..., description="标题")
    cover: str = Field(..., description="封面图片")
    author: str = Field(..., description="作者")
    content: str = Field(..., description="内容")
    excerpt: str = Field(..., description="摘要")
    createdAt: str = Field(..., description="创建时间", alias="created_at")
    isPinned: bool = Field(..., description="是否置顶", alias="is_pinned")
    profilePicture: str = Field(..., description="作者头像", alias="profile_picture")
    tag: Optional[str] = Field(None, description="标签")
    type: int = Field(..., description="类型")


class NewsListResponse(BaseSchema):
    """新闻列表响应"""
    total: int = Field(..., description="总数")
    newsList: List[NewsItem] = Field(..., description="新闻列表", alias="news_list")


class NewsListResponseModel(StandardResponse):
    """新闻列表响应模型"""
    data: Optional[NewsListResponse] = None


class NewsDetailResponse(BaseSchema):
    """新闻详情响应"""
    id: int = Field(..., description="新闻ID")
    title: str = Field(..., description="标题")
    cover: str = Field(..., description="封面图片")
    author: str = Field(..., description="作者")
    content: str = Field(..., description="内容")
    excerpt: str = Field(..., description="摘要")
    createdAt: str = Field(..., description="创建时间", alias="created_at")
    isPinned: bool = Field(..., description="是否置顶", alias="is_pinned")
    profilePicture: str = Field(..., description="作者头像", alias="profile_picture")
    tag: Optional[str] = Field(None, description="标签")
    type: int = Field(..., description="类型")


class NewsDetailResponseModel(StandardResponse):
    """新闻详情响应模型"""
    data: Optional[NewsDetailResponse] = None
