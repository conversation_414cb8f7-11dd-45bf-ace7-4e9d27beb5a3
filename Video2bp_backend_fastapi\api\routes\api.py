from fastapi import APIRouter

from api.routes.authentication import router as authentication_router
from apps.app_user.api import router as user_router
from apps.app_user_auth.api import router as user_auth_router
from apps.app_video_manage.api import router as video_manage_router
from apps.app_home_manage.api import router as home_manage_router
from apps.app_order_manage.api import router as order_manage_router
from apps.app_monitor.api import router as monitor_router
from apps.app_business.api import router as business_router
from apps.app_projects.api import router as projects_router
from apps.app_resources.api import router as resources_router

from fastapi import Depends, Query
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from db.database import get_async_db
from lib.cookie_auth import get_current_user_from_cookie
from apps.app_user.model import User
from apps.app_video_manage.service import video_manage_service
from schemas.response import StandardResponse

bp_router = APIRouter()

@bp_router.get(
    "/download",
    name="骨骼点下载",
)
async def bp_download(
    videoId: int = Query(..., description="视频ID"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user_from_cookie),
):
    """骨骼点文件下载"""
    try:
        file_path = await video_manage_service.get_bp_path(db, current_user.id, videoId)
        return FileResponse(
            path=file_path,
            media_type='application/json',
            filename=f"video_{videoId}_bp.json"
        )
    except Exception as e:
        return StandardResponse(
            code=500,
            message=str(e),
        ).to_json(status_code=500)


router = APIRouter()
router.include_router(authentication_router, tags=["用户认证"], prefix="/auth")
router.include_router(user_router, tags=["用户类"], prefix="/users")
router.include_router(user_auth_router, tags=["用户认证管理"], prefix="/user")
router.include_router(video_manage_router, tags=["视频管理"], prefix="/video")
router.include_router(bp_router, tags=["骨骼点管理"], prefix="/bp")
router.include_router(business_router, tags=["业务管理"], prefix="/business")
router.include_router(projects_router, tags=["项目管理"], prefix="/projects")
router.include_router(resources_router, tags=["资源管理"], prefix="/resources")
router.include_router(home_manage_router, tags=["首页管理"], prefix="")
router.include_router(order_manage_router, tags=["订单管理"], prefix="/order")
router.include_router(monitor_router, tags=["系统监控"], prefix="")
