from fastapi import APIRouter, Body, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from apps.app_home_manage.schema import (
    CompanyInfoResponseModel,
    ComboInfoResponseModel,
    NewsListRequest,
    NewsListResponseModel,
    NewsDetailResponseModel,
)
from apps.app_home_manage.service import home_manage_service
from core.e import ErrorCode, ErrorMessage
from db.database import get_async_db
from schemas.response import StandardResponse


router = APIRouter()


@router.get(
    "/company/info",
    name="公司信息",
    response_model=CompanyInfoResponseModel,
)
async def company_info():
    """获取公司信息"""
    try:
        result = home_manage_service.get_company_info()
        return CompanyInfoResponseModel(data=result)
    except Exception as e:
        return CompanyInfoResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.get(
    "/combo/info",
    name="套餐信息",
    response_model=ComboInfoResponseModel,
)
async def combo_info():
    """获取套餐信息"""
    try:
        result = home_manage_service.get_combo_info()
        return ComboInfoResponseModel(data=result)
    except Exception as e:
        return ComboInfoResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)


@router.post(
    "/news/list",
    name="资讯信息",
    response_model=NewsListResponseModel,
)
async def news_list(
    request: NewsListRequest = Body(...),
    db: AsyncSession = Depends(get_async_db),
):
    """获取新闻列表"""
    try:
        # 验证请求参数
        if request.page < 1:
            return NewsListResponseModel(
                code=400,
                message="请求参数错误",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)

        if request.size < 1:
            return NewsListResponseModel(
                code=400,
                message="请求参数错误",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)

        result = await home_manage_service.get_news_list(
            db, request.page, request.size, request.type
        )
        return NewsListResponseModel(
            code=0,
            message="success",
            data=result
        )
    except HTTPException as e:
        error_detail = e.detail
        if "未授权" in error_detail or "需要登录" in error_detail:
            return NewsListResponseModel(
                code=401,
                message="未授权，需要登录",
            ).to_json(status_code=HTTP_401_UNAUTHORIZED)
        else:
            return NewsListResponseModel(
                code=400,
                message="请求参数错误",
            ).to_json(status_code=HTTP_400_BAD_REQUEST)
    except Exception as e:
        return NewsListResponseModel(
            code=500,
            message="服务器内部错误",
        ).to_json(status_code=HTTP_500_INTERNAL_SERVER_ERROR)


@router.get(
    "/news/detail",
    name="资讯详情信息",
    response_model=NewsDetailResponseModel,
)
async def news_detail(
    id: int = Query(..., description="新闻ID"),
    db: AsyncSession = Depends(get_async_db),
):
    """获取新闻详情"""
    try:
        result = await home_manage_service.get_news_detail(db, id)
        return NewsDetailResponseModel(data=result)
    except HTTPException as e:
        return NewsDetailResponseModel(
            code=int(e.detail.split("_")[0]),
            message=e.detail.split("_", 1)[1],
        ).to_json(status_code=e.status_code)
    except Exception as e:
        return NewsDetailResponseModel(
            code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=str(e),
        ).to_json(status_code=HTTP_400_BAD_REQUEST)
