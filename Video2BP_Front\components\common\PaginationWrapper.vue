<template>
  <div v-if="total > 0" class="pagination-wrapper">
    <a-pagination v-model:current="currentPage" v-model:page-size="pageSize" :total="total"
      :show-size-changer="showSizeChanger" :show-total="showTotal ? (total, range) => `共 ${total} 条记录` : undefined"
      @change="handlePageChange" />
  </div>
</template>

<script setup lang="ts">
interface Props {
  total: number
  current: number
  pageSize: number
  showSizeChanger?: boolean
  showTotal?: boolean
}

interface Emits {
  (e: 'change', page: number): void
}

const props = withDefaults(defineProps<Props>(), {
  showSizeChanger: false,
  showTotal: true,
})

const emit = defineEmits<Emits>()

// 双向绑定当前页码
const currentPage = computed({
  get: () => props.current,
  set: (value) => emit('change', value)
})

// 双向绑定页面大小
const pageSize = computed(() => props.pageSize)

// 处理页码变化
const handlePageChange = (page: number) => {
  emit('change', page)
}
</script>

<style lang="scss" scoped>
.pagination-wrapper {
  @include pagination-wrapper;
  @include pagination-styles;
}
</style>
