import { defineStore } from 'pinia';
import { useApi } from '~/composables/useApi';
import { useUserStore } from './user';

export const useAuthStore = defineStore('auth', () => {
	// 状态
	const authError = ref<string | null>(null);
	const isLoading = ref(false);
	const { auth } = useApi();

	// 使用useCookie获取认证状态
	const authCookie = useCookie('auth_status');

	// 计算属性：从cookie获取认证状态
	const isAuthenticated = computed(
		() => authCookie.value === 'authenticated'
	);

	function setAuthError(error: string | null) {
		authError.value = error;
	}

	function setLoading(loading: boolean) {
		isLoading.value = loading;
	}

	async function login(email: string, password: string) {
		try {
			setLoading(true);
			setAuthError(null);

			await auth?.login({
				email,
				password,
			});
			useUserStore()?.fetchUserInfo();
			return true;
		} catch (error) {
			setAuthError(error as string);
			return false;
		} finally {
			setLoading(false);
		}
	}

	async function logout() {
		try {
			await auth?.logout();
			useUserStore()?.clearUserInfo();

			return true;
		} catch (error) {
			console.error('登出失败', error);
			return false;
		}
	}

	return {
		isAuthenticated,
		authError,
		isLoading,
		login,
		logout,
		setAuthError,
		setLoading,
	};
});
